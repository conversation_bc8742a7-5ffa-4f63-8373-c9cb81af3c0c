{"ast": null, "code": "/**\n * Utility functions for managing favicon dynamically\n */\n\n/**\n * Updates the favicon in the document head\n * @param {string} faviconUrl - The URL of the new favicon\n */\nexport const updateFavicon = faviconUrl => {\n  try {\n    // Remove existing favicon links\n    const existingFavicons = document.querySelectorAll('link[rel*=\"icon\"]');\n    existingFavicons.forEach(link => link.remove());\n\n    // Create new favicon link\n    const link = document.createElement('link');\n    link.rel = 'icon';\n    link.type = 'image/x-icon';\n    link.href = faviconUrl;\n\n    // Add to document head\n    document.head.appendChild(link);\n\n    // Also update apple-touch-icon if it exists\n    const appleTouchIcon = document.querySelector('link[rel=\"apple-touch-icon\"]');\n    if (appleTouchIcon && faviconUrl.endsWith('.png')) {\n      appleTouchIcon.href = faviconUrl;\n    }\n    console.log('Favicon updated successfully:', faviconUrl);\n    return true;\n  } catch (error) {\n    console.error('Error updating favicon:', error);\n    return false;\n  }\n};\n\n/**\n * Fetches the current favicon from the server and updates it\n */\nexport const refreshFavicon = async () => {\n  try {\n    const response = await fetch('/backend/handlers/get_favicon.php');\n    const data = await response.json();\n    if (data.success && data.favicon_url) {\n      updateFavicon(data.favicon_url);\n      return data.favicon_url;\n    } else if (data.public_favicon_exists) {\n      updateFavicon('/favicon.ico');\n      return '/favicon.ico';\n    }\n    return null;\n  } catch (error) {\n    console.error('Error refreshing favicon:', error);\n    return null;\n  }\n};\n\n/**\n * Preloads a favicon to ensure it's cached\n * @param {string} faviconUrl - The URL of the favicon to preload\n */\nexport const preloadFavicon = faviconUrl => {\n  const link = document.createElement('link');\n  link.rel = 'preload';\n  link.as = 'image';\n  link.href = faviconUrl;\n  document.head.appendChild(link);\n};\n\n/**\n * Validates if a file is a valid favicon format\n * @param {File} file - The file to validate\n * @returns {Object} - Validation result with success and message\n */\nexport const validateFaviconFile = file => {\n  const allowedTypes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/ico', 'image/icon', 'image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];\n  const allowedExtensions = ['ico', 'png', 'jpg', 'jpeg', 'svg'];\n  const fileExtension = file.name.split('.').pop().toLowerCase();\n\n  // Check file type\n  if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {\n    return {\n      success: false,\n      message: 'Please select a valid favicon file (ICO, PNG, JPG, or SVG)'\n    };\n  }\n\n  // Check file size (max 2MB)\n  if (file.size > 2 * 1024 * 1024) {\n    return {\n      success: false,\n      message: 'Favicon file size must be less than 2MB'\n    };\n  }\n\n  // Check dimensions for optimal favicon (optional warning)\n  return new Promise(resolve => {\n    const img = new Image();\n    img.onload = function () {\n      const optimalSizes = [16, 32, 48, 64];\n      const isOptimalSize = optimalSizes.includes(this.width) && this.width === this.height;\n      resolve({\n        success: true,\n        message: isOptimalSize ? 'Valid favicon file' : 'File is valid, but optimal favicon sizes are 16x16, 32x32, or 48x48 pixels',\n        isOptimalSize\n      });\n    };\n    img.onerror = function () {\n      resolve({\n        success: true,\n        message: 'Valid favicon file (unable to check dimensions)'\n      });\n    };\n    img.src = URL.createObjectURL(file);\n  });\n};\n\n/**\n * Gets the current favicon URL from the document\n * @returns {string|null} - The current favicon URL or null if not found\n */\nexport const getCurrentFaviconUrl = () => {\n  const faviconLink = document.querySelector('link[rel*=\"icon\"]');\n  return faviconLink ? faviconLink.href : null;\n};", "map": {"version": 3, "names": ["updateFavicon", "faviconUrl", "existingFavicons", "document", "querySelectorAll", "for<PERSON>ach", "link", "remove", "createElement", "rel", "type", "href", "head", "append<PERSON><PERSON><PERSON>", "appleTouchIcon", "querySelector", "endsWith", "console", "log", "error", "refreshFavicon", "response", "fetch", "data", "json", "success", "favicon_url", "public_favicon_exists", "preloadFavicon", "as", "validateFaviconFile", "file", "allowedTypes", "allowedExtensions", "fileExtension", "name", "split", "pop", "toLowerCase", "includes", "message", "size", "Promise", "resolve", "img", "Image", "onload", "optimalSizes", "isOptimalSize", "width", "height", "onerror", "src", "URL", "createObjectURL", "getCurrentFaviconUrl", "faviconLink"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/utils/faviconUtils.js"], "sourcesContent": ["/**\n * Utility functions for managing favicon dynamically\n */\n\n/**\n * Updates the favicon in the document head\n * @param {string} faviconUrl - The URL of the new favicon\n */\nexport const updateFavicon = (faviconUrl) => {\n    try {\n        // Remove existing favicon links\n        const existingFavicons = document.querySelectorAll('link[rel*=\"icon\"]');\n        existingFavicons.forEach(link => link.remove());\n\n        // Create new favicon link\n        const link = document.createElement('link');\n        link.rel = 'icon';\n        link.type = 'image/x-icon';\n        link.href = faviconUrl;\n        \n        // Add to document head\n        document.head.appendChild(link);\n\n        // Also update apple-touch-icon if it exists\n        const appleTouchIcon = document.querySelector('link[rel=\"apple-touch-icon\"]');\n        if (appleTouchIcon && faviconUrl.endsWith('.png')) {\n            appleTouchIcon.href = faviconUrl;\n        }\n\n        console.log('Favicon updated successfully:', faviconUrl);\n        return true;\n    } catch (error) {\n        console.error('Error updating favicon:', error);\n        return false;\n    }\n};\n\n/**\n * Fetches the current favicon from the server and updates it\n */\nexport const refreshFavicon = async () => {\n    try {\n        const response = await fetch('/backend/handlers/get_favicon.php');\n        const data = await response.json();\n        \n        if (data.success && data.favicon_url) {\n            updateFavicon(data.favicon_url);\n            return data.favicon_url;\n        } else if (data.public_favicon_exists) {\n            updateFavicon('/favicon.ico');\n            return '/favicon.ico';\n        }\n        \n        return null;\n    } catch (error) {\n        console.error('Error refreshing favicon:', error);\n        return null;\n    }\n};\n\n/**\n * Preloads a favicon to ensure it's cached\n * @param {string} faviconUrl - The URL of the favicon to preload\n */\nexport const preloadFavicon = (faviconUrl) => {\n    const link = document.createElement('link');\n    link.rel = 'preload';\n    link.as = 'image';\n    link.href = faviconUrl;\n    document.head.appendChild(link);\n};\n\n/**\n * Validates if a file is a valid favicon format\n * @param {File} file - The file to validate\n * @returns {Object} - Validation result with success and message\n */\nexport const validateFaviconFile = (file) => {\n    const allowedTypes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/ico', 'image/icon', 'image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];\n    const allowedExtensions = ['ico', 'png', 'jpg', 'jpeg', 'svg'];\n    const fileExtension = file.name.split('.').pop().toLowerCase();\n    \n    // Check file type\n    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {\n        return {\n            success: false,\n            message: 'Please select a valid favicon file (ICO, PNG, JPG, or SVG)'\n        };\n    }\n    \n    // Check file size (max 2MB)\n    if (file.size > 2 * 1024 * 1024) {\n        return {\n            success: false,\n            message: 'Favicon file size must be less than 2MB'\n        };\n    }\n    \n    // Check dimensions for optimal favicon (optional warning)\n    return new Promise((resolve) => {\n        const img = new Image();\n        img.onload = function() {\n            const optimalSizes = [16, 32, 48, 64];\n            const isOptimalSize = optimalSizes.includes(this.width) && this.width === this.height;\n            \n            resolve({\n                success: true,\n                message: isOptimalSize ? 'Valid favicon file' : 'File is valid, but optimal favicon sizes are 16x16, 32x32, or 48x48 pixels',\n                isOptimalSize\n            });\n        };\n        img.onerror = function() {\n            resolve({\n                success: true,\n                message: 'Valid favicon file (unable to check dimensions)'\n            });\n        };\n        img.src = URL.createObjectURL(file);\n    });\n};\n\n/**\n * Gets the current favicon URL from the document\n * @returns {string|null} - The current favicon URL or null if not found\n */\nexport const getCurrentFaviconUrl = () => {\n    const faviconLink = document.querySelector('link[rel*=\"icon\"]');\n    return faviconLink ? faviconLink.href : null;\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMA,aAAa,GAAIC,UAAU,IAAK;EACzC,IAAI;IACA;IACA,MAAMC,gBAAgB,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,mBAAmB,CAAC;IACvEF,gBAAgB,CAACG,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;;IAE/C;IACA,MAAMD,IAAI,GAAGH,QAAQ,CAACK,aAAa,CAAC,MAAM,CAAC;IAC3CF,IAAI,CAACG,GAAG,GAAG,MAAM;IACjBH,IAAI,CAACI,IAAI,GAAG,cAAc;IAC1BJ,IAAI,CAACK,IAAI,GAAGV,UAAU;;IAEtB;IACAE,QAAQ,CAACS,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;;IAE/B;IACA,MAAMQ,cAAc,GAAGX,QAAQ,CAACY,aAAa,CAAC,8BAA8B,CAAC;IAC7E,IAAID,cAAc,IAAIb,UAAU,CAACe,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC/CF,cAAc,CAACH,IAAI,GAAGV,UAAU;IACpC;IAEAgB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEjB,UAAU,CAAC;IACxD,OAAO,IAAI;EACf,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACZF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,OAAO,KAAK;EAChB;AACJ,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;EACtC,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,mCAAmC,CAAC;IACjE,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACG,WAAW,EAAE;MAClC1B,aAAa,CAACuB,IAAI,CAACG,WAAW,CAAC;MAC/B,OAAOH,IAAI,CAACG,WAAW;IAC3B,CAAC,MAAM,IAAIH,IAAI,CAACI,qBAAqB,EAAE;MACnC3B,aAAa,CAAC,cAAc,CAAC;MAC7B,OAAO,cAAc;IACzB;IAEA,OAAO,IAAI;EACf,CAAC,CAAC,OAAOmB,KAAK,EAAE;IACZF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,OAAO,IAAI;EACf;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMS,cAAc,GAAI3B,UAAU,IAAK;EAC1C,MAAMK,IAAI,GAAGH,QAAQ,CAACK,aAAa,CAAC,MAAM,CAAC;EAC3CF,IAAI,CAACG,GAAG,GAAG,SAAS;EACpBH,IAAI,CAACuB,EAAE,GAAG,OAAO;EACjBvB,IAAI,CAACK,IAAI,GAAGV,UAAU;EACtBE,QAAQ,CAACS,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;AACnC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMwB,mBAAmB,GAAIC,IAAI,IAAK;EACzC,MAAMC,YAAY,GAAG,CAAC,cAAc,EAAE,0BAA0B,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,eAAe,CAAC;EACrJ,MAAMC,iBAAiB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;EAC9D,MAAMC,aAAa,GAAGH,IAAI,CAACI,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;;EAE9D;EACA,IAAI,CAACN,YAAY,CAACO,QAAQ,CAACR,IAAI,CAACrB,IAAI,CAAC,IAAI,CAACuB,iBAAiB,CAACM,QAAQ,CAACL,aAAa,CAAC,EAAE;IACjF,OAAO;MACHT,OAAO,EAAE,KAAK;MACde,OAAO,EAAE;IACb,CAAC;EACL;;EAEA;EACA,IAAIT,IAAI,CAACU,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;IAC7B,OAAO;MACHhB,OAAO,EAAE,KAAK;MACde,OAAO,EAAE;IACb,CAAC;EACL;;EAEA;EACA,OAAO,IAAIE,OAAO,CAAEC,OAAO,IAAK;IAC5B,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACE,MAAM,GAAG,YAAW;MACpB,MAAMC,YAAY,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MACrC,MAAMC,aAAa,GAAGD,YAAY,CAACR,QAAQ,CAAC,IAAI,CAACU,KAAK,CAAC,IAAI,IAAI,CAACA,KAAK,KAAK,IAAI,CAACC,MAAM;MAErFP,OAAO,CAAC;QACJlB,OAAO,EAAE,IAAI;QACbe,OAAO,EAAEQ,aAAa,GAAG,oBAAoB,GAAG,4EAA4E;QAC5HA;MACJ,CAAC,CAAC;IACN,CAAC;IACDJ,GAAG,CAACO,OAAO,GAAG,YAAW;MACrBR,OAAO,CAAC;QACJlB,OAAO,EAAE,IAAI;QACbe,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC;IACDI,GAAG,CAACQ,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACvB,IAAI,CAAC;EACvC,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMwB,oBAAoB,GAAGA,CAAA,KAAM;EACtC,MAAMC,WAAW,GAAGrD,QAAQ,CAACY,aAAa,CAAC,mBAAmB,CAAC;EAC/D,OAAOyC,WAAW,GAAGA,WAAW,CAAC7C,IAAI,GAAG,IAAI;AAChD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}