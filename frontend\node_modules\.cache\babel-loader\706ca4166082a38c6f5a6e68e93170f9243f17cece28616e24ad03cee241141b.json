{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\WelcomeSplash.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport MainLayout from '../components/Layout/MainLayout';\nimport ErrorAlert from '../components/ErrorAlert';\nimport { handleError, retryOperation } from '../utils/errorHandler';\nimport HeroSlider from '../components/WelcomePage/HeroSlider';\nimport ChallengesList from '../components/WelcomePage/ChallengesList';\nimport RecentBets from '../components/WelcomePage/RecentBets';\nimport './WelcomeSplash.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst WelcomeSplash = () => {\n  _s();\n  const [recentChallenges, setRecentChallenges] = useState([]);\n  const [recentBets, setRecentBets] = useState([]);\n  const [loading, setLoading] = useState({\n    challenges: true,\n    bets: true\n  });\n  const [error, setError] = useState(null);\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [userName, setUserName] = useState('');\n  const sliderImages = [process.env.PUBLIC_URL + '/slider/Slider1.png', process.env.PUBLIC_URL + '/slider/Slider2.png'];\n  useEffect(() => {\n    const fetchData = async () => {\n      await Promise.all([fetchRecentChallenges(), fetchRecentBets()]);\n    };\n    fetchData();\n    const userId = localStorage.getItem('userId');\n    setIsLoggedIn(!!userId);\n    if (userId) {\n      const storedUserName = localStorage.getItem('userName');\n      if (storedUserName) {\n        setUserName(storedUserName);\n      } else {\n        fetchUserName(userId);\n      }\n    }\n  }, []);\n  const fetchRecentChallenges = async () => {\n    try {\n      setLoading(prev => ({\n        ...prev,\n        challenges: true\n      }));\n      const response = await retryOperation(async () => {\n        const result = await axios.get('recent_challenges.php');\n        return result;\n      });\n      if (response.data.success && Array.isArray(response.data.challenges)) {\n        const challenges = response.data.challenges.map(challenge => ({\n          ...challenge,\n          end_time: new Date(challenge.end_time)\n        }));\n        setRecentChallenges(challenges);\n      } else {\n        // If no challenges or unexpected response format, set empty array\n        setRecentChallenges([]);\n      }\n    } catch (err) {\n      console.error(\"Error fetching challenges:\", err);\n      // Don't show error to user, just set empty array\n      setRecentChallenges([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        challenges: false\n      }));\n    }\n  };\n  const fetchRecentBets = async () => {\n    try {\n      setLoading(prev => ({\n        ...prev,\n        bets: true\n      }));\n      const response = await retryOperation(async () => {\n        const result = await axios.get(`${API_BASE_URL}/handlers/welcome_recent_bets.php`);\n        return result;\n      });\n      if (response.data.success && Array.isArray(response.data.bets)) {\n        setRecentBets(response.data.bets);\n      } else {\n        // If no bets or unexpected response format, set empty array\n        setRecentBets([]);\n      }\n    } catch (err) {\n      console.error(\"Error fetching recent bets:\", err);\n      // Don't show error to user, just set empty array\n      setRecentBets([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        bets: false\n      }));\n    }\n  };\n  const fetchUserName = async userId => {\n    try {\n      const response = await retryOperation(async () => {\n        const result = await axios.get(`${API_BASE_URL}/handlers/get_user_info.php?user_id=${userId}`);\n        if (!result.data.success) {\n          throw new Error(result.data.message || 'Failed to fetch user information');\n        }\n        return result;\n      });\n      if (response.data.user && response.data.user.username) {\n        setUserName(response.data.user.username);\n        localStorage.setItem('userName', response.data.user.username);\n      }\n    } catch (err) {\n      const appError = handleError(err, {\n        component: 'WelcomeSplash',\n        operation: 'fetchUserName'\n      });\n      console.error(appError);\n      // Don't set error state to avoid showing error alert for this operation\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(MainLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome welcome-splash\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome__content\",\n        children: [error && /*#__PURE__*/_jsxDEV(ErrorAlert, {\n          error: error,\n          onClose: () => setError(null)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(HeroSlider, {\n          sliderImages: sliderImages\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section challenges-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section__header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"section__title\",\n              children: \"LIVE CHALLENGES\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section__actions\",\n              children: isLoggedIn ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"user-welcome\",\n                  children: [\"Welcome, \", userName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/user/dashboard\",\n                  className: \"section__link\",\n                  children: \"Dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"section__link login-link\",\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ChallengesList, {\n            recentChallenges: recentChallenges,\n            loading: loading.challenges,\n            isLoggedIn: isLoggedIn,\n            API_BASE_URL: API_BASE_URL\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section recent-bets-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section__header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"section__title\",\n              children: \"RECENT BETS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/recent-bets\",\n              className: \"section__link\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(RecentBets, {\n            recentBets: recentBets,\n            loading: loading.bets,\n            API_BASE_URL: API_BASE_URL\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 5\n  }, this);\n};\n_s(WelcomeSplash, \"g/OvvzvyzDUHhNrwLkeiZGTJ+QU=\");\n_c = WelcomeSplash;\nexport default WelcomeSplash;\nvar _c;\n$RefreshReg$(_c, \"WelcomeSplash\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "axios", "MainLayout", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleError", "retryOperation", "HeroSlider", "ChallengesList", "RecentBets", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "WelcomeSplash", "_s", "recentChallenges", "setRecentChallenges", "recentBets", "setRecentBets", "loading", "setLoading", "challenges", "bets", "error", "setError", "isLoggedIn", "setIsLoggedIn", "userName", "setUserName", "sliderImages", "process", "env", "PUBLIC_URL", "fetchData", "Promise", "all", "fetchRecentChallenges", "fetchRecentBets", "userId", "localStorage", "getItem", "storedUserName", "fetchUserName", "prev", "response", "result", "get", "data", "success", "Array", "isArray", "map", "challenge", "end_time", "Date", "err", "console", "API_BASE_URL", "Error", "message", "user", "username", "setItem", "appError", "component", "operation", "children", "className", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/WelcomeSplash.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport MainLayout from '../components/Layout/MainLayout';\nimport ErrorAlert from '../components/ErrorAlert';\nimport { handleError, retryOperation } from '../utils/errorHandler';\nimport HeroSlider from '../components/WelcomePage/HeroSlider';\nimport ChallengesList from '../components/WelcomePage/ChallengesList';\nimport RecentBets from '../components/WelcomePage/RecentBets';\nimport './WelcomeSplash.css';\n\nconst WelcomeSplash = () => {\n  const [recentChallenges, setRecentChallenges] = useState([]);\n  const [recentBets, setRecentBets] = useState([]);\n  const [loading, setLoading] = useState({\n    challenges: true,\n    bets: true\n  });\n  const [error, setError] = useState(null);\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [userName, setUserName] = useState('');\n\n  const sliderImages = [\n    process.env.PUBLIC_URL + '/slider/Slider1.png',\n    process.env.PUBLIC_URL + '/slider/Slider2.png'\n  ];\n\n  useEffect(() => {\n    const fetchData = async () => {\n      await Promise.all([\n        fetchRecentChallenges(),\n        fetchRecentBets()\n      ]);\n    };\n\n    fetchData();\n\n    const userId = localStorage.getItem('userId');\n    setIsLoggedIn(!!userId);\n\n    if (userId) {\n      const storedUserName = localStorage.getItem('userName');\n      if (storedUserName) {\n        setUserName(storedUserName);\n      } else {\n        fetchUserName(userId);\n      }\n    }\n  }, []);\n\n  const fetchRecentChallenges = async () => {\n    try {\n      setLoading(prev => ({ ...prev, challenges: true }));\n      const response = await retryOperation(async () => {\n        const result = await axios.get('recent_challenges.php');\n        return result;\n      });\n\n      if (response.data.success && Array.isArray(response.data.challenges)) {\n        const challenges = response.data.challenges.map(challenge => ({\n          ...challenge,\n          end_time: new Date(challenge.end_time)\n        }));\n        setRecentChallenges(challenges);\n      } else {\n        // If no challenges or unexpected response format, set empty array\n        setRecentChallenges([]);\n      }\n    } catch (err) {\n      console.error(\"Error fetching challenges:\", err);\n      // Don't show error to user, just set empty array\n      setRecentChallenges([]);\n    } finally {\n      setLoading(prev => ({ ...prev, challenges: false }));\n    }\n  };\n\n  const fetchRecentBets = async () => {\n    try {\n      setLoading(prev => ({ ...prev, bets: true }));\n      const response = await retryOperation(async () => {\n        const result = await axios.get(`${API_BASE_URL}/handlers/welcome_recent_bets.php`);\n        return result;\n      });\n\n      if (response.data.success && Array.isArray(response.data.bets)) {\n        setRecentBets(response.data.bets);\n      } else {\n        // If no bets or unexpected response format, set empty array\n        setRecentBets([]);\n      }\n    } catch (err) {\n      console.error(\"Error fetching recent bets:\", err);\n      // Don't show error to user, just set empty array\n      setRecentBets([]);\n    } finally {\n      setLoading(prev => ({ ...prev, bets: false }));\n    }\n  };\n\n  const fetchUserName = async (userId) => {\n    try {\n      const response = await retryOperation(async () => {\n        const result = await axios.get(`${API_BASE_URL}/handlers/get_user_info.php?user_id=${userId}`);\n        if (!result.data.success) {\n          throw new Error(result.data.message || 'Failed to fetch user information');\n        }\n        return result;\n      });\n\n      if (response.data.user && response.data.user.username) {\n        setUserName(response.data.user.username);\n        localStorage.setItem('userName', response.data.user.username);\n      }\n    } catch (err) {\n      const appError = handleError(err, { component: 'WelcomeSplash', operation: 'fetchUserName' });\n      console.error(appError);\n      // Don't set error state to avoid showing error alert for this operation\n    }\n  };\n\n  return (\n    <MainLayout>\n      <div className=\"welcome welcome-splash\">\n        <div className=\"welcome__content\">\n          {error && <ErrorAlert error={error} onClose={() => setError(null)} />}\n\n          {/* Hero Section */}\n          <HeroSlider sliderImages={sliderImages} />\n\n          {/* Live Challenges Section */}\n          <section className=\"section challenges-section\">\n            <div className=\"section__header\">\n              <h2 className=\"section__title\">LIVE CHALLENGES</h2>\n              <div className=\"section__actions\">\n                {isLoggedIn ? (\n                  <>\n                    <span className=\"user-welcome\">Welcome, {userName}</span>\n                    <Link to=\"/user/dashboard\" className=\"section__link\">Dashboard</Link>\n                  </>\n                ) : (\n                  <Link to=\"/login\" className=\"section__link login-link\">Login</Link>\n                )}\n              </div>\n            </div>\n            <ChallengesList\n              recentChallenges={recentChallenges}\n              loading={loading.challenges}\n              isLoggedIn={isLoggedIn}\n              API_BASE_URL={API_BASE_URL}\n            />\n          </section>\n\n          {/* Recent Bets Section */}\n          <section className=\"section recent-bets-section\">\n            <div className=\"section__header\">\n              <h2 className=\"section__title\">RECENT BETS</h2>\n              <Link to=\"/user/recent-bets\" className=\"section__link\">View All</Link>\n            </div>\n            <RecentBets\n              recentBets={recentBets}\n              loading={loading.bets}\n              API_BASE_URL={API_BASE_URL}\n            />\n          </section>\n        </div>\n      </div>\n    </MainLayout>\n  );\n};\n\nexport default WelcomeSplash;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,UAAU,MAAM,iCAAiC;AACxD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,WAAW,EAAEC,cAAc,QAAQ,uBAAuB;AACnE,OAAOC,UAAU,MAAM,sCAAsC;AAC7D,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,sCAAsC;AAC7D,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC;IACrCuB,UAAU,EAAE,IAAI;IAChBC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAM+B,YAAY,GAAG,CACnBC,OAAO,CAACC,GAAG,CAACC,UAAU,GAAG,qBAAqB,EAC9CF,OAAO,CAACC,GAAG,CAACC,UAAU,GAAG,qBAAqB,CAC/C;EAEDjC,SAAS,CAAC,MAAM;IACd,MAAMkC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChBC,qBAAqB,CAAC,CAAC,EACvBC,eAAe,CAAC,CAAC,CAClB,CAAC;IACJ,CAAC;IAEDJ,SAAS,CAAC,CAAC;IAEX,MAAMK,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAC7Cd,aAAa,CAAC,CAAC,CAACY,MAAM,CAAC;IAEvB,IAAIA,MAAM,EAAE;MACV,MAAMG,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MACvD,IAAIC,cAAc,EAAE;QAClBb,WAAW,CAACa,cAAc,CAAC;MAC7B,CAAC,MAAM;QACLC,aAAa,CAACJ,MAAM,CAAC;MACvB;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMF,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACFhB,UAAU,CAACuB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtB,UAAU,EAAE;MAAK,CAAC,CAAC,CAAC;MACnD,MAAMuB,QAAQ,GAAG,MAAMvC,cAAc,CAAC,YAAY;QAChD,MAAMwC,MAAM,GAAG,MAAM5C,KAAK,CAAC6C,GAAG,CAAC,uBAAuB,CAAC;QACvD,OAAOD,MAAM;MACf,CAAC,CAAC;MAEF,IAAID,QAAQ,CAACG,IAAI,CAACC,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACG,IAAI,CAAC1B,UAAU,CAAC,EAAE;QACpE,MAAMA,UAAU,GAAGuB,QAAQ,CAACG,IAAI,CAAC1B,UAAU,CAAC8B,GAAG,CAACC,SAAS,KAAK;UAC5D,GAAGA,SAAS;UACZC,QAAQ,EAAE,IAAIC,IAAI,CAACF,SAAS,CAACC,QAAQ;QACvC,CAAC,CAAC,CAAC;QACHrC,mBAAmB,CAACK,UAAU,CAAC;MACjC,CAAC,MAAM;QACL;QACAL,mBAAmB,CAAC,EAAE,CAAC;MACzB;IACF,CAAC,CAAC,OAAOuC,GAAG,EAAE;MACZC,OAAO,CAACjC,KAAK,CAAC,4BAA4B,EAAEgC,GAAG,CAAC;MAChD;MACAvC,mBAAmB,CAAC,EAAE,CAAC;IACzB,CAAC,SAAS;MACRI,UAAU,CAACuB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtB,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAMgB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFjB,UAAU,CAACuB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAErB,IAAI,EAAE;MAAK,CAAC,CAAC,CAAC;MAC7C,MAAMsB,QAAQ,GAAG,MAAMvC,cAAc,CAAC,YAAY;QAChD,MAAMwC,MAAM,GAAG,MAAM5C,KAAK,CAAC6C,GAAG,CAAC,GAAGW,YAAY,mCAAmC,CAAC;QAClF,OAAOZ,MAAM;MACf,CAAC,CAAC;MAEF,IAAID,QAAQ,CAACG,IAAI,CAACC,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACG,IAAI,CAACzB,IAAI,CAAC,EAAE;QAC9DJ,aAAa,CAAC0B,QAAQ,CAACG,IAAI,CAACzB,IAAI,CAAC;MACnC,CAAC,MAAM;QACL;QACAJ,aAAa,CAAC,EAAE,CAAC;MACnB;IACF,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACZC,OAAO,CAACjC,KAAK,CAAC,6BAA6B,EAAEgC,GAAG,CAAC;MACjD;MACArC,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRE,UAAU,CAACuB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAErB,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;EAED,MAAMoB,aAAa,GAAG,MAAOJ,MAAM,IAAK;IACtC,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMvC,cAAc,CAAC,YAAY;QAChD,MAAMwC,MAAM,GAAG,MAAM5C,KAAK,CAAC6C,GAAG,CAAC,GAAGW,YAAY,uCAAuCnB,MAAM,EAAE,CAAC;QAC9F,IAAI,CAACO,MAAM,CAACE,IAAI,CAACC,OAAO,EAAE;UACxB,MAAM,IAAIU,KAAK,CAACb,MAAM,CAACE,IAAI,CAACY,OAAO,IAAI,kCAAkC,CAAC;QAC5E;QACA,OAAOd,MAAM;MACf,CAAC,CAAC;MAEF,IAAID,QAAQ,CAACG,IAAI,CAACa,IAAI,IAAIhB,QAAQ,CAACG,IAAI,CAACa,IAAI,CAACC,QAAQ,EAAE;QACrDjC,WAAW,CAACgB,QAAQ,CAACG,IAAI,CAACa,IAAI,CAACC,QAAQ,CAAC;QACxCtB,YAAY,CAACuB,OAAO,CAAC,UAAU,EAAElB,QAAQ,CAACG,IAAI,CAACa,IAAI,CAACC,QAAQ,CAAC;MAC/D;IACF,CAAC,CAAC,OAAON,GAAG,EAAE;MACZ,MAAMQ,QAAQ,GAAG3D,WAAW,CAACmD,GAAG,EAAE;QAAES,SAAS,EAAE,eAAe;QAAEC,SAAS,EAAE;MAAgB,CAAC,CAAC;MAC7FT,OAAO,CAACjC,KAAK,CAACwC,QAAQ,CAAC;MACvB;IACF;EACF,CAAC;EAED,oBACErD,OAAA,CAACR,UAAU;IAAAgE,QAAA,eACTxD,OAAA;MAAKyD,SAAS,EAAC,wBAAwB;MAAAD,QAAA,eACrCxD,OAAA;QAAKyD,SAAS,EAAC,kBAAkB;QAAAD,QAAA,GAC9B3C,KAAK,iBAAIb,OAAA,CAACP,UAAU;UAACoB,KAAK,EAAEA,KAAM;UAAC6C,OAAO,EAAEA,CAAA,KAAM5C,QAAQ,CAAC,IAAI;QAAE;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGrE9D,OAAA,CAACJ,UAAU;UAACuB,YAAY,EAAEA;QAAa;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1C9D,OAAA;UAASyD,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBAC7CxD,OAAA;YAAKyD,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC9BxD,OAAA;cAAIyD,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnD9D,OAAA;cAAKyD,SAAS,EAAC,kBAAkB;cAAAD,QAAA,EAC9BzC,UAAU,gBACTf,OAAA,CAAAE,SAAA;gBAAAsD,QAAA,gBACExD,OAAA;kBAAMyD,SAAS,EAAC,cAAc;kBAAAD,QAAA,GAAC,WAAS,EAACvC,QAAQ;gBAAA;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzD9D,OAAA,CAACV,IAAI;kBAACyE,EAAE,EAAC,iBAAiB;kBAACN,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eACrE,CAAC,gBAEH9D,OAAA,CAACV,IAAI;gBAACyE,EAAE,EAAC,QAAQ;gBAACN,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACnE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN9D,OAAA,CAACH,cAAc;YACbQ,gBAAgB,EAAEA,gBAAiB;YACnCI,OAAO,EAAEA,OAAO,CAACE,UAAW;YAC5BI,UAAU,EAAEA,UAAW;YACvBgC,YAAY,EAAEA;UAAa;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGV9D,OAAA;UAASyD,SAAS,EAAC,6BAA6B;UAAAD,QAAA,gBAC9CxD,OAAA;YAAKyD,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC9BxD,OAAA;cAAIyD,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C9D,OAAA,CAACV,IAAI;cAACyE,EAAE,EAAC,mBAAmB;cAACN,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACN9D,OAAA,CAACF,UAAU;YACTS,UAAU,EAAEA,UAAW;YACvBE,OAAO,EAAEA,OAAO,CAACG,IAAK;YACtBmC,YAAY,EAAEA;UAAa;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAAC1D,EAAA,CA9JID,aAAa;AAAA6D,EAAA,GAAb7D,aAAa;AAgKnB,eAAeA,aAAa;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}