{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\GeneralSettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCog, FaCheck, FaTimes, FaSave, FaUpload } from 'react-icons/fa';\nimport { useSiteConfig } from '../contexts/SiteConfigContext';\nimport { updateFavicon, refreshFavicon } from '../utils/faviconUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction GeneralSettings() {\n  _s();\n  const {\n    refreshConfig\n  } = useSiteConfig();\n  const [settings, setSettings] = useState({\n    site_name: '',\n    site_logo: '',\n    sidebar_logo: '',\n    contact_email: '',\n    contact_phone: '',\n    facebook_url: '',\n    twitter_url: '',\n    instagram_url: '',\n    about_text: '',\n    terms_conditions: '',\n    privacy_policy: '',\n    footer_text: ''\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [logoPreview, setLogoPreview] = useState('');\n  const [logoFile, setLogoFile] = useState(null);\n  const [sidebarLogoPreview, setSidebarLogoPreview] = useState('');\n  const [sidebarLogoFile, setSidebarLogoFile] = useState(null);\n  const [faviconPreview, setFaviconPreview] = useState('');\n  const [faviconFile, setFaviconFile] = useState(null);\n  const [testingSmtp, setTestingSmtp] = useState(false);\n  useEffect(() => {\n    fetchSettings();\n    fetchFavicon();\n  }, []);\n  const fetchSettings = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_general_settings.php`);\n      if (response.data.success && response.data.settings) {\n        const formattedSettings = {};\n        Object.keys(response.data.settings).forEach(key => {\n          formattedSettings[key] = response.data.settings[key].value;\n        });\n        setSettings(formattedSettings);\n\n        // Set logo preview\n        if (formattedSettings.site_logo) {\n          setLogoPreview(`${API_BASE_URL}/${formattedSettings.site_logo}`);\n        }\n\n        // Set sidebar logo preview\n        if (formattedSettings.sidebar_logo) {\n          setSidebarLogoPreview(`${API_BASE_URL}/${formattedSettings.sidebar_logo}`);\n        }\n      }\n    } catch (err) {\n      setError('Failed to load settings');\n      console.error('Error fetching settings:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchFavicon = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_favicon.php`);\n      if (response.data.success && response.data.favicon_path) {\n        setFaviconPreview(`${API_BASE_URL}/${response.data.favicon_path}`);\n      } else if (response.data.public_favicon_exists) {\n        setFaviconPreview('/favicon.ico');\n      }\n    } catch (err) {\n      console.error('Error fetching favicon:', err);\n      // Try to show default favicon\n      setFaviconPreview('/favicon.ico');\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setSettings(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleLogoChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];\n      if (!allowedTypes.includes(file.type)) {\n        setError('Please select a valid image file (JPG, PNG, or SVG)');\n        return;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        setError('File size must be less than 5MB');\n        return;\n      }\n      setLogoFile(file);\n      setLogoPreview(URL.createObjectURL(file));\n    }\n  };\n  const handleFaviconChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type for favicon\n      const allowedTypes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/ico', 'image/icon', 'image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];\n      const allowedExtensions = ['ico', 'png', 'jpg', 'jpeg', 'svg'];\n      const fileExtension = file.name.split('.').pop().toLowerCase();\n      if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {\n        setError('Please select a valid favicon file (ICO, PNG, JPG, or SVG)');\n        return;\n      }\n\n      // Validate file size (max 2MB for favicon)\n      if (file.size > 2 * 1024 * 1024) {\n        setError('Favicon file size must be less than 2MB');\n        return;\n      }\n      setFaviconFile(file);\n      setFaviconPreview(URL.createObjectURL(file));\n    }\n  };\n  const handleSidebarLogoChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];\n      if (!allowedTypes.includes(file.type)) {\n        setError('Please select a valid image file (JPG, PNG, or SVG)');\n        return;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        setError('File size must be less than 5MB');\n        return;\n      }\n      setSidebarLogoFile(file);\n      setSidebarLogoPreview(URL.createObjectURL(file));\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      setSaving(true);\n      setError('');\n      setSuccess('');\n\n      // First upload logo if a new one is selected\n      let updatedSettings = {\n        ...settings\n      };\n      if (logoFile) {\n        const formData = new FormData();\n        formData.append('logo', logoFile);\n        const uploadResponse = await axios.post(`${API_BASE_URL}/handlers/upload_logo.php`, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        if (uploadResponse.data.success) {\n          updatedSettings.site_logo = uploadResponse.data.file_path;\n        } else {\n          throw new Error(uploadResponse.data.message || 'Failed to upload logo');\n        }\n      }\n\n      // Upload favicon if a new one is selected\n      if (faviconFile) {\n        const faviconFormData = new FormData();\n        faviconFormData.append('favicon', faviconFile);\n        const faviconUploadResponse = await axios.post(`${API_BASE_URL}/handlers/upload_favicon.php`, faviconFormData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        if (faviconUploadResponse.data.success) {\n          // Favicon is handled separately in the database, no need to add to settings\n          console.log('Favicon uploaded successfully:', faviconUploadResponse.data.file_path);\n\n          // Update the favicon in the browser immediately\n          const newFaviconUrl = `${API_BASE_URL}/${faviconUploadResponse.data.file_path}`;\n          updateFavicon(newFaviconUrl);\n\n          // Update the preview\n          setFaviconPreview(newFaviconUrl);\n        } else {\n          throw new Error(faviconUploadResponse.data.message || 'Failed to upload favicon');\n        }\n      }\n\n      // Upload sidebar logo if a new one is selected\n      if (sidebarLogoFile) {\n        const sidebarLogoFormData = new FormData();\n        sidebarLogoFormData.append('sidebar_logo', sidebarLogoFile);\n        const sidebarLogoUploadResponse = await axios.post(`${API_BASE_URL}/handlers/upload_sidebar_logo.php`, sidebarLogoFormData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        if (sidebarLogoUploadResponse.data.success) {\n          updatedSettings.sidebar_logo = sidebarLogoUploadResponse.data.file_path;\n        } else {\n          throw new Error(sidebarLogoUploadResponse.data.message || 'Failed to upload sidebar logo');\n        }\n      }\n\n      // Then update settings\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_general_settings.php`, {\n        settings: updatedSettings\n      });\n      if (response.data.success) {\n        setSuccess('Settings saved successfully!');\n        setTimeout(() => setSuccess(''), 3000);\n        // Update settings state with new logo path\n        setSettings(updatedSettings);\n        // Refresh site config to update throughout the app\n        refreshConfig();\n      } else {\n        throw new Error(response.data.message || 'Failed to save general settings');\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to save settings. Please try again.');\n      console.error('Error saving settings:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n  const testSmtpConnection = async () => {\n    try {\n      setTestingSmtp(true);\n      setError('');\n      setSuccess('');\n      const response = await axios.post(`${API_BASE_URL}/handlers/test_smtp.php`, {\n        host: 'mail.hostinger.com',\n        port: 465,\n        username: '<EMAIL>',\n        password: 'Money2025@Demo#',\n        encryption: 'ssl',\n        from_email: '<EMAIL>',\n        from_name: 'FanBet247'\n      });\n      if (response.data.success) {\n        setSuccess('SMTP connection test successful!');\n      } else {\n        setError(response.data.message || 'SMTP connection test failed');\n      }\n    } catch (err) {\n      setError('Failed to test SMTP connection');\n      console.error('Error testing SMTP:', err);\n    } finally {\n      setTestingSmtp(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg text-gray-600\",\n          children: \"Loading settings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-800 flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(FaCog, {\n          className: \"text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 21\n        }, this), \"General Settings\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mt-2\",\n        children: \"Configure basic system settings like site name, logo, and contact information.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n        className: \"text-red-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-red-700\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n        className: \"text-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-green-700\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"admin-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Basic Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"site_name\",\n            children: \"Site Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"site_name\",\n            name: \"site_name\",\n            value: settings.site_name,\n            onChange: handleInputChange,\n            required: true,\n            placeholder: \"FanBet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"site_logo\",\n            children: \"Site Logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-upload-container\",\n            children: [logoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-preview\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: logoPreview,\n                alt: \"Site Logo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-upload\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"logo_file\",\n                className: \"upload-button\",\n                children: [/*#__PURE__*/_jsxDEV(FaUpload, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this), \" Upload Logo\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"logo_file\",\n                accept: \"image/*\",\n                onChange: handleLogoChange,\n                style: {\n                  display: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"sidebar_logo\",\n            children: \"Sidebar Logo (User Dashboard)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-upload-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-info\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"logo-description\",\n                children: \"Upload a logo specifically for the user dashboard sidebar. This should be a white or light-colored logo for visibility on dark backgrounds.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this), sidebarLogoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-preview\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: sidebarLogoPreview,\n                alt: \"Sidebar Logo\",\n                style: {\n                  backgroundColor: '#1a1c23',\n                  padding: '10px',\n                  borderRadius: '8px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-upload\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"sidebar_logo_file\",\n                className: \"upload-button\",\n                children: [/*#__PURE__*/_jsxDEV(FaUpload, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this), \" Upload Sidebar Logo\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"sidebar_logo_file\",\n                accept: \"image/*\",\n                onChange: handleSidebarLogoChange,\n                style: {\n                  display: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"site_favicon\",\n            children: \"Site Favicon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"favicon-upload-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"favicon-info\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"favicon-description\",\n                children: \"Upload a favicon for your site. Recommended formats: ICO, PNG (16x16, 32x32, or 48x48 pixels)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this), faviconPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"favicon-preview\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: faviconPreview,\n                alt: \"Site Favicon\",\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Current Favicon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"favicon-upload\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"favicon_file\",\n                className: \"upload-button\",\n                children: [/*#__PURE__*/_jsxDEV(FaUpload, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this), \" Upload Favicon\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"favicon_file\",\n                accept: \".ico,.png,.jpg,.jpeg,.svg\",\n                onChange: handleFaviconChange,\n                style: {\n                  display: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Contact Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"contact_email\",\n            children: \"Contact Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"contact_email\",\n            name: \"contact_email\",\n            value: settings.contact_email,\n            onChange: handleInputChange,\n            required: true,\n            placeholder: \"<EMAIL>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"contact_phone\",\n            children: \"Contact Phone\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"contact_phone\",\n            name: \"contact_phone\",\n            value: settings.contact_phone,\n            onChange: handleInputChange,\n            placeholder: \"+1234567890\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Social Media\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"facebook_url\",\n            children: \"Facebook URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"url\",\n            id: \"facebook_url\",\n            name: \"facebook_url\",\n            value: settings.facebook_url,\n            onChange: handleInputChange,\n            placeholder: \"https://facebook.com/fanbet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"twitter_url\",\n            children: \"Twitter URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"url\",\n            id: \"twitter_url\",\n            name: \"twitter_url\",\n            value: settings.twitter_url,\n            onChange: handleInputChange,\n            placeholder: \"https://twitter.com/fanbet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"instagram_url\",\n            children: \"Instagram URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"url\",\n            id: \"instagram_url\",\n            name: \"instagram_url\",\n            value: settings.instagram_url,\n            onChange: handleInputChange,\n            placeholder: \"https://instagram.com/fanbet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Content\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"about_text\",\n            children: \"About Text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"about_text\",\n            name: \"about_text\",\n            value: settings.about_text,\n            onChange: handleInputChange,\n            rows: \"4\",\n            placeholder: \"About FanBet247...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"terms_conditions\",\n            children: \"Terms & Conditions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"terms_conditions\",\n            name: \"terms_conditions\",\n            value: settings.terms_conditions,\n            onChange: handleInputChange,\n            rows: \"6\",\n            placeholder: \"Terms and conditions text goes here...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"privacy_policy\",\n            children: \"Privacy Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"privacy_policy\",\n            name: \"privacy_policy\",\n            value: settings.privacy_policy,\n            onChange: handleInputChange,\n            rows: \"6\",\n            placeholder: \"Privacy policy text goes here...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"footer_text\",\n            children: \"Footer Text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"footer_text\",\n            name: \"footer_text\",\n            value: settings.footer_text,\n            onChange: handleInputChange,\n            placeholder: \"\\xA9 2024 FanBet247. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"SMTP Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Test the SMTP connection with the configured settings (mail.hostinger.com:465)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: testSmtpConnection,\n          className: \"btn btn-secondary\",\n          disabled: testingSmtp,\n          children: testingSmtp ? 'Testing...' : 'Test SMTP Connection'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: saving,\n          children: saving ? 'Saving...' : 'Save Settings'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n          .favicon-upload-container {\n            display: flex;\n            flex-direction: column;\n            gap: 1rem;\n            padding: 1rem;\n            border: 2px dashed #e2e8f0;\n            border-radius: 8px;\n            background-color: #f8fafc;\n          }\n\n          .favicon-info {\n            margin-bottom: 0.5rem;\n          }\n\n          .favicon-description {\n            font-size: 0.875rem;\n            color: #64748b;\n            margin: 0;\n          }\n\n          .favicon-preview {\n            display: flex;\n            align-items: center;\n            gap: 0.75rem;\n            padding: 0.75rem;\n            background-color: white;\n            border: 1px solid #e2e8f0;\n            border-radius: 6px;\n          }\n\n          .favicon-preview img {\n            border: 1px solid #e2e8f0;\n            border-radius: 4px;\n          }\n\n          .favicon-preview span {\n            font-size: 0.875rem;\n            color: #475569;\n            font-weight: 500;\n          }\n\n          .favicon-upload {\n            display: flex;\n            justify-content: center;\n          }\n\n          .favicon-upload .upload-button {\n            display: inline-flex;\n            align-items: center;\n            gap: 0.5rem;\n            padding: 0.75rem 1.5rem;\n            background-color: #3b82f6;\n            color: white;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-size: 0.875rem;\n            font-weight: 500;\n            transition: background-color 0.2s;\n          }\n\n          .favicon-upload .upload-button:hover {\n            background-color: #2563eb;\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 295,\n    columnNumber: 9\n  }, this);\n}\n_s(GeneralSettings, \"lIpH6YRbDSGxfFo/Gg5FG//fKkE=\", false, function () {\n  return [useSiteConfig];\n});\n_c = GeneralSettings;\n;\nexport default GeneralSettings;\nvar _c;\n$RefreshReg$(_c, \"GeneralSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaCog", "FaCheck", "FaTimes", "FaSave", "FaUpload", "useSiteConfig", "updateFavicon", "refreshFavicon", "jsxDEV", "_jsxDEV", "API_BASE_URL", "GeneralSettings", "_s", "refreshConfig", "settings", "setSettings", "site_name", "site_logo", "sidebar_logo", "contact_email", "contact_phone", "facebook_url", "twitter_url", "instagram_url", "about_text", "terms_conditions", "privacy_policy", "footer_text", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "logoPreview", "setLogoPreview", "logoFile", "setLogoFile", "sidebarLogoPreview", "setSidebarLogoPreview", "sidebarLogoFile", "setSidebarLogoFile", "faviconPreview", "setFaviconPreview", "faviconFile", "setFaviconFile", "testingSmtp", "setTestingSmtp", "fetchSettings", "fetchFavicon", "response", "get", "data", "formattedSettings", "Object", "keys", "for<PERSON>ach", "key", "value", "err", "console", "favicon_path", "public_favicon_exists", "handleInputChange", "e", "name", "target", "prev", "handleLogoChange", "file", "files", "allowedTypes", "includes", "type", "size", "URL", "createObjectURL", "handleFaviconChange", "allowedExtensions", "fileExtension", "split", "pop", "toLowerCase", "handleSidebarLogoChange", "handleSubmit", "preventDefault", "updatedSettings", "formData", "FormData", "append", "uploadResponse", "post", "headers", "file_path", "Error", "message", "faviconFormData", "faviconUploadResponse", "log", "newFaviconUrl", "sidebarLogoFormData", "sidebarLogoUploadResponse", "setTimeout", "testSmtpConnection", "host", "port", "username", "password", "encryption", "from_email", "from_name", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "id", "onChange", "required", "placeholder", "src", "alt", "accept", "style", "display", "backgroundColor", "padding", "borderRadius", "width", "height", "rows", "onClick", "disabled", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/GeneralSettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCog, FaCheck, FaTimes, FaSave, FaUpload } from 'react-icons/fa';\nimport { useSiteConfig } from '../contexts/SiteConfigContext';\nimport { updateFavicon, refreshFavicon } from '../utils/faviconUtils';\n\nconst API_BASE_URL = '/backend';\n\nfunction GeneralSettings() {\n    const { refreshConfig } = useSiteConfig();\n    const [settings, setSettings] = useState({\n        site_name: '',\n        site_logo: '',\n        sidebar_logo: '',\n        contact_email: '',\n        contact_phone: '',\n        facebook_url: '',\n        twitter_url: '',\n        instagram_url: '',\n        about_text: '',\n        terms_conditions: '',\n        privacy_policy: '',\n        footer_text: ''\n    });\n    const [loading, setLoading] = useState(true);\n    const [saving, setSaving] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [logoPreview, setLogoPreview] = useState('');\n    const [logoFile, setLogoFile] = useState(null);\n    const [sidebarLogoPreview, setSidebarLogoPreview] = useState('');\n    const [sidebarLogoFile, setSidebarLogoFile] = useState(null);\n    const [faviconPreview, setFaviconPreview] = useState('');\n    const [faviconFile, setFaviconFile] = useState(null);\n    const [testingSmtp, setTestingSmtp] = useState(false);\n\n    useEffect(() => {\n        fetchSettings();\n        fetchFavicon();\n    }, []);\n\n    const fetchSettings = async () => {\n        try {\n            setLoading(true);\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_general_settings.php`);\n\n            if (response.data.success && response.data.settings) {\n                const formattedSettings = {};\n                Object.keys(response.data.settings).forEach(key => {\n                    formattedSettings[key] = response.data.settings[key].value;\n                });\n                setSettings(formattedSettings);\n\n                // Set logo preview\n                if (formattedSettings.site_logo) {\n                    setLogoPreview(`${API_BASE_URL}/${formattedSettings.site_logo}`);\n                }\n\n                // Set sidebar logo preview\n                if (formattedSettings.sidebar_logo) {\n                    setSidebarLogoPreview(`${API_BASE_URL}/${formattedSettings.sidebar_logo}`);\n                }\n            }\n        } catch (err) {\n            setError('Failed to load settings');\n            console.error('Error fetching settings:', err);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchFavicon = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_favicon.php`);\n            if (response.data.success && response.data.favicon_path) {\n                setFaviconPreview(`${API_BASE_URL}/${response.data.favicon_path}`);\n            } else if (response.data.public_favicon_exists) {\n                setFaviconPreview('/favicon.ico');\n            }\n        } catch (err) {\n            console.error('Error fetching favicon:', err);\n            // Try to show default favicon\n            setFaviconPreview('/favicon.ico');\n        }\n    };\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setSettings(prev => ({\n            ...prev,\n            [name]: value\n        }));\n    };\n\n    const handleLogoChange = (e) => {\n        const file = e.target.files[0];\n        if (file) {\n            // Validate file type\n            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];\n            if (!allowedTypes.includes(file.type)) {\n                setError('Please select a valid image file (JPG, PNG, or SVG)');\n                return;\n            }\n\n            // Validate file size (max 5MB)\n            if (file.size > 5 * 1024 * 1024) {\n                setError('File size must be less than 5MB');\n                return;\n            }\n\n            setLogoFile(file);\n            setLogoPreview(URL.createObjectURL(file));\n        }\n    };\n\n    const handleFaviconChange = (e) => {\n        const file = e.target.files[0];\n        if (file) {\n            // Validate file type for favicon\n            const allowedTypes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/ico', 'image/icon', 'image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];\n            const allowedExtensions = ['ico', 'png', 'jpg', 'jpeg', 'svg'];\n            const fileExtension = file.name.split('.').pop().toLowerCase();\n\n            if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {\n                setError('Please select a valid favicon file (ICO, PNG, JPG, or SVG)');\n                return;\n            }\n\n            // Validate file size (max 2MB for favicon)\n            if (file.size > 2 * 1024 * 1024) {\n                setError('Favicon file size must be less than 2MB');\n                return;\n            }\n\n            setFaviconFile(file);\n            setFaviconPreview(URL.createObjectURL(file));\n        }\n    };\n\n    const handleSidebarLogoChange = (e) => {\n        const file = e.target.files[0];\n        if (file) {\n            // Validate file type\n            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];\n            if (!allowedTypes.includes(file.type)) {\n                setError('Please select a valid image file (JPG, PNG, or SVG)');\n                return;\n            }\n\n            // Validate file size (max 5MB)\n            if (file.size > 5 * 1024 * 1024) {\n                setError('File size must be less than 5MB');\n                return;\n            }\n\n            setSidebarLogoFile(file);\n            setSidebarLogoPreview(URL.createObjectURL(file));\n        }\n    };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      setSaving(true);\n      setError('');\n      setSuccess('');\n\n      // First upload logo if a new one is selected\n      let updatedSettings = { ...settings };\n\n      if (logoFile) {\n        const formData = new FormData();\n        formData.append('logo', logoFile);\n\n        const uploadResponse = await axios.post(`${API_BASE_URL}/handlers/upload_logo.php`, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n\n        if (uploadResponse.data.success) {\n          updatedSettings.site_logo = uploadResponse.data.file_path;\n        } else {\n          throw new Error(uploadResponse.data.message || 'Failed to upload logo');\n        }\n      }\n\n      // Upload favicon if a new one is selected\n      if (faviconFile) {\n        const faviconFormData = new FormData();\n        faviconFormData.append('favicon', faviconFile);\n\n        const faviconUploadResponse = await axios.post(`${API_BASE_URL}/handlers/upload_favicon.php`, faviconFormData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n\n        if (faviconUploadResponse.data.success) {\n          // Favicon is handled separately in the database, no need to add to settings\n          console.log('Favicon uploaded successfully:', faviconUploadResponse.data.file_path);\n\n          // Update the favicon in the browser immediately\n          const newFaviconUrl = `${API_BASE_URL}/${faviconUploadResponse.data.file_path}`;\n          updateFavicon(newFaviconUrl);\n\n          // Update the preview\n          setFaviconPreview(newFaviconUrl);\n        } else {\n          throw new Error(faviconUploadResponse.data.message || 'Failed to upload favicon');\n        }\n      }\n\n      // Upload sidebar logo if a new one is selected\n      if (sidebarLogoFile) {\n        const sidebarLogoFormData = new FormData();\n        sidebarLogoFormData.append('sidebar_logo', sidebarLogoFile);\n\n        const sidebarLogoUploadResponse = await axios.post(`${API_BASE_URL}/handlers/upload_sidebar_logo.php`, sidebarLogoFormData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n\n        if (sidebarLogoUploadResponse.data.success) {\n          updatedSettings.sidebar_logo = sidebarLogoUploadResponse.data.file_path;\n        } else {\n          throw new Error(sidebarLogoUploadResponse.data.message || 'Failed to upload sidebar logo');\n        }\n      }\n\n      // Then update settings\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_general_settings.php`, {\n        settings: updatedSettings\n      });\n\n      if (response.data.success) {\n        setSuccess('Settings saved successfully!');\n        setTimeout(() => setSuccess(''), 3000);\n        // Update settings state with new logo path\n        setSettings(updatedSettings);\n        // Refresh site config to update throughout the app\n        refreshConfig();\n      } else {\n        throw new Error(response.data.message || 'Failed to save general settings');\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to save settings. Please try again.');\n      console.error('Error saving settings:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const testSmtpConnection = async () => {\n    try {\n      setTestingSmtp(true);\n      setError('');\n      setSuccess('');\n\n      const response = await axios.post(`${API_BASE_URL}/handlers/test_smtp.php`, {\n        host: 'mail.hostinger.com',\n        port: 465,\n        username: '<EMAIL>',\n        password: 'Money2025@Demo#',\n        encryption: 'ssl',\n        from_email: '<EMAIL>',\n        from_name: 'FanBet247'\n      });\n\n      if (response.data.success) {\n        setSuccess('SMTP connection test successful!');\n      } else {\n        setError(response.data.message || 'SMTP connection test failed');\n      }\n    } catch (err) {\n      setError('Failed to test SMTP connection');\n      console.error('Error testing SMTP:', err);\n    } finally {\n      setTestingSmtp(false);\n    }\n  };\n\n    if (loading) {\n        return (\n            <div className=\"p-6\">\n                <div className=\"flex items-center justify-center h-64\">\n                    <div className=\"text-lg text-gray-600\">Loading settings...</div>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"p-6\">\n            {/* Header */}\n            <div className=\"mb-8\">\n                <h1 className=\"text-2xl font-bold text-gray-800 flex items-center gap-3\">\n                    <FaCog className=\"text-blue-500\" />\n                    General Settings\n                </h1>\n                <p className=\"text-gray-600 mt-2\">\n                    Configure basic system settings like site name, logo, and contact information.\n                </p>\n            </div>\n\n            {/* Alerts */}\n            {error && (\n                <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\">\n                    <FaTimes className=\"text-red-500\" />\n                    <span className=\"text-red-700\">{error}</span>\n                </div>\n            )}\n\n            {success && (\n                <div className=\"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\">\n                    <FaCheck className=\"text-green-500\" />\n                    <span className=\"text-green-700\">{success}</span>\n                </div>\n            )}\n\n        <form onSubmit={handleSubmit} className=\"admin-form\">\n          <div className=\"form-section\">\n            <h2>Basic Information</h2>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"site_name\">Site Name</label>\n              <input\n                type=\"text\"\n                id=\"site_name\"\n                name=\"site_name\"\n                value={settings.site_name}\n                onChange={handleInputChange}\n                required\n                placeholder=\"FanBet247\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"site_logo\">Site Logo</label>\n              <div className=\"logo-upload-container\">\n                {logoPreview && (\n                  <div className=\"logo-preview\">\n                    <img src={logoPreview} alt=\"Site Logo\" />\n                  </div>\n                )}\n                <div className=\"logo-upload\">\n                  <label htmlFor=\"logo_file\" className=\"upload-button\">\n                    <FaUpload /> Upload Logo\n                  </label>\n                  <input\n                    type=\"file\"\n                    id=\"logo_file\"\n                    accept=\"image/*\"\n                    onChange={handleLogoChange}\n                    style={{ display: 'none' }}\n                  />\n                </div>\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"sidebar_logo\">Sidebar Logo (User Dashboard)</label>\n              <div className=\"logo-upload-container\">\n                <div className=\"logo-info\">\n                  <p className=\"logo-description\">\n                    Upload a logo specifically for the user dashboard sidebar. This should be a white or light-colored logo for visibility on dark backgrounds.\n                  </p>\n                </div>\n                {sidebarLogoPreview && (\n                  <div className=\"logo-preview\">\n                    <img src={sidebarLogoPreview} alt=\"Sidebar Logo\" style={{backgroundColor: '#1a1c23', padding: '10px', borderRadius: '8px'}} />\n                  </div>\n                )}\n                <div className=\"logo-upload\">\n                  <label htmlFor=\"sidebar_logo_file\" className=\"upload-button\">\n                    <FaUpload /> Upload Sidebar Logo\n                  </label>\n                  <input\n                    type=\"file\"\n                    id=\"sidebar_logo_file\"\n                    accept=\"image/*\"\n                    onChange={handleSidebarLogoChange}\n                    style={{ display: 'none' }}\n                  />\n                </div>\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"site_favicon\">Site Favicon</label>\n              <div className=\"favicon-upload-container\">\n                <div className=\"favicon-info\">\n                  <p className=\"favicon-description\">\n                    Upload a favicon for your site. Recommended formats: ICO, PNG (16x16, 32x32, or 48x48 pixels)\n                  </p>\n                </div>\n                {faviconPreview && (\n                  <div className=\"favicon-preview\">\n                    <img src={faviconPreview} alt=\"Site Favicon\" style={{width: '32px', height: '32px'}} />\n                    <span>Current Favicon</span>\n                  </div>\n                )}\n                <div className=\"favicon-upload\">\n                  <label htmlFor=\"favicon_file\" className=\"upload-button\">\n                    <FaUpload /> Upload Favicon\n                  </label>\n                  <input\n                    type=\"file\"\n                    id=\"favicon_file\"\n                    accept=\".ico,.png,.jpg,.jpeg,.svg\"\n                    onChange={handleFaviconChange}\n                    style={{ display: 'none' }}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h2>Contact Information</h2>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"contact_email\">Contact Email</label>\n              <input\n                type=\"email\"\n                id=\"contact_email\"\n                name=\"contact_email\"\n                value={settings.contact_email}\n                onChange={handleInputChange}\n                required\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"contact_phone\">Contact Phone</label>\n              <input\n                type=\"text\"\n                id=\"contact_phone\"\n                name=\"contact_phone\"\n                value={settings.contact_phone}\n                onChange={handleInputChange}\n                placeholder=\"+1234567890\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h2>Social Media</h2>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"facebook_url\">Facebook URL</label>\n              <input\n                type=\"url\"\n                id=\"facebook_url\"\n                name=\"facebook_url\"\n                value={settings.facebook_url}\n                onChange={handleInputChange}\n                placeholder=\"https://facebook.com/fanbet247\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"twitter_url\">Twitter URL</label>\n              <input\n                type=\"url\"\n                id=\"twitter_url\"\n                name=\"twitter_url\"\n                value={settings.twitter_url}\n                onChange={handleInputChange}\n                placeholder=\"https://twitter.com/fanbet247\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"instagram_url\">Instagram URL</label>\n              <input\n                type=\"url\"\n                id=\"instagram_url\"\n                name=\"instagram_url\"\n                value={settings.instagram_url}\n                onChange={handleInputChange}\n                placeholder=\"https://instagram.com/fanbet247\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h2>Content</h2>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"about_text\">About Text</label>\n              <textarea\n                id=\"about_text\"\n                name=\"about_text\"\n                value={settings.about_text}\n                onChange={handleInputChange}\n                rows=\"4\"\n                placeholder=\"About FanBet247...\"\n              ></textarea>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"terms_conditions\">Terms & Conditions</label>\n              <textarea\n                id=\"terms_conditions\"\n                name=\"terms_conditions\"\n                value={settings.terms_conditions}\n                onChange={handleInputChange}\n                rows=\"6\"\n                placeholder=\"Terms and conditions text goes here...\"\n              ></textarea>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"privacy_policy\">Privacy Policy</label>\n              <textarea\n                id=\"privacy_policy\"\n                name=\"privacy_policy\"\n                value={settings.privacy_policy}\n                onChange={handleInputChange}\n                rows=\"6\"\n                placeholder=\"Privacy policy text goes here...\"\n              ></textarea>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"footer_text\">Footer Text</label>\n              <input\n                type=\"text\"\n                id=\"footer_text\"\n                name=\"footer_text\"\n                value={settings.footer_text}\n                onChange={handleInputChange}\n                placeholder=\"© 2024 FanBet247. All rights reserved.\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h2>SMTP Test</h2>\n            <p>Test the SMTP connection with the configured settings (mail.hostinger.com:465)</p>\n            <button\n              type=\"button\"\n              onClick={testSmtpConnection}\n              className=\"btn btn-secondary\"\n              disabled={testingSmtp}\n            >\n              {testingSmtp ? 'Testing...' : 'Test SMTP Connection'}\n            </button>\n          </div>\n\n          <div className=\"form-actions\">\n            <button\n              type=\"submit\"\n              className=\"btn btn-primary\"\n              disabled={saving}\n            >\n              {saving ? 'Saving...' : 'Save Settings'}\n            </button>\n          </div>\n        </form>\n\n        <style jsx>{`\n          .favicon-upload-container {\n            display: flex;\n            flex-direction: column;\n            gap: 1rem;\n            padding: 1rem;\n            border: 2px dashed #e2e8f0;\n            border-radius: 8px;\n            background-color: #f8fafc;\n          }\n\n          .favicon-info {\n            margin-bottom: 0.5rem;\n          }\n\n          .favicon-description {\n            font-size: 0.875rem;\n            color: #64748b;\n            margin: 0;\n          }\n\n          .favicon-preview {\n            display: flex;\n            align-items: center;\n            gap: 0.75rem;\n            padding: 0.75rem;\n            background-color: white;\n            border: 1px solid #e2e8f0;\n            border-radius: 6px;\n          }\n\n          .favicon-preview img {\n            border: 1px solid #e2e8f0;\n            border-radius: 4px;\n          }\n\n          .favicon-preview span {\n            font-size: 0.875rem;\n            color: #475569;\n            font-weight: 500;\n          }\n\n          .favicon-upload {\n            display: flex;\n            justify-content: center;\n          }\n\n          .favicon-upload .upload-button {\n            display: inline-flex;\n            align-items: center;\n            gap: 0.5rem;\n            padding: 0.75rem 1.5rem;\n            background-color: #3b82f6;\n            color: white;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-size: 0.875rem;\n            font-weight: 500;\n            transition: background-color 0.2s;\n          }\n\n          .favicon-upload .upload-button:hover {\n            background-color: #2563eb;\n          }\n        `}</style>\n    </div>\n  );\n};\n\nexport default GeneralSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AAC1E,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,aAAa,EAAEC,cAAc,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAc,CAAC,GAAGR,aAAa,CAAC,CAAC;EACzC,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC;IACrCmB,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,UAAU,EAAE,EAAE;IACdC,gBAAgB,EAAE,EAAE;IACpBC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC2C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAErDC,SAAS,CAAC,MAAM;IACZoD,aAAa,CAAC,CAAC;IACfC,YAAY,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACArB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMuB,QAAQ,GAAG,MAAMrD,KAAK,CAACsD,GAAG,CAAC,GAAG3C,YAAY,oCAAoC,CAAC;MAErF,IAAI0C,QAAQ,CAACE,IAAI,CAACpB,OAAO,IAAIkB,QAAQ,CAACE,IAAI,CAACxC,QAAQ,EAAE;QACjD,MAAMyC,iBAAiB,GAAG,CAAC,CAAC;QAC5BC,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACE,IAAI,CAACxC,QAAQ,CAAC,CAAC4C,OAAO,CAACC,GAAG,IAAI;UAC/CJ,iBAAiB,CAACI,GAAG,CAAC,GAAGP,QAAQ,CAACE,IAAI,CAACxC,QAAQ,CAAC6C,GAAG,CAAC,CAACC,KAAK;QAC9D,CAAC,CAAC;QACF7C,WAAW,CAACwC,iBAAiB,CAAC;;QAE9B;QACA,IAAIA,iBAAiB,CAACtC,SAAS,EAAE;UAC7BoB,cAAc,CAAC,GAAG3B,YAAY,IAAI6C,iBAAiB,CAACtC,SAAS,EAAE,CAAC;QACpE;;QAEA;QACA,IAAIsC,iBAAiB,CAACrC,YAAY,EAAE;UAChCuB,qBAAqB,CAAC,GAAG/B,YAAY,IAAI6C,iBAAiB,CAACrC,YAAY,EAAE,CAAC;QAC9E;MACJ;IACJ,CAAC,CAAC,OAAO2C,GAAG,EAAE;MACV5B,QAAQ,CAAC,yBAAyB,CAAC;MACnC6B,OAAO,CAAC9B,KAAK,CAAC,0BAA0B,EAAE6B,GAAG,CAAC;IAClD,CAAC,SAAS;MACNhC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMsB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMrD,KAAK,CAACsD,GAAG,CAAC,GAAG3C,YAAY,2BAA2B,CAAC;MAC5E,IAAI0C,QAAQ,CAACE,IAAI,CAACpB,OAAO,IAAIkB,QAAQ,CAACE,IAAI,CAACS,YAAY,EAAE;QACrDlB,iBAAiB,CAAC,GAAGnC,YAAY,IAAI0C,QAAQ,CAACE,IAAI,CAACS,YAAY,EAAE,CAAC;MACtE,CAAC,MAAM,IAAIX,QAAQ,CAACE,IAAI,CAACU,qBAAqB,EAAE;QAC5CnB,iBAAiB,CAAC,cAAc,CAAC;MACrC;IACJ,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACVC,OAAO,CAAC9B,KAAK,CAAC,yBAAyB,EAAE6B,GAAG,CAAC;MAC7C;MACAhB,iBAAiB,CAAC,cAAc,CAAC;IACrC;EACJ,CAAC;EAED,MAAMoB,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEP;IAAM,CAAC,GAAGM,CAAC,CAACE,MAAM;IAChCrD,WAAW,CAACsD,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACF,IAAI,GAAGP;IACZ,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMU,gBAAgB,GAAIJ,CAAC,IAAK;IAC5B,MAAMK,IAAI,GAAGL,CAAC,CAACE,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACN;MACA,MAAME,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,CAAC;MAC9E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;QACnC1C,QAAQ,CAAC,qDAAqD,CAAC;QAC/D;MACJ;;MAEA;MACA,IAAIsC,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC7B3C,QAAQ,CAAC,iCAAiC,CAAC;QAC3C;MACJ;MAEAM,WAAW,CAACgC,IAAI,CAAC;MACjBlC,cAAc,CAACwC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC,CAAC;IAC7C;EACJ,CAAC;EAED,MAAMQ,mBAAmB,GAAIb,CAAC,IAAK;IAC/B,MAAMK,IAAI,GAAGL,CAAC,CAACE,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACN;MACA,MAAME,YAAY,GAAG,CAAC,cAAc,EAAE,0BAA0B,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,eAAe,CAAC;MACrJ,MAAMO,iBAAiB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;MAC9D,MAAMC,aAAa,GAAGV,IAAI,CAACJ,IAAI,CAACe,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAE9D,IAAI,CAACX,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,IAAI,CAACK,iBAAiB,CAACN,QAAQ,CAACO,aAAa,CAAC,EAAE;QACjFhD,QAAQ,CAAC,4DAA4D,CAAC;QACtE;MACJ;;MAEA;MACA,IAAIsC,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC7B3C,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACJ;MAEAc,cAAc,CAACwB,IAAI,CAAC;MACpB1B,iBAAiB,CAACgC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC,CAAC;IAChD;EACJ,CAAC;EAED,MAAMc,uBAAuB,GAAInB,CAAC,IAAK;IACnC,MAAMK,IAAI,GAAGL,CAAC,CAACE,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACN;MACA,MAAME,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,CAAC;MAC9E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;QACnC1C,QAAQ,CAAC,qDAAqD,CAAC;QAC/D;MACJ;;MAEA;MACA,IAAIsC,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC7B3C,QAAQ,CAAC,iCAAiC,CAAC;QAC3C;MACJ;MAEAU,kBAAkB,CAAC4B,IAAI,CAAC;MACxB9B,qBAAqB,CAACoC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC,CAAC;IACpD;EACJ,CAAC;EAEH,MAAMe,YAAY,GAAG,MAAOpB,CAAC,IAAK;IAChCA,CAAC,CAACqB,cAAc,CAAC,CAAC;IAClB,IAAI;MACFxD,SAAS,CAAC,IAAI,CAAC;MACfE,QAAQ,CAAC,EAAE,CAAC;MACZE,UAAU,CAAC,EAAE,CAAC;;MAEd;MACA,IAAIqD,eAAe,GAAG;QAAE,GAAG1E;MAAS,CAAC;MAErC,IAAIwB,QAAQ,EAAE;QACZ,MAAMmD,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAErD,QAAQ,CAAC;QAEjC,MAAMsD,cAAc,GAAG,MAAM7F,KAAK,CAAC8F,IAAI,CAAC,GAAGnF,YAAY,2BAA2B,EAAE+E,QAAQ,EAAE;UAC5FK,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIF,cAAc,CAACtC,IAAI,CAACpB,OAAO,EAAE;UAC/BsD,eAAe,CAACvE,SAAS,GAAG2E,cAAc,CAACtC,IAAI,CAACyC,SAAS;QAC3D,CAAC,MAAM;UACL,MAAM,IAAIC,KAAK,CAACJ,cAAc,CAACtC,IAAI,CAAC2C,OAAO,IAAI,uBAAuB,CAAC;QACzE;MACF;;MAEA;MACA,IAAInD,WAAW,EAAE;QACf,MAAMoD,eAAe,GAAG,IAAIR,QAAQ,CAAC,CAAC;QACtCQ,eAAe,CAACP,MAAM,CAAC,SAAS,EAAE7C,WAAW,CAAC;QAE9C,MAAMqD,qBAAqB,GAAG,MAAMpG,KAAK,CAAC8F,IAAI,CAAC,GAAGnF,YAAY,8BAA8B,EAAEwF,eAAe,EAAE;UAC7GJ,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIK,qBAAqB,CAAC7C,IAAI,CAACpB,OAAO,EAAE;UACtC;UACA4B,OAAO,CAACsC,GAAG,CAAC,gCAAgC,EAAED,qBAAqB,CAAC7C,IAAI,CAACyC,SAAS,CAAC;;UAEnF;UACA,MAAMM,aAAa,GAAG,GAAG3F,YAAY,IAAIyF,qBAAqB,CAAC7C,IAAI,CAACyC,SAAS,EAAE;UAC/EzF,aAAa,CAAC+F,aAAa,CAAC;;UAE5B;UACAxD,iBAAiB,CAACwD,aAAa,CAAC;QAClC,CAAC,MAAM;UACL,MAAM,IAAIL,KAAK,CAACG,qBAAqB,CAAC7C,IAAI,CAAC2C,OAAO,IAAI,0BAA0B,CAAC;QACnF;MACF;;MAEA;MACA,IAAIvD,eAAe,EAAE;QACnB,MAAM4D,mBAAmB,GAAG,IAAIZ,QAAQ,CAAC,CAAC;QAC1CY,mBAAmB,CAACX,MAAM,CAAC,cAAc,EAAEjD,eAAe,CAAC;QAE3D,MAAM6D,yBAAyB,GAAG,MAAMxG,KAAK,CAAC8F,IAAI,CAAC,GAAGnF,YAAY,mCAAmC,EAAE4F,mBAAmB,EAAE;UAC1HR,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIS,yBAAyB,CAACjD,IAAI,CAACpB,OAAO,EAAE;UAC1CsD,eAAe,CAACtE,YAAY,GAAGqF,yBAAyB,CAACjD,IAAI,CAACyC,SAAS;QACzE,CAAC,MAAM;UACL,MAAM,IAAIC,KAAK,CAACO,yBAAyB,CAACjD,IAAI,CAAC2C,OAAO,IAAI,+BAA+B,CAAC;QAC5F;MACF;;MAEA;MACA,MAAM7C,QAAQ,GAAG,MAAMrD,KAAK,CAAC8F,IAAI,CAAC,GAAGnF,YAAY,uCAAuC,EAAE;QACxFI,QAAQ,EAAE0E;MACZ,CAAC,CAAC;MAEF,IAAIpC,QAAQ,CAACE,IAAI,CAACpB,OAAO,EAAE;QACzBC,UAAU,CAAC,8BAA8B,CAAC;QAC1CqE,UAAU,CAAC,MAAMrE,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QACtC;QACApB,WAAW,CAACyE,eAAe,CAAC;QAC5B;QACA3E,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL,MAAM,IAAImF,KAAK,CAAC5C,QAAQ,CAACE,IAAI,CAAC2C,OAAO,IAAI,iCAAiC,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOpC,GAAG,EAAE;MACZ5B,QAAQ,CAAC4B,GAAG,CAACoC,OAAO,IAAI,4CAA4C,CAAC;MACrEnC,OAAO,CAAC9B,KAAK,CAAC,wBAAwB,EAAE6B,GAAG,CAAC;IAC9C,CAAC,SAAS;MACR9B,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAM0E,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFxD,cAAc,CAAC,IAAI,CAAC;MACpBhB,QAAQ,CAAC,EAAE,CAAC;MACZE,UAAU,CAAC,EAAE,CAAC;MAEd,MAAMiB,QAAQ,GAAG,MAAMrD,KAAK,CAAC8F,IAAI,CAAC,GAAGnF,YAAY,yBAAyB,EAAE;QAC1EgG,IAAI,EAAE,oBAAoB;QAC1BC,IAAI,EAAE,GAAG;QACTC,QAAQ,EAAE,2BAA2B;QACrCC,QAAQ,EAAE,iBAAiB;QAC3BC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE,2BAA2B;QACvCC,SAAS,EAAE;MACb,CAAC,CAAC;MAEF,IAAI5D,QAAQ,CAACE,IAAI,CAACpB,OAAO,EAAE;QACzBC,UAAU,CAAC,kCAAkC,CAAC;MAChD,CAAC,MAAM;QACLF,QAAQ,CAACmB,QAAQ,CAACE,IAAI,CAAC2C,OAAO,IAAI,6BAA6B,CAAC;MAClE;IACF,CAAC,CAAC,OAAOpC,GAAG,EAAE;MACZ5B,QAAQ,CAAC,gCAAgC,CAAC;MAC1C6B,OAAO,CAAC9B,KAAK,CAAC,qBAAqB,EAAE6B,GAAG,CAAC;IAC3C,CAAC,SAAS;MACRZ,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAEC,IAAIrB,OAAO,EAAE;IACT,oBACInB,OAAA;MAAKwG,SAAS,EAAC,KAAK;MAAAC,QAAA,eAChBzG,OAAA;QAAKwG,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eAClDzG,OAAA;UAAKwG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACI7G,OAAA;IAAKwG,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAEhBzG,OAAA;MAAKwG,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBzG,OAAA;QAAIwG,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACpEzG,OAAA,CAACT,KAAK;UAACiH,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL7G,OAAA;QAAGwG,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLtF,KAAK,iBACFvB,OAAA;MAAKwG,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBACxFzG,OAAA,CAACP,OAAO;QAAC+G,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpC7G,OAAA;QAAMwG,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAElF;MAAK;QAAAmF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CACR,EAEApF,OAAO,iBACJzB,OAAA;MAAKwG,SAAS,EAAC,iFAAiF;MAAAC,QAAA,gBAC5FzG,OAAA,CAACR,OAAO;QAACgH,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtC7G,OAAA;QAAMwG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAEhF;MAAO;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CACR,eAEL7G,OAAA;MAAM8G,QAAQ,EAAEjC,YAAa;MAAC2B,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAClDzG,OAAA;QAAKwG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BzG,OAAA;UAAAyG,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE1B7G,OAAA;UAAKwG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzG,OAAA;YAAO+G,OAAO,EAAC,WAAW;YAAAN,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5C7G,OAAA;YACEkE,IAAI,EAAC,MAAM;YACX8C,EAAE,EAAC,WAAW;YACdtD,IAAI,EAAC,WAAW;YAChBP,KAAK,EAAE9C,QAAQ,CAACE,SAAU;YAC1B0G,QAAQ,EAAEzD,iBAAkB;YAC5B0D,QAAQ;YACRC,WAAW,EAAC;UAAW;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7G,OAAA;UAAKwG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzG,OAAA;YAAO+G,OAAO,EAAC,WAAW;YAAAN,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5C7G,OAAA;YAAKwG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GACnC9E,WAAW,iBACV3B,OAAA;cAAKwG,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BzG,OAAA;gBAAKoH,GAAG,EAAEzF,WAAY;gBAAC0F,GAAG,EAAC;cAAW;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CACN,eACD7G,OAAA;cAAKwG,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BzG,OAAA;gBAAO+G,OAAO,EAAC,WAAW;gBAACP,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAClDzG,OAAA,CAACL,QAAQ;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBACd;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7G,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACX8C,EAAE,EAAC,WAAW;gBACdM,MAAM,EAAC,SAAS;gBAChBL,QAAQ,EAAEpD,gBAAiB;gBAC3B0D,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAO;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7G,OAAA;UAAKwG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzG,OAAA;YAAO+G,OAAO,EAAC,cAAc;YAAAN,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnE7G,OAAA;YAAKwG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCzG,OAAA;cAAKwG,SAAS,EAAC,WAAW;cAAAC,QAAA,eACxBzG,OAAA;gBAAGwG,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAEhC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EACL9E,kBAAkB,iBACjB/B,OAAA;cAAKwG,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BzG,OAAA;gBAAKoH,GAAG,EAAErF,kBAAmB;gBAACsF,GAAG,EAAC,cAAc;gBAACE,KAAK,EAAE;kBAACE,eAAe,EAAE,SAAS;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,YAAY,EAAE;gBAAK;cAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3H,CACN,eACD7G,OAAA;cAAKwG,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BzG,OAAA;gBAAO+G,OAAO,EAAC,mBAAmB;gBAACP,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC1DzG,OAAA,CAACL,QAAQ;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wBACd;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7G,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACX8C,EAAE,EAAC,mBAAmB;gBACtBM,MAAM,EAAC,SAAS;gBAChBL,QAAQ,EAAErC,uBAAwB;gBAClC2C,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAO;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7G,OAAA;UAAKwG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzG,OAAA;YAAO+G,OAAO,EAAC,cAAc;YAAAN,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClD7G,OAAA;YAAKwG,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCzG,OAAA;cAAKwG,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BzG,OAAA;gBAAGwG,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAEnC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EACL1E,cAAc,iBACbnC,OAAA;cAAKwG,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BzG,OAAA;gBAAKoH,GAAG,EAAEjF,cAAe;gBAACkF,GAAG,EAAC,cAAc;gBAACE,KAAK,EAAE;kBAACK,KAAK,EAAE,MAAM;kBAAEC,MAAM,EAAE;gBAAM;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvF7G,OAAA;gBAAAyG,QAAA,EAAM;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CACN,eACD7G,OAAA;cAAKwG,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BzG,OAAA;gBAAO+G,OAAO,EAAC,cAAc;gBAACP,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBACrDzG,OAAA,CAACL,QAAQ;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mBACd;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7G,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACX8C,EAAE,EAAC,cAAc;gBACjBM,MAAM,EAAC,2BAA2B;gBAClCL,QAAQ,EAAE3C,mBAAoB;gBAC9BiD,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAO;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7G,OAAA;QAAKwG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BzG,OAAA;UAAAyG,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE5B7G,OAAA;UAAKwG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzG,OAAA;YAAO+G,OAAO,EAAC,eAAe;YAAAN,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpD7G,OAAA;YACEkE,IAAI,EAAC,OAAO;YACZ8C,EAAE,EAAC,eAAe;YAClBtD,IAAI,EAAC,eAAe;YACpBP,KAAK,EAAE9C,QAAQ,CAACK,aAAc;YAC9BuG,QAAQ,EAAEzD,iBAAkB;YAC5B0D,QAAQ;YACRC,WAAW,EAAC;UAAuB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7G,OAAA;UAAKwG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzG,OAAA;YAAO+G,OAAO,EAAC,eAAe;YAAAN,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpD7G,OAAA;YACEkE,IAAI,EAAC,MAAM;YACX8C,EAAE,EAAC,eAAe;YAClBtD,IAAI,EAAC,eAAe;YACpBP,KAAK,EAAE9C,QAAQ,CAACM,aAAc;YAC9BsG,QAAQ,EAAEzD,iBAAkB;YAC5B2D,WAAW,EAAC;UAAa;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7G,OAAA;QAAKwG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BzG,OAAA;UAAAyG,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAErB7G,OAAA;UAAKwG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzG,OAAA;YAAO+G,OAAO,EAAC,cAAc;YAAAN,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClD7G,OAAA;YACEkE,IAAI,EAAC,KAAK;YACV8C,EAAE,EAAC,cAAc;YACjBtD,IAAI,EAAC,cAAc;YACnBP,KAAK,EAAE9C,QAAQ,CAACO,YAAa;YAC7BqG,QAAQ,EAAEzD,iBAAkB;YAC5B2D,WAAW,EAAC;UAAgC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7G,OAAA;UAAKwG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzG,OAAA;YAAO+G,OAAO,EAAC,aAAa;YAAAN,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChD7G,OAAA;YACEkE,IAAI,EAAC,KAAK;YACV8C,EAAE,EAAC,aAAa;YAChBtD,IAAI,EAAC,aAAa;YAClBP,KAAK,EAAE9C,QAAQ,CAACQ,WAAY;YAC5BoG,QAAQ,EAAEzD,iBAAkB;YAC5B2D,WAAW,EAAC;UAA+B;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7G,OAAA;UAAKwG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzG,OAAA;YAAO+G,OAAO,EAAC,eAAe;YAAAN,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpD7G,OAAA;YACEkE,IAAI,EAAC,KAAK;YACV8C,EAAE,EAAC,eAAe;YAClBtD,IAAI,EAAC,eAAe;YACpBP,KAAK,EAAE9C,QAAQ,CAACS,aAAc;YAC9BmG,QAAQ,EAAEzD,iBAAkB;YAC5B2D,WAAW,EAAC;UAAiC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7G,OAAA;QAAKwG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BzG,OAAA;UAAAyG,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEhB7G,OAAA;UAAKwG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzG,OAAA;YAAO+G,OAAO,EAAC,YAAY;YAAAN,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9C7G,OAAA;YACEgH,EAAE,EAAC,YAAY;YACftD,IAAI,EAAC,YAAY;YACjBP,KAAK,EAAE9C,QAAQ,CAACU,UAAW;YAC3BkG,QAAQ,EAAEzD,iBAAkB;YAC5BsE,IAAI,EAAC,GAAG;YACRX,WAAW,EAAC;UAAoB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEN7G,OAAA;UAAKwG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzG,OAAA;YAAO+G,OAAO,EAAC,kBAAkB;YAAAN,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5D7G,OAAA;YACEgH,EAAE,EAAC,kBAAkB;YACrBtD,IAAI,EAAC,kBAAkB;YACvBP,KAAK,EAAE9C,QAAQ,CAACW,gBAAiB;YACjCiG,QAAQ,EAAEzD,iBAAkB;YAC5BsE,IAAI,EAAC,GAAG;YACRX,WAAW,EAAC;UAAwC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEN7G,OAAA;UAAKwG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzG,OAAA;YAAO+G,OAAO,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtD7G,OAAA;YACEgH,EAAE,EAAC,gBAAgB;YACnBtD,IAAI,EAAC,gBAAgB;YACrBP,KAAK,EAAE9C,QAAQ,CAACY,cAAe;YAC/BgG,QAAQ,EAAEzD,iBAAkB;YAC5BsE,IAAI,EAAC,GAAG;YACRX,WAAW,EAAC;UAAkC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEN7G,OAAA;UAAKwG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzG,OAAA;YAAO+G,OAAO,EAAC,aAAa;YAAAN,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChD7G,OAAA;YACEkE,IAAI,EAAC,MAAM;YACX8C,EAAE,EAAC,aAAa;YAChBtD,IAAI,EAAC,aAAa;YAClBP,KAAK,EAAE9C,QAAQ,CAACa,WAAY;YAC5B+F,QAAQ,EAAEzD,iBAAkB;YAC5B2D,WAAW,EAAC;UAAwC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7G,OAAA;QAAKwG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BzG,OAAA;UAAAyG,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClB7G,OAAA;UAAAyG,QAAA,EAAG;QAA8E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrF7G,OAAA;UACEkE,IAAI,EAAC,QAAQ;UACb6D,OAAO,EAAE/B,kBAAmB;UAC5BQ,SAAS,EAAC,mBAAmB;UAC7BwB,QAAQ,EAAEzF,WAAY;UAAAkE,QAAA,EAErBlE,WAAW,GAAG,YAAY,GAAG;QAAsB;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN7G,OAAA;QAAKwG,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BzG,OAAA;UACEkE,IAAI,EAAC,QAAQ;UACbsC,SAAS,EAAC,iBAAiB;UAC3BwB,QAAQ,EAAE3G,MAAO;UAAAoF,QAAA,EAEhBpF,MAAM,GAAG,WAAW,GAAG;QAAe;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEP7G,OAAA;MAAOiI,GAAG;MAAAxB,QAAA,EAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV;AAAC1G,EAAA,CAhnBQD,eAAe;EAAA,QACMN,aAAa;AAAA;AAAAsI,EAAA,GADlChI,eAAe;AAgnBvB;AAED,eAAeA,eAAe;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}