{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\CreditChallenge.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';\nimport axios from 'axios';\nimport AlertContainer from '../components/AlertContainer';\nimport './CreditChallenge.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nconst initialFormState = {\n  selectedChallenge: '',\n  teamAScore: 0,\n  teamBScore: 0,\n  selectedWinner: '',\n  selectedLoser: '',\n  newStatus: 'Settled',\n  oddsTeamA: 0,\n  oddsTeamB: 0,\n  oddsDraw: 0,\n  oddsLost: 0\n};\nfunction CreditChallenge() {\n  _s();\n  const [challenges, setChallenges] = useState([]);\n  const [formState, setFormState] = useState(initialFormState);\n  const [loading, setLoading] = useState(false);\n  const [betsPreview, setBetsPreview] = useState(null);\n  const [alerts, setAlerts] = useState([]);\n  const [leagueDetails, setLeagueDetails] = useState(null);\n  const [payoutPreviews, setPayoutPreviews] = useState({});\n  const addAlert = (message, type = 'success', duration = 3000) => {\n    const id = Date.now();\n    setAlerts(prev => [...prev, {\n      id,\n      message,\n      type,\n      duration\n    }]);\n  };\n  const removeAlert = id => {\n    setAlerts(prev => prev.filter(alert => alert.id !== id));\n  };\n  const fetchOpenChallenges = useCallback(async () => {\n    try {\n      const response = await axios.get('challenge_management.php', {\n        params: {\n          status: 'Closed'\n        }\n      });\n      if (response.data.success) {\n        const closedChallenges = response.data.challenges.filter(c => c.status === 'Closed').map(challenge => ({\n          ...challenge,\n          match_date: new Date(challenge.match_date).toLocaleDateString(),\n          isLeague: Boolean(challenge.league_id)\n        }));\n        setChallenges(closedChallenges);\n      } else {\n        addAlert('Failed to fetch challenges', 'error');\n      }\n    } catch (err) {\n      console.error('Error fetching challenges:', err);\n      addAlert('Failed to fetch challenges', 'error');\n    }\n  }, []);\n  const fetchBetsPreview = useCallback(async challengeId => {\n    if (!challengeId) return;\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_challenge_bets.php`, {\n        params: {\n          challenge_id: challengeId\n        }\n      });\n      if (response.data.success) {\n        setBetsPreview(response.data.bets);\n        // Fetch league details for involved users\n        const uniqueUserIds = new Set(response.data.bets.flatMap(bet => [bet.user1_id, bet.user2_id]));\n        await fetchLeagueDetails(Array.from(uniqueUserIds));\n      }\n    } catch (err) {\n      console.error('Error fetching bets preview:', err);\n      addAlert('Failed to load bet details', 'error');\n    }\n  }, []);\n  const fetchLeagueDetails = async userIds => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/league_details.php`, {\n        params: {\n          user_ids: userIds.join(',')\n        }\n      });\n      if (response.data.success) {\n        setLeagueDetails(response.data.leagueDetails);\n      }\n    } catch (err) {\n      console.error('Error fetching league details:', err);\n      addAlert('Failed to load league information', 'error');\n    }\n  };\n  useEffect(() => {\n    fetchOpenChallenges();\n  }, [fetchOpenChallenges]);\n  const selectedChallenge = useMemo(() => challenges.find(c => c.challenge_id === parseInt(formState.selectedChallenge)), [challenges, formState.selectedChallenge]);\n  useEffect(() => {\n    if (selectedChallenge) {\n      setFormState(prev => ({\n        ...prev,\n        oddsTeamA: selectedChallenge.odds_team_a || 0,\n        oddsTeamB: selectedChallenge.odds_team_b || 0,\n        oddsDraw: selectedChallenge.odds_draw || 0,\n        oddsLost: selectedChallenge.odds_lost || 0\n      }));\n      fetchBetsPreview(selectedChallenge.challenge_id);\n    } else {\n      setBetsPreview(null);\n    }\n  }, [selectedChallenge, fetchBetsPreview]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormState(prev => ({\n      ...prev,\n      [name]: name.includes('Score') || name.includes('odds') ? parseFloat(value) || 0 : value\n    }));\n  };\n  const resetForm = () => {\n    setFormState(initialFormState);\n    setBetsPreview(null);\n  };\n  const handleCompleteChallenge = async e => {\n    e.preventDefault();\n    if (!formState.selectedWinner) {\n      addAlert('Please select a winner', 'error');\n      return;\n    }\n    if (formState.selectedWinner !== 'draw' && !formState.selectedLoser) {\n      addAlert('Please select a loser', 'error');\n      return;\n    }\n    if (formState.selectedWinner !== 'draw' && formState.selectedWinner === formState.selectedLoser) {\n      addAlert('Winner and loser cannot be the same team', 'error');\n      return;\n    }\n    setLoading(true);\n    try {\n      const formData = new FormData();\n      formData.append('challenge_id', formState.selectedChallenge);\n      formData.append('team_a_score', formState.teamAScore);\n      formData.append('team_b_score', formState.teamBScore);\n      formData.append('winner', formState.selectedWinner);\n      formData.append('loser', formState.selectedLoser);\n      formData.append('new_status', formState.newStatus);\n      const response = await axios.post(`${API_BASE_URL}/handlers/complete_challenge.php`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data.success) {\n        addAlert('Challenge settled successfully!');\n        fetchOpenChallenges();\n        resetForm();\n      } else {\n        addAlert(response.data.message || 'Failed to complete challenge', 'error');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      const errorMsg = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Failed to complete challenge';\n      addAlert(errorMsg, 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const calculatePotentialPayout = (bet, choice) => {\n    if (!formState.selectedWinner) return null;\n\n    // For draw outcome\n    if (formState.selectedWinner === 'draw') {\n      if (choice === 'draw') {\n        return bet.potential_returns_user1.draw;\n      } else {\n        return (bet.amount_user1 * selectedChallenge.odds_lost).toFixed(2);\n      }\n    }\n\n    // For win/loss outcomes\n    const isWinning = formState.selectedWinner === choice;\n    if (isWinning) {\n      switch (choice) {\n        case 'team_a_win':\n        case 'team_b_win':\n          return bet.potential_returns_user1.win;\n        default:\n          return null;\n      }\n    } else {\n      return (bet.amount_user1 * selectedChallenge.odds_lost).toFixed(2);\n    }\n  };\n  const calculateGoalAdvantage = (teamAScore, teamBScore) => {\n    const difference = teamAScore - teamBScore;\n    if (difference > 0) {\n      return {\n        value: `+${difference}`,\n        class: 'goal-advantage-positive'\n      };\n    } else if (difference < 0) {\n      return {\n        value: difference.toString(),\n        class: 'goal-advantage-negative'\n      };\n    }\n    return {\n      value: '0',\n      class: 'goal-advantage-neutral'\n    };\n  };\n  const renderLeaguePanel = () => {\n    if (!leagueDetails || !(selectedChallenge !== null && selectedChallenge !== void 0 && selectedChallenge.isLeague)) return null;\n    return /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"league-info-panel\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"heading-container\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"page-heading\",\n          children: \"League Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"league-info-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: leagueDetails.league_logo || '/default-league-logo.png',\n          alt: \"League Logo\",\n          className: \"league-logo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"league-name\",\n          children: leagueDetails.league_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"league-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Wins\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-value\",\n            children: leagueDetails.wins || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Draws\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-value\",\n            children: leagueDetails.draws || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Losses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-value\",\n            children: leagueDetails.losses || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"league-position\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"Current Position\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          children: leagueDetails.position || 'N/A'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 13\n    }, this);\n  };\n  const renderScoreDisplay = () => {\n    if (!selectedChallenge) return null;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"score-display\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"team-score-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"team-name\",\n          children: selectedChallenge.team_a\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          name: \"teamAScore\",\n          value: formState.teamAScore,\n          onChange: handleInputChange,\n          min: \"0\",\n          className: \"score-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"vs-indicator\",\n        children: \"VS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"team-score-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"team-name\",\n          children: selectedChallenge.team_b\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          name: \"teamBScore\",\n          value: formState.teamBScore,\n          onChange: handleInputChange,\n          min: \"0\",\n          className: \"score-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 13\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"credit-challenge-container\",\n    children: [renderLeaguePanel(), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"credit-challenge-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"challenge-form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"heading-container\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"page-heading\",\n            children: \"Settle Challenge\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleCompleteChallenge,\n          className: \"form-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Select Challenge:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"selectedChallenge\",\n              value: formState.selectedChallenge,\n              onChange: handleInputChange,\n              className: \"form-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select a challenge...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 33\n              }, this), challenges.map(challenge => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: challenge.challenge_id,\n                children: [challenge.team_a, \" vs \", challenge.team_b, challenge.isLeague ? ' (League Match)' : '', \" -\", challenge.match_date]\n              }, challenge.challenge_id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 37\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 25\n          }, this), selectedChallenge && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [renderScoreDisplay(), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Select Winner:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"selectedWinner\",\n                  value: formState.selectedWinner,\n                  onChange: handleInputChange,\n                  className: \"form-select\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Winner\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"team_a_win\",\n                    children: selectedChallenge.team_a\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"team_b_win\",\n                    children: selectedChallenge.team_b\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"draw\",\n                    children: \"Draw\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"Select Loser:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"selectedLoser\",\n                  value: formState.selectedLoser,\n                  onChange: handleInputChange,\n                  className: \"form-select\",\n                  disabled: formState.selectedWinner === 'draw',\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Loser\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 45\n                  }, this), formState.selectedWinner !== 'team_a_win' && /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"team_a_win\",\n                    children: selectedChallenge.team_a\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 49\n                  }, this), formState.selectedWinner !== 'team_b_win' && /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"team_b_win\",\n                    children: selectedChallenge.team_b\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Challenge Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"newStatus\",\n                value: formState.newStatus,\n                onChange: handleInputChange,\n                className: \"form-select\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Settled\",\n                  children: \"Settled\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Completed\",\n                  children: \"Completed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Cancelled\",\n                  children: \"Cancelled\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: `submit-button ${loading ? 'loading' : ''}`,\n              disabled: loading,\n              children: loading ? 'Processing...' : 'Settle Challenge'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"live-preview\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"heading-container\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"page-heading\",\n            children: \"Live Preview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 21\n        }, this), betsPreview && betsPreview.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bets-list\",\n          role: \"list\",\n          children: betsPreview.map((bet, index) => {\n            const user1Payout = calculatePotentialPayout(bet, bet.bet_choice_user1);\n            const user2Payout = calculatePotentialPayout(bet, bet.bet_choice_user2);\n            const user1PayoutClass = user1Payout > bet.amount_user1 ? 'winning' : 'losing';\n            const user2PayoutClass = user2Payout > bet.amount_user2 ? 'winning' : 'losing';\n            const goalAdvantage = calculateGoalAdvantage(formState.teamAScore, formState.teamBScore);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bet-item\",\n              role: \"listitem\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"score-summary\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"admin-score\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"admin-score-label\",\n                    children: \"Final Score:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"admin-score-value\",\n                    children: [formState.teamAScore, \" - \", formState.teamBScore]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `goal-advantage ${goalAdvantage.class}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Goal Advantage:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: goalAdvantage.value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bet-users-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `bet-user ${user1PayoutClass}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bet-user-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"user-avatar\",\n                      \"aria-hidden\": \"true\",\n                      children: bet.user1_name.charAt(0).toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 425,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"user-name\",\n                      children: bet.user1_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bet-details\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"bet-choice\",\n                      children: [\"Choice: \", bet.bet_choice_user1]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 431,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"bet-amount\",\n                      children: [\"Stake: \", bet.amount_user1]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 434,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"payout-preview\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"payout-label\",\n                      children: \"Payout:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 439,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"payout-value\",\n                      children: user1Payout\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `bet-user ${user2PayoutClass}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bet-user-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"user-avatar\",\n                      \"aria-hidden\": \"true\",\n                      children: bet.user2_name.charAt(0).toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 446,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"user-name\",\n                      children: bet.user2_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 449,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bet-details\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"bet-choice\",\n                      children: [\"Choice: \", bet.bet_choice_user2]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"bet-amount\",\n                      children: [\"Stake: \", bet.amount_user2]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"payout-preview\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"payout-label\",\n                      children: \"Payout:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 460,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"payout-value\",\n                      children: user2Payout\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 41\n              }, this)]\n            }, bet.bet_id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 37\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-bets-message\",\n          role: \"status\",\n          children: selectedChallenge ? 'No bets found for this challenge' : 'Select a challenge to view bets'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(AlertContainer, {\n      alerts: alerts,\n      removeAlert: removeAlert\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 295,\n    columnNumber: 9\n  }, this);\n}\n_s(CreditChallenge, \"FhJMtG3WcKbADPg7391r1bKwfSU=\");\n_c = CreditChallenge;\nexport default CreditChallenge;\nvar _c;\n$RefreshReg$(_c, \"CreditChallenge\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "useRef", "axios", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE_URL", "initialFormState", "selected<PERSON>hall<PERSON><PERSON>", "teamAScore", "teamBScore", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "newStatus", "oddsTeamA", "oddsTeamB", "oddsDraw", "oddsLost", "CreditChallenge", "_s", "challenges", "setChallenges", "formState", "setFormState", "loading", "setLoading", "betsPreview", "setBetsPreview", "alerts", "<PERSON><PERSON><PERSON><PERSON>", "leagueDetails", "setLeagueDetails", "payoutPreviews", "setPayoutPreviews", "add<PERSON><PERSON><PERSON>", "message", "type", "duration", "id", "Date", "now", "prev", "<PERSON><PERSON><PERSON><PERSON>", "filter", "alert", "fetchOpenChallenges", "response", "get", "params", "status", "data", "success", "closedChallenges", "c", "map", "challenge", "match_date", "toLocaleDateString", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "league_id", "err", "console", "error", "fetchBetsPreview", "challengeId", "challenge_id", "bets", "uniqueUserIds", "Set", "flatMap", "bet", "user1_id", "user2_id", "fetchLeagueDetails", "Array", "from", "userIds", "user_ids", "join", "find", "parseInt", "odds_team_a", "odds_team_b", "odds_draw", "odds_lost", "handleInputChange", "e", "name", "value", "target", "includes", "parseFloat", "resetForm", "handleCompleteChallenge", "preventDefault", "formData", "FormData", "append", "post", "headers", "_err$response", "_err$response$data", "errorMsg", "calculatePotentialPayout", "choice", "potential_returns_user1", "draw", "amount_user1", "toFixed", "isWinning", "win", "calculateGoalAdvantage", "difference", "class", "toString", "renderLeaguePanel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "league_logo", "alt", "league_name", "wins", "draws", "losses", "position", "renderScoreDisplay", "team_a", "onChange", "min", "team_b", "onSubmit", "disabled", "length", "role", "index", "user1Payout", "bet_choice_user1", "user2Payout", "bet_choice_user2", "user1PayoutClass", "user2PayoutClass", "amount_user2", "goalAdvantage", "user1_name", "char<PERSON>t", "toUpperCase", "user2_name", "bet_id", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/CreditChallenge.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';\nimport axios from 'axios';\nimport AlertContainer from '../components/AlertContainer';\nimport './CreditChallenge.css';\n\nconst API_BASE_URL = '/backend';\n\nconst initialFormState = {\n    selectedChallenge: '',\n    teamAScore: 0,\n    teamBScore: 0,\n    selectedWinner: '',\n    selectedLoser: '',\n    newStatus: 'Settled',\n    oddsTeamA: 0,\n    oddsTeamB: 0,\n    oddsDraw: 0,\n    oddsLost: 0\n};\n\nfunction CreditChallenge() {\n    const [challenges, setChallenges] = useState([]);\n    const [formState, setFormState] = useState(initialFormState);\n    const [loading, setLoading] = useState(false);\n    const [betsPreview, setBetsPreview] = useState(null);\n    const [alerts, setAlerts] = useState([]);\n    const [leagueDetails, setLeagueDetails] = useState(null);\n    const [payoutPreviews, setPayoutPreviews] = useState({});\n\n    const addAlert = (message, type = 'success', duration = 3000) => {\n        const id = Date.now();\n        setAlerts(prev => [...prev, { id, message, type, duration }]);\n    };\n\n    const removeAlert = (id) => {\n        setAlerts(prev => prev.filter(alert => alert.id !== id));\n    };\n\n    const fetchOpenChallenges = useCallback(async () => {\n        try {\n            const response = await axios.get('challenge_management.php', {\n                params: { status: 'Closed' }\n            });\n            \n            if (response.data.success) {\n                const closedChallenges = response.data.challenges\n                    .filter(c => c.status === 'Closed')\n                    .map(challenge => ({\n                        ...challenge,\n                        match_date: new Date(challenge.match_date).toLocaleDateString(),\n                        isLeague: Boolean(challenge.league_id)\n                    }));\n                setChallenges(closedChallenges);\n            } else {\n                addAlert('Failed to fetch challenges', 'error');\n            }\n        } catch (err) {\n            console.error('Error fetching challenges:', err);\n            addAlert('Failed to fetch challenges', 'error');\n        }\n    }, []);\n\n    const fetchBetsPreview = useCallback(async (challengeId) => {\n        if (!challengeId) return;\n        \n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_challenge_bets.php`, {\n                params: { challenge_id: challengeId }\n            });\n            \n            if (response.data.success) {\n                setBetsPreview(response.data.bets);\n                // Fetch league details for involved users\n                const uniqueUserIds = new Set(\n                    response.data.bets.flatMap(bet => [bet.user1_id, bet.user2_id])\n                );\n                await fetchLeagueDetails(Array.from(uniqueUserIds));\n            }\n        } catch (err) {\n            console.error('Error fetching bets preview:', err);\n            addAlert('Failed to load bet details', 'error');\n        }\n    }, []);\n\n    const fetchLeagueDetails = async (userIds) => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/league_details.php`, {\n                params: { user_ids: userIds.join(',') }\n            });\n            \n            if (response.data.success) {\n                setLeagueDetails(response.data.leagueDetails);\n            }\n        } catch (err) {\n            console.error('Error fetching league details:', err);\n            addAlert('Failed to load league information', 'error');\n        }\n    };\n\n    useEffect(() => {\n        fetchOpenChallenges();\n    }, [fetchOpenChallenges]);\n\n    const selectedChallenge = useMemo(() => \n        challenges.find(c => c.challenge_id === parseInt(formState.selectedChallenge)),\n        [challenges, formState.selectedChallenge]\n    );\n\n    useEffect(() => {\n        if (selectedChallenge) {\n            setFormState(prev => ({\n                ...prev,\n                oddsTeamA: selectedChallenge.odds_team_a || 0,\n                oddsTeamB: selectedChallenge.odds_team_b || 0,\n                oddsDraw: selectedChallenge.odds_draw || 0,\n                oddsLost: selectedChallenge.odds_lost || 0\n            }));\n            fetchBetsPreview(selectedChallenge.challenge_id);\n        } else {\n            setBetsPreview(null);\n        }\n    }, [selectedChallenge, fetchBetsPreview]);\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setFormState(prev => ({ \n            ...prev, \n            [name]: name.includes('Score') || name.includes('odds') ? parseFloat(value) || 0 : value \n        }));\n    };\n\n    const resetForm = () => {\n        setFormState(initialFormState);\n        setBetsPreview(null);\n    };\n\n    const handleCompleteChallenge = async (e) => {\n        e.preventDefault();\n        \n        if (!formState.selectedWinner) {\n            addAlert('Please select a winner', 'error');\n            return;\n        }\n\n        if (formState.selectedWinner !== 'draw' && !formState.selectedLoser) {\n            addAlert('Please select a loser', 'error');\n            return;\n        }\n\n        if (formState.selectedWinner !== 'draw' && formState.selectedWinner === formState.selectedLoser) {\n            addAlert('Winner and loser cannot be the same team', 'error');\n            return;\n        }\n\n        setLoading(true);\n        \n        try {\n            const formData = new FormData();\n            formData.append('challenge_id', formState.selectedChallenge);\n            formData.append('team_a_score', formState.teamAScore);\n            formData.append('team_b_score', formState.teamBScore);\n            formData.append('winner', formState.selectedWinner);\n            formData.append('loser', formState.selectedLoser);\n            formData.append('new_status', formState.newStatus);\n\n            const response = await axios.post(\n                `${API_BASE_URL}/handlers/complete_challenge.php`,\n                formData,\n                { headers: { 'Content-Type': 'multipart/form-data' } }\n            );\n\n            if (response.data.success) {\n                addAlert('Challenge settled successfully!');\n                fetchOpenChallenges();\n                resetForm();\n            } else {\n                addAlert(response.data.message || 'Failed to complete challenge', 'error');\n            }\n        } catch (err) {\n            const errorMsg = err.response?.data?.message || 'Failed to complete challenge';\n            addAlert(errorMsg, 'error');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const calculatePotentialPayout = (bet, choice) => {\n        if (!formState.selectedWinner) return null;\n        \n        // For draw outcome\n        if (formState.selectedWinner === 'draw') {\n            if (choice === 'draw') {\n                return bet.potential_returns_user1.draw;\n            } else {\n                return (bet.amount_user1 * selectedChallenge.odds_lost).toFixed(2);\n            }\n        }\n\n        // For win/loss outcomes\n        const isWinning = formState.selectedWinner === choice;\n        if (isWinning) {\n            switch (choice) {\n                case 'team_a_win':\n                case 'team_b_win':\n                    return bet.potential_returns_user1.win;\n                default:\n                    return null;\n            }\n        } else {\n            return (bet.amount_user1 * selectedChallenge.odds_lost).toFixed(2);\n        }\n    };\n\n    const calculateGoalAdvantage = (teamAScore, teamBScore) => {\n        const difference = teamAScore - teamBScore;\n        if (difference > 0) {\n            return { value: `+${difference}`, class: 'goal-advantage-positive' };\n        } else if (difference < 0) {\n            return { value: difference.toString(), class: 'goal-advantage-negative' };\n        }\n        return { value: '0', class: 'goal-advantage-neutral' };\n    };\n\n    const renderLeaguePanel = () => {\n        if (!leagueDetails || !selectedChallenge?.isLeague) return null;\n\n        return (\n            <section className=\"league-info-panel\">\n                <div className=\"heading-container\">\n                    <h2 className=\"page-heading\">League Information</h2>\n                </div>\n                <div className=\"league-info-header\">\n                    <img \n                        src={leagueDetails.league_logo || '/default-league-logo.png'} \n                        alt=\"League Logo\" \n                        className=\"league-logo\"\n                    />\n                    <h3 className=\"league-name\">{leagueDetails.league_name}</h3>\n                </div>\n                <div className=\"league-stats\">\n                    <div className=\"stat-item\">\n                        <div className=\"stat-label\">Wins</div>\n                        <div className=\"stat-value\">{leagueDetails.wins || 0}</div>\n                    </div>\n                    <div className=\"stat-item\">\n                        <div className=\"stat-label\">Draws</div>\n                        <div className=\"stat-value\">{leagueDetails.draws || 0}</div>\n                    </div>\n                    <div className=\"stat-item\">\n                        <div className=\"stat-label\">Losses</div>\n                        <div className=\"stat-value\">{leagueDetails.losses || 0}</div>\n                    </div>\n                </div>\n                <div className=\"league-position\">\n                    <div className=\"stat-label\">Current Position</div>\n                    <div className=\"stat-value\">{leagueDetails.position || 'N/A'}</div>\n                </div>\n            </section>\n        );\n    };\n\n    const renderScoreDisplay = () => {\n        if (!selectedChallenge) return null;\n\n        return (\n            <div className=\"score-display\">\n                <div className=\"team-score-container\">\n                    <div className=\"team-name\">{selectedChallenge.team_a}</div>\n                    <input\n                        type=\"number\"\n                        name=\"teamAScore\"\n                        value={formState.teamAScore}\n                        onChange={handleInputChange}\n                        min=\"0\"\n                        className=\"score-input\"\n                    />\n                </div>\n                <div className=\"vs-indicator\">VS</div>\n                <div className=\"team-score-container\">\n                    <div className=\"team-name\">{selectedChallenge.team_b}</div>\n                    <input\n                        type=\"number\"\n                        name=\"teamBScore\"\n                        value={formState.teamBScore}\n                        onChange={handleInputChange}\n                        min=\"0\"\n                        className=\"score-input\"\n                    />\n                </div>\n            </div>\n        );\n    };\n\n    return (\n        <div className=\"credit-challenge-container\">\n            {/* League Information Panel - Conditionally rendered */}\n            {renderLeaguePanel()}\n\n            <div className=\"credit-challenge-content\">\n                {/* Challenge Form Section */}\n                <section className=\"challenge-form-section\">\n                    <div className=\"heading-container\">\n                        <h2 className=\"page-heading\">Settle Challenge</h2>\n                    </div>\n                    \n                    <form onSubmit={handleCompleteChallenge} className=\"form-content\">\n                        <div className=\"form-group\">\n                            <label className=\"form-label\">Select Challenge:</label>\n                            <select\n                                name=\"selectedChallenge\"\n                                value={formState.selectedChallenge}\n                                onChange={handleInputChange}\n                                className=\"form-select\"\n                            >\n                                <option value=\"\">Select a challenge...</option>\n                                {challenges.map(challenge => (\n                                    <option key={challenge.challenge_id} value={challenge.challenge_id}>\n                                        {challenge.team_a} vs {challenge.team_b} \n                                        {challenge.isLeague ? ' (League Match)' : ''} - \n                                        {challenge.match_date}\n                                    </option>\n                                ))}\n                            </select>\n                        </div>\n\n                        {selectedChallenge && (\n                            <>\n                                {renderScoreDisplay()}\n\n                                <div className=\"dropdown-section\">\n                                    <div className=\"form-group\">\n                                        <label className=\"form-label\">Select Winner:</label>\n                                        <select\n                                            name=\"selectedWinner\"\n                                            value={formState.selectedWinner}\n                                            onChange={handleInputChange}\n                                            className=\"form-select\"\n                                        >\n                                            <option value=\"\">Select Winner</option>\n                                            <option value=\"team_a_win\">{selectedChallenge.team_a}</option>\n                                            <option value=\"team_b_win\">{selectedChallenge.team_b}</option>\n                                            <option value=\"draw\">Draw</option>\n                                        </select>\n                                    </div>\n\n                                    <div className=\"form-group\">\n                                        <label className=\"form-label\">Select Loser:</label>\n                                        <select\n                                            name=\"selectedLoser\"\n                                            value={formState.selectedLoser}\n                                            onChange={handleInputChange}\n                                            className=\"form-select\"\n                                            disabled={formState.selectedWinner === 'draw'}\n                                        >\n                                            <option value=\"\">Select Loser</option>\n                                            {formState.selectedWinner !== 'team_a_win' && (\n                                                <option value=\"team_a_win\">{selectedChallenge.team_a}</option>\n                                            )}\n                                            {formState.selectedWinner !== 'team_b_win' && (\n                                                <option value=\"team_b_win\">{selectedChallenge.team_b}</option>\n                                            )}\n                                        </select>\n                                    </div>\n                                </div>\n\n                                <div className=\"form-group\">\n                                    <label className=\"form-label\">Challenge Status:</label>\n                                    <select\n                                        name=\"newStatus\"\n                                        value={formState.newStatus}\n                                        onChange={handleInputChange}\n                                        className=\"form-select\"\n                                    >\n                                        <option value=\"Settled\">Settled</option>\n                                        <option value=\"Completed\">Completed</option>\n                                        <option value=\"Cancelled\">Cancelled</option>\n                                    </select>\n                                </div>\n\n                                <button \n                                    type=\"submit\" \n                                    className={`submit-button ${loading ? 'loading' : ''}`}\n                                    disabled={loading}\n                                >\n                                    {loading ? 'Processing...' : 'Settle Challenge'}\n                                </button>\n                            </>\n                        )}\n                    </form>\n                </section>\n\n                {/* Live Preview Section */}\n                <section className=\"live-preview\">\n                    <div className=\"heading-container\">\n                        <h2 className=\"page-heading\">Live Preview</h2>\n                    </div>\n                    \n                    {betsPreview && betsPreview.length > 0 ? (\n                        <div className=\"bets-list\" role=\"list\">\n                            {betsPreview.map((bet, index) => {\n                                const user1Payout = calculatePotentialPayout(bet, bet.bet_choice_user1);\n                                const user2Payout = calculatePotentialPayout(bet, bet.bet_choice_user2);\n                                const user1PayoutClass = user1Payout > bet.amount_user1 ? 'winning' : 'losing';\n                                const user2PayoutClass = user2Payout > bet.amount_user2 ? 'winning' : 'losing';\n                                const goalAdvantage = calculateGoalAdvantage(formState.teamAScore, formState.teamBScore);\n\n                                return (\n                                    <div key={bet.bet_id || index} className=\"bet-item\" role=\"listitem\">\n                                        <div className=\"score-summary\">\n                                            <div className=\"admin-score\">\n                                                <span className=\"admin-score-label\">Final Score:</span>\n                                                <span className=\"admin-score-value\">\n                                                    {formState.teamAScore} - {formState.teamBScore}\n                                                </span>\n                                            </div>\n                                            <div className={`goal-advantage ${goalAdvantage.class}`}>\n                                                <span>Goal Advantage:</span>\n                                                <span>{goalAdvantage.value}</span>\n                                            </div>\n                                        </div>\n\n                                        <div className=\"bet-users-container\">\n                                            <div className={`bet-user ${user1PayoutClass}`}>\n                                                <div className=\"bet-user-header\">\n                                                    <div className=\"user-avatar\" aria-hidden=\"true\">\n                                                        {bet.user1_name.charAt(0).toUpperCase()}\n                                                    </div>\n                                                    <span className=\"user-name\">{bet.user1_name}</span>\n                                                </div>\n                                                <div className=\"bet-details\">\n                                                    <span className=\"bet-choice\">\n                                                        Choice: {bet.bet_choice_user1}\n                                                    </span>\n                                                    <span className=\"bet-amount\">\n                                                        Stake: {bet.amount_user1}\n                                                    </span>\n                                                </div>\n                                                <div className=\"payout-preview\">\n                                                    <span className=\"payout-label\">Payout:</span>\n                                                    <span className=\"payout-value\">{user1Payout}</span>\n                                                </div>\n                                            </div>\n\n                                            <div className={`bet-user ${user2PayoutClass}`}>\n                                                <div className=\"bet-user-header\">\n                                                    <div className=\"user-avatar\" aria-hidden=\"true\">\n                                                        {bet.user2_name.charAt(0).toUpperCase()}\n                                                    </div>\n                                                    <span className=\"user-name\">{bet.user2_name}</span>\n                                                </div>\n                                                <div className=\"bet-details\">\n                                                    <span className=\"bet-choice\">\n                                                        Choice: {bet.bet_choice_user2}\n                                                    </span>\n                                                    <span className=\"bet-amount\">\n                                                        Stake: {bet.amount_user2}\n                                                    </span>\n                                                </div>\n                                                <div className=\"payout-preview\">\n                                                    <span className=\"payout-label\">Payout:</span>\n                                                    <span className=\"payout-value\">{user2Payout}</span>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                );\n                            })}\n                        </div>\n                    ) : (\n                        <div className=\"no-bets-message\" role=\"status\">\n                            {selectedChallenge ? 'No bets found for this challenge' : 'Select a challenge to view bets'}\n                        </div>\n                    )}\n                </section>\n            </div>\n            <AlertContainer alerts={alerts} removeAlert={removeAlert} />\n        </div>\n    );\n}\n\nexport default CreditChallenge;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAChF,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAMC,YAAY,GAAG,UAAU;AAE/B,MAAMC,gBAAgB,GAAG;EACrBC,iBAAiB,EAAE,EAAE;EACrBC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,CAAC;EACbC,cAAc,EAAE,EAAE;EAClBC,aAAa,EAAE,EAAE;EACjBC,SAAS,EAAE,SAAS;EACpBC,SAAS,EAAE,CAAC;EACZC,SAAS,EAAE,CAAC;EACZC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE;AACd,CAAC;AAED,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAACY,gBAAgB,CAAC;EAC5D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACiC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExD,MAAMuC,QAAQ,GAAGA,CAACC,OAAO,EAAEC,IAAI,GAAG,SAAS,EAAEC,QAAQ,GAAG,IAAI,KAAK;IAC7D,MAAMC,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IACrBX,SAAS,CAACY,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAEH,EAAE;MAAEH,OAAO;MAAEC,IAAI;MAAEC;IAAS,CAAC,CAAC,CAAC;EACjE,CAAC;EAED,MAAMK,WAAW,GAAIJ,EAAE,IAAK;IACxBT,SAAS,CAACY,IAAI,IAAIA,IAAI,CAACE,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACN,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC5D,CAAC;EAED,MAAMO,mBAAmB,GAAGhD,WAAW,CAAC,YAAY;IAChD,IAAI;MACA,MAAMiD,QAAQ,GAAG,MAAM9C,KAAK,CAAC+C,GAAG,CAAC,0BAA0B,EAAE;QACzDC,MAAM,EAAE;UAAEC,MAAM,EAAE;QAAS;MAC/B,CAAC,CAAC;MAEF,IAAIH,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvB,MAAMC,gBAAgB,GAAGN,QAAQ,CAACI,IAAI,CAAC9B,UAAU,CAC5CuB,MAAM,CAACU,CAAC,IAAIA,CAAC,CAACJ,MAAM,KAAK,QAAQ,CAAC,CAClCK,GAAG,CAACC,SAAS,KAAK;UACf,GAAGA,SAAS;UACZC,UAAU,EAAE,IAAIjB,IAAI,CAACgB,SAAS,CAACC,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAC/DC,QAAQ,EAAEC,OAAO,CAACJ,SAAS,CAACK,SAAS;QACzC,CAAC,CAAC,CAAC;QACPvC,aAAa,CAAC+B,gBAAgB,CAAC;MACnC,CAAC,MAAM;QACHlB,QAAQ,CAAC,4BAA4B,EAAE,OAAO,CAAC;MACnD;IACJ,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;MAChD3B,QAAQ,CAAC,4BAA4B,EAAE,OAAO,CAAC;IACnD;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM8B,gBAAgB,GAAGnE,WAAW,CAAC,MAAOoE,WAAW,IAAK;IACxD,IAAI,CAACA,WAAW,EAAE;IAElB,IAAI;MACA,MAAMnB,QAAQ,GAAG,MAAM9C,KAAK,CAAC+C,GAAG,CAAC,GAAGzC,YAAY,kCAAkC,EAAE;QAChF0C,MAAM,EAAE;UAAEkB,YAAY,EAAED;QAAY;MACxC,CAAC,CAAC;MAEF,IAAInB,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvBxB,cAAc,CAACmB,QAAQ,CAACI,IAAI,CAACiB,IAAI,CAAC;QAClC;QACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CACzBvB,QAAQ,CAACI,IAAI,CAACiB,IAAI,CAACG,OAAO,CAACC,GAAG,IAAI,CAACA,GAAG,CAACC,QAAQ,EAAED,GAAG,CAACE,QAAQ,CAAC,CAClE,CAAC;QACD,MAAMC,kBAAkB,CAACC,KAAK,CAACC,IAAI,CAACR,aAAa,CAAC,CAAC;MACvD;IACJ,CAAC,CAAC,OAAOP,GAAG,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEF,GAAG,CAAC;MAClD3B,QAAQ,CAAC,4BAA4B,EAAE,OAAO,CAAC;IACnD;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMwC,kBAAkB,GAAG,MAAOG,OAAO,IAAK;IAC1C,IAAI;MACA,MAAM/B,QAAQ,GAAG,MAAM9C,KAAK,CAAC+C,GAAG,CAAC,GAAGzC,YAAY,8BAA8B,EAAE;QAC5E0C,MAAM,EAAE;UAAE8B,QAAQ,EAAED,OAAO,CAACE,IAAI,CAAC,GAAG;QAAE;MAC1C,CAAC,CAAC;MAEF,IAAIjC,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvBpB,gBAAgB,CAACe,QAAQ,CAACI,IAAI,CAACpB,aAAa,CAAC;MACjD;IACJ,CAAC,CAAC,OAAO+B,GAAG,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEF,GAAG,CAAC;MACpD3B,QAAQ,CAAC,mCAAmC,EAAE,OAAO,CAAC;IAC1D;EACJ,CAAC;EAEDtC,SAAS,CAAC,MAAM;IACZiD,mBAAmB,CAAC,CAAC;EACzB,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;EAEzB,MAAMrC,iBAAiB,GAAGV,OAAO,CAAC,MAC9BsB,UAAU,CAAC4D,IAAI,CAAC3B,CAAC,IAAIA,CAAC,CAACa,YAAY,KAAKe,QAAQ,CAAC3D,SAAS,CAACd,iBAAiB,CAAC,CAAC,EAC9E,CAACY,UAAU,EAAEE,SAAS,CAACd,iBAAiB,CAC5C,CAAC;EAEDZ,SAAS,CAAC,MAAM;IACZ,IAAIY,iBAAiB,EAAE;MACnBe,YAAY,CAACkB,IAAI,KAAK;QAClB,GAAGA,IAAI;QACP3B,SAAS,EAAEN,iBAAiB,CAAC0E,WAAW,IAAI,CAAC;QAC7CnE,SAAS,EAAEP,iBAAiB,CAAC2E,WAAW,IAAI,CAAC;QAC7CnE,QAAQ,EAAER,iBAAiB,CAAC4E,SAAS,IAAI,CAAC;QAC1CnE,QAAQ,EAAET,iBAAiB,CAAC6E,SAAS,IAAI;MAC7C,CAAC,CAAC,CAAC;MACHrB,gBAAgB,CAACxD,iBAAiB,CAAC0D,YAAY,CAAC;IACpD,CAAC,MAAM;MACHvC,cAAc,CAAC,IAAI,CAAC;IACxB;EACJ,CAAC,EAAE,CAACnB,iBAAiB,EAAEwD,gBAAgB,CAAC,CAAC;EAEzC,MAAMsB,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCnE,YAAY,CAACkB,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAAC+C,IAAI,GAAGA,IAAI,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,IAAI,CAACG,QAAQ,CAAC,MAAM,CAAC,GAAGC,UAAU,CAACH,KAAK,CAAC,IAAI,CAAC,GAAGA;IACvF,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMI,SAAS,GAAGA,CAAA,KAAM;IACpBtE,YAAY,CAAChB,gBAAgB,CAAC;IAC9BoB,cAAc,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMmE,uBAAuB,GAAG,MAAOP,CAAC,IAAK;IACzCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAElB,IAAI,CAACzE,SAAS,CAACX,cAAc,EAAE;MAC3BuB,QAAQ,CAAC,wBAAwB,EAAE,OAAO,CAAC;MAC3C;IACJ;IAEA,IAAIZ,SAAS,CAACX,cAAc,KAAK,MAAM,IAAI,CAACW,SAAS,CAACV,aAAa,EAAE;MACjEsB,QAAQ,CAAC,uBAAuB,EAAE,OAAO,CAAC;MAC1C;IACJ;IAEA,IAAIZ,SAAS,CAACX,cAAc,KAAK,MAAM,IAAIW,SAAS,CAACX,cAAc,KAAKW,SAAS,CAACV,aAAa,EAAE;MAC7FsB,QAAQ,CAAC,0CAA0C,EAAE,OAAO,CAAC;MAC7D;IACJ;IAEAT,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACA,MAAMuE,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAE5E,SAAS,CAACd,iBAAiB,CAAC;MAC5DwF,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAE5E,SAAS,CAACb,UAAU,CAAC;MACrDuF,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAE5E,SAAS,CAACZ,UAAU,CAAC;MACrDsF,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE5E,SAAS,CAACX,cAAc,CAAC;MACnDqF,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE5E,SAAS,CAACV,aAAa,CAAC;MACjDoF,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE5E,SAAS,CAACT,SAAS,CAAC;MAElD,MAAMiC,QAAQ,GAAG,MAAM9C,KAAK,CAACmG,IAAI,CAC7B,GAAG7F,YAAY,kCAAkC,EACjD0F,QAAQ,EACR;QAAEI,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MAAE,CACzD,CAAC;MAED,IAAItD,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvBjB,QAAQ,CAAC,iCAAiC,CAAC;QAC3CW,mBAAmB,CAAC,CAAC;QACrBgD,SAAS,CAAC,CAAC;MACf,CAAC,MAAM;QACH3D,QAAQ,CAACY,QAAQ,CAACI,IAAI,CAACf,OAAO,IAAI,8BAA8B,EAAE,OAAO,CAAC;MAC9E;IACJ,CAAC,CAAC,OAAO0B,GAAG,EAAE;MAAA,IAAAwC,aAAA,EAAAC,kBAAA;MACV,MAAMC,QAAQ,GAAG,EAAAF,aAAA,GAAAxC,GAAG,CAACf,QAAQ,cAAAuD,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcnD,IAAI,cAAAoD,kBAAA,uBAAlBA,kBAAA,CAAoBnE,OAAO,KAAI,8BAA8B;MAC9ED,QAAQ,CAACqE,QAAQ,EAAE,OAAO,CAAC;IAC/B,CAAC,SAAS;MACN9E,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM+E,wBAAwB,GAAGA,CAACjC,GAAG,EAAEkC,MAAM,KAAK;IAC9C,IAAI,CAACnF,SAAS,CAACX,cAAc,EAAE,OAAO,IAAI;;IAE1C;IACA,IAAIW,SAAS,CAACX,cAAc,KAAK,MAAM,EAAE;MACrC,IAAI8F,MAAM,KAAK,MAAM,EAAE;QACnB,OAAOlC,GAAG,CAACmC,uBAAuB,CAACC,IAAI;MAC3C,CAAC,MAAM;QACH,OAAO,CAACpC,GAAG,CAACqC,YAAY,GAAGpG,iBAAiB,CAAC6E,SAAS,EAAEwB,OAAO,CAAC,CAAC,CAAC;MACtE;IACJ;;IAEA;IACA,MAAMC,SAAS,GAAGxF,SAAS,CAACX,cAAc,KAAK8F,MAAM;IACrD,IAAIK,SAAS,EAAE;MACX,QAAQL,MAAM;QACV,KAAK,YAAY;QACjB,KAAK,YAAY;UACb,OAAOlC,GAAG,CAACmC,uBAAuB,CAACK,GAAG;QAC1C;UACI,OAAO,IAAI;MACnB;IACJ,CAAC,MAAM;MACH,OAAO,CAACxC,GAAG,CAACqC,YAAY,GAAGpG,iBAAiB,CAAC6E,SAAS,EAAEwB,OAAO,CAAC,CAAC,CAAC;IACtE;EACJ,CAAC;EAED,MAAMG,sBAAsB,GAAGA,CAACvG,UAAU,EAAEC,UAAU,KAAK;IACvD,MAAMuG,UAAU,GAAGxG,UAAU,GAAGC,UAAU;IAC1C,IAAIuG,UAAU,GAAG,CAAC,EAAE;MAChB,OAAO;QAAExB,KAAK,EAAE,IAAIwB,UAAU,EAAE;QAAEC,KAAK,EAAE;MAA0B,CAAC;IACxE,CAAC,MAAM,IAAID,UAAU,GAAG,CAAC,EAAE;MACvB,OAAO;QAAExB,KAAK,EAAEwB,UAAU,CAACE,QAAQ,CAAC,CAAC;QAAED,KAAK,EAAE;MAA0B,CAAC;IAC7E;IACA,OAAO;MAAEzB,KAAK,EAAE,GAAG;MAAEyB,KAAK,EAAE;IAAyB,CAAC;EAC1D,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACtF,aAAa,IAAI,EAACtB,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEkD,QAAQ,GAAE,OAAO,IAAI;IAE/D,oBACIvD,OAAA;MAASkH,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAClCnH,OAAA;QAAKkH,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAC9BnH,OAAA;UAAIkH,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACNvH,OAAA;QAAKkH,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAC/BnH,OAAA;UACIwH,GAAG,EAAE7F,aAAa,CAAC8F,WAAW,IAAI,0BAA2B;UAC7DC,GAAG,EAAC,aAAa;UACjBR,SAAS,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACFvH,OAAA;UAAIkH,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAExF,aAAa,CAACgG;QAAW;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACNvH,OAAA;QAAKkH,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzBnH,OAAA;UAAKkH,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBnH,OAAA;YAAKkH,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCvH,OAAA;YAAKkH,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAExF,aAAa,CAACiG,IAAI,IAAI;UAAC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACNvH,OAAA;UAAKkH,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBnH,OAAA;YAAKkH,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvH,OAAA;YAAKkH,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAExF,aAAa,CAACkG,KAAK,IAAI;UAAC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACNvH,OAAA;UAAKkH,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBnH,OAAA;YAAKkH,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxCvH,OAAA;YAAKkH,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAExF,aAAa,CAACmG,MAAM,IAAI;UAAC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNvH,OAAA;QAAKkH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BnH,OAAA;UAAKkH,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClDvH,OAAA;UAAKkH,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAExF,aAAa,CAACoG,QAAQ,IAAI;QAAK;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAElB,CAAC;EAED,MAAMS,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAC3H,iBAAiB,EAAE,OAAO,IAAI;IAEnC,oBACIL,OAAA;MAAKkH,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1BnH,OAAA;QAAKkH,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjCnH,OAAA;UAAKkH,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAE9G,iBAAiB,CAAC4H;QAAM;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3DvH,OAAA;UACIiC,IAAI,EAAC,QAAQ;UACboD,IAAI,EAAC,YAAY;UACjBC,KAAK,EAAEnE,SAAS,CAACb,UAAW;UAC5B4H,QAAQ,EAAE/C,iBAAkB;UAC5BgD,GAAG,EAAC,GAAG;UACPjB,SAAS,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNvH,OAAA;QAAKkH,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACtCvH,OAAA;QAAKkH,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjCnH,OAAA;UAAKkH,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAE9G,iBAAiB,CAAC+H;QAAM;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3DvH,OAAA;UACIiC,IAAI,EAAC,QAAQ;UACboD,IAAI,EAAC,YAAY;UACjBC,KAAK,EAAEnE,SAAS,CAACZ,UAAW;UAC5B2H,QAAQ,EAAE/C,iBAAkB;UAC5BgD,GAAG,EAAC,GAAG;UACPjB,SAAS,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd,CAAC;EAED,oBACIvH,OAAA;IAAKkH,SAAS,EAAC,4BAA4B;IAAAC,QAAA,GAEtCF,iBAAiB,CAAC,CAAC,eAEpBjH,OAAA;MAAKkH,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBAErCnH,OAAA;QAASkH,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACvCnH,OAAA;UAAKkH,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAC9BnH,OAAA;YAAIkH,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eAENvH,OAAA;UAAMqI,QAAQ,EAAE1C,uBAAwB;UAACuB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC7DnH,OAAA;YAAKkH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBnH,OAAA;cAAOkH,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvDvH,OAAA;cACIqF,IAAI,EAAC,mBAAmB;cACxBC,KAAK,EAAEnE,SAAS,CAACd,iBAAkB;cACnC6H,QAAQ,EAAE/C,iBAAkB;cAC5B+B,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAEvBnH,OAAA;gBAAQsF,KAAK,EAAC,EAAE;gBAAA6B,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9CtG,UAAU,CAACkC,GAAG,CAACC,SAAS,iBACrBpD,OAAA;gBAAqCsF,KAAK,EAAElC,SAAS,CAACW,YAAa;gBAAAoD,QAAA,GAC9D/D,SAAS,CAAC6E,MAAM,EAAC,MAAI,EAAC7E,SAAS,CAACgF,MAAM,EACtChF,SAAS,CAACG,QAAQ,GAAG,iBAAiB,GAAG,EAAE,EAAC,IAC7C,EAACH,SAAS,CAACC,UAAU;cAAA,GAHZD,SAAS,CAACW,YAAY;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAI3B,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,EAELlH,iBAAiB,iBACdL,OAAA,CAAAE,SAAA;YAAAiH,QAAA,GACKa,kBAAkB,CAAC,CAAC,eAErBhI,OAAA;cAAKkH,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC7BnH,OAAA;gBAAKkH,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvBnH,OAAA;kBAAOkH,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpDvH,OAAA;kBACIqF,IAAI,EAAC,gBAAgB;kBACrBC,KAAK,EAAEnE,SAAS,CAACX,cAAe;kBAChC0H,QAAQ,EAAE/C,iBAAkB;kBAC5B+B,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAEvBnH,OAAA;oBAAQsF,KAAK,EAAC,EAAE;oBAAA6B,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvCvH,OAAA;oBAAQsF,KAAK,EAAC,YAAY;oBAAA6B,QAAA,EAAE9G,iBAAiB,CAAC4H;kBAAM;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eAC9DvH,OAAA;oBAAQsF,KAAK,EAAC,YAAY;oBAAA6B,QAAA,EAAE9G,iBAAiB,CAAC+H;kBAAM;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eAC9DvH,OAAA;oBAAQsF,KAAK,EAAC,MAAM;oBAAA6B,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eAENvH,OAAA;gBAAKkH,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvBnH,OAAA;kBAAOkH,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDvH,OAAA;kBACIqF,IAAI,EAAC,eAAe;kBACpBC,KAAK,EAAEnE,SAAS,CAACV,aAAc;kBAC/ByH,QAAQ,EAAE/C,iBAAkB;kBAC5B+B,SAAS,EAAC,aAAa;kBACvBoB,QAAQ,EAAEnH,SAAS,CAACX,cAAc,KAAK,MAAO;kBAAA2G,QAAA,gBAE9CnH,OAAA;oBAAQsF,KAAK,EAAC,EAAE;oBAAA6B,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACrCpG,SAAS,CAACX,cAAc,KAAK,YAAY,iBACtCR,OAAA;oBAAQsF,KAAK,EAAC,YAAY;oBAAA6B,QAAA,EAAE9G,iBAAiB,CAAC4H;kBAAM;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAChE,EACApG,SAAS,CAACX,cAAc,KAAK,YAAY,iBACtCR,OAAA;oBAAQsF,KAAK,EAAC,YAAY;oBAAA6B,QAAA,EAAE9G,iBAAiB,CAAC+H;kBAAM;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAChE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENvH,OAAA;cAAKkH,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBnH,OAAA;gBAAOkH,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvDvH,OAAA;gBACIqF,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAEnE,SAAS,CAACT,SAAU;gBAC3BwH,QAAQ,EAAE/C,iBAAkB;gBAC5B+B,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAEvBnH,OAAA;kBAAQsF,KAAK,EAAC,SAAS;kBAAA6B,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxCvH,OAAA;kBAAQsF,KAAK,EAAC,WAAW;kBAAA6B,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CvH,OAAA;kBAAQsF,KAAK,EAAC,WAAW;kBAAA6B,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAENvH,OAAA;cACIiC,IAAI,EAAC,QAAQ;cACbiF,SAAS,EAAE,iBAAiB7F,OAAO,GAAG,SAAS,GAAG,EAAE,EAAG;cACvDiH,QAAQ,EAAEjH,OAAQ;cAAA8F,QAAA,EAEjB9F,OAAO,GAAG,eAAe,GAAG;YAAkB;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA,eACX,CACL;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGVvH,OAAA;QAASkH,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC7BnH,OAAA;UAAKkH,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAC9BnH,OAAA;YAAIkH,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,EAELhG,WAAW,IAAIA,WAAW,CAACgH,MAAM,GAAG,CAAC,gBAClCvI,OAAA;UAAKkH,SAAS,EAAC,WAAW;UAACsB,IAAI,EAAC,MAAM;UAAArB,QAAA,EACjC5F,WAAW,CAAC4B,GAAG,CAAC,CAACiB,GAAG,EAAEqE,KAAK,KAAK;YAC7B,MAAMC,WAAW,GAAGrC,wBAAwB,CAACjC,GAAG,EAAEA,GAAG,CAACuE,gBAAgB,CAAC;YACvE,MAAMC,WAAW,GAAGvC,wBAAwB,CAACjC,GAAG,EAAEA,GAAG,CAACyE,gBAAgB,CAAC;YACvE,MAAMC,gBAAgB,GAAGJ,WAAW,GAAGtE,GAAG,CAACqC,YAAY,GAAG,SAAS,GAAG,QAAQ;YAC9E,MAAMsC,gBAAgB,GAAGH,WAAW,GAAGxE,GAAG,CAAC4E,YAAY,GAAG,SAAS,GAAG,QAAQ;YAC9E,MAAMC,aAAa,GAAGpC,sBAAsB,CAAC1F,SAAS,CAACb,UAAU,EAAEa,SAAS,CAACZ,UAAU,CAAC;YAExF,oBACIP,OAAA;cAA+BkH,SAAS,EAAC,UAAU;cAACsB,IAAI,EAAC,UAAU;cAAArB,QAAA,gBAC/DnH,OAAA;gBAAKkH,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC1BnH,OAAA;kBAAKkH,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBACxBnH,OAAA;oBAAMkH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvDvH,OAAA;oBAAMkH,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,GAC9BhG,SAAS,CAACb,UAAU,EAAC,KAAG,EAACa,SAAS,CAACZ,UAAU;kBAAA;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNvH,OAAA;kBAAKkH,SAAS,EAAE,kBAAkB+B,aAAa,CAAClC,KAAK,EAAG;kBAAAI,QAAA,gBACpDnH,OAAA;oBAAAmH,QAAA,EAAM;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5BvH,OAAA;oBAAAmH,QAAA,EAAO8B,aAAa,CAAC3D;kBAAK;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENvH,OAAA;gBAAKkH,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAChCnH,OAAA;kBAAKkH,SAAS,EAAE,YAAY4B,gBAAgB,EAAG;kBAAA3B,QAAA,gBAC3CnH,OAAA;oBAAKkH,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC5BnH,OAAA;sBAAKkH,SAAS,EAAC,aAAa;sBAAC,eAAY,MAAM;sBAAAC,QAAA,EAC1C/C,GAAG,CAAC8E,UAAU,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;oBAAC;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC,eACNvH,OAAA;sBAAMkH,SAAS,EAAC,WAAW;sBAAAC,QAAA,EAAE/C,GAAG,CAAC8E;oBAAU;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACNvH,OAAA;oBAAKkH,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBACxBnH,OAAA;sBAAMkH,SAAS,EAAC,YAAY;sBAAAC,QAAA,GAAC,UACjB,EAAC/C,GAAG,CAACuE,gBAAgB;oBAAA;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC,eACPvH,OAAA;sBAAMkH,SAAS,EAAC,YAAY;sBAAAC,QAAA,GAAC,SAClB,EAAC/C,GAAG,CAACqC,YAAY;oBAAA;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNvH,OAAA;oBAAKkH,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC3BnH,OAAA;sBAAMkH,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7CvH,OAAA;sBAAMkH,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAEuB;oBAAW;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAENvH,OAAA;kBAAKkH,SAAS,EAAE,YAAY6B,gBAAgB,EAAG;kBAAA5B,QAAA,gBAC3CnH,OAAA;oBAAKkH,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC5BnH,OAAA;sBAAKkH,SAAS,EAAC,aAAa;sBAAC,eAAY,MAAM;sBAAAC,QAAA,EAC1C/C,GAAG,CAACiF,UAAU,CAACF,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;oBAAC;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC,eACNvH,OAAA;sBAAMkH,SAAS,EAAC,WAAW;sBAAAC,QAAA,EAAE/C,GAAG,CAACiF;oBAAU;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACNvH,OAAA;oBAAKkH,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBACxBnH,OAAA;sBAAMkH,SAAS,EAAC,YAAY;sBAAAC,QAAA,GAAC,UACjB,EAAC/C,GAAG,CAACyE,gBAAgB;oBAAA;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC,eACPvH,OAAA;sBAAMkH,SAAS,EAAC,YAAY;sBAAAC,QAAA,GAAC,SAClB,EAAC/C,GAAG,CAAC4E,YAAY;oBAAA;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNvH,OAAA;oBAAKkH,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC3BnH,OAAA;sBAAMkH,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7CvH,OAAA;sBAAMkH,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAEyB;oBAAW;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA,GAxDAnD,GAAG,CAACkF,MAAM,IAAIb,KAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyDxB,CAAC;UAEd,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,gBAENvH,OAAA;UAAKkH,SAAS,EAAC,iBAAiB;UAACsB,IAAI,EAAC,QAAQ;UAAArB,QAAA,EACzC9G,iBAAiB,GAAG,kCAAkC,GAAG;QAAiC;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eACNvH,OAAA,CAACF,cAAc;MAAC2B,MAAM,EAAEA,MAAO;MAACc,WAAW,EAAEA;IAAY;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3D,CAAC;AAEd;AAACvG,EAAA,CA1cQD,eAAe;AAAAwI,EAAA,GAAfxI,eAAe;AA4cxB,eAAeA,eAAe;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}