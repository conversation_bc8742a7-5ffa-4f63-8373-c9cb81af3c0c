{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\ChallengeSystem.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaSave, FaTrash, FaExchangeAlt, FaClock, FaCalendarAlt, FaFutbol, FaInfoCircle, FaCheckCircle, FaExclamationTriangle, FaHome, FaPlane } from 'react-icons/fa';\nimport './ChallengeSystem.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction ChallengeSystem() {\n  _s();\n  var _teams$find, _teams$find2, _teams$find3, _teams$find4;\n  const [challenges, setChallenges] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const [newChallenge, setNewChallenge] = useState({\n    team1: '',\n    team2: '',\n    odds1: 1.8,\n    odds2: 1.8,\n    goalAdvantage1: 0,\n    goalAdvantage2: 0,\n    startTime: '',\n    endTime: '',\n    matchTime: '',\n    matchType: 'full_time',\n    oddsDraw: 0.8,\n    oddsLost: 0.2\n  });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  useEffect(() => {\n    fetchTeams();\n  }, []);\n  const fetchTeams = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      setTeams(response.data.data);\n    } catch (err) {\n      console.error(\"Error fetching teams:\", err);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewChallenge(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n    if (!newChallenge.team1 || !newChallenge.team2 || !newChallenge.odds1 || !newChallenge.odds2) {\n      setError('Team names and odds are required.');\n      return;\n    }\n    try {\n      const formData = new FormData();\n      const team1Data = teams.find(t => t.name === newChallenge.team1);\n      const team2Data = teams.find(t => t.name === newChallenge.team2);\n      formData.append('team1', newChallenge.team1);\n      formData.append('team2', newChallenge.team2);\n      formData.append('odds1', newChallenge.odds1);\n      formData.append('odds2', newChallenge.odds2);\n      formData.append('goalAdvantage1', newChallenge.goalAdvantage1);\n      formData.append('goalAdvantage2', newChallenge.goalAdvantage2);\n      formData.append('startTime', newChallenge.startTime);\n      formData.append('endTime', newChallenge.endTime);\n      formData.append('matchTime', newChallenge.matchTime);\n      formData.append('matchType', newChallenge.matchType);\n      formData.append('oddsDraw', newChallenge.oddsDraw);\n      formData.append('oddsLost', newChallenge.oddsLost);\n      formData.append('logo1', team1Data ? `${API_BASE_URL}/${team1Data.logo}` : '');\n      formData.append('logo2', team2Data ? `${API_BASE_URL}/${team2Data.logo}` : '');\n      const response = await axios.post('create_challenge.php', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data.success) {\n        setSuccess('Challenge created successfully!');\n        setNewChallenge({\n          team1: '',\n          team2: '',\n          odds1: 1.8,\n          odds2: 1.8,\n          goalAdvantage1: 0,\n          goalAdvantage2: 0,\n          startTime: '',\n          endTime: '',\n          matchTime: '',\n          matchType: 'full_time',\n          oddsDraw: 0.8,\n          oddsLost: 0.2\n        });\n        setTimeout(() => {\n          setSuccess('');\n        }, 3000);\n      } else {\n        setError(response.data.message || 'Failed to create challenge');\n        setTimeout(() => {\n          setError('');\n        }, 3000);\n      }\n    } catch (err) {\n      setError('Failed to create challenge');\n    }\n  };\n  const handleDiscard = () => {\n    setNewChallenge({\n      team1: '',\n      team2: '',\n      odds1: 1.8,\n      odds2: 1.8,\n      goalAdvantage1: 0,\n      goalAdvantage2: 0,\n      startTime: '',\n      endTime: '',\n      matchTime: '',\n      matchType: 'full_time',\n      oddsDraw: 0.8,\n      oddsLost: 0.2\n    });\n    setError('');\n    setSuccess('');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"challenge-system\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Create a New Challenge\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleSubmit,\n        style: {\n          backgroundColor: '#166534',\n          color: 'white'\n        },\n        children: [/*#__PURE__*/_jsxDEV(FaSave, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Save Challenge\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleDiscard,\n        style: {\n          backgroundColor: '#dc2626',\n          color: 'white',\n          border: 'none'\n        },\n        children: [/*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Discard Changes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-message\",\n      children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this), success]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"challenge-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"match-settings\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"match-type-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [/*#__PURE__*/_jsxDEV(FaFutbol, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this), \" Match Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"match-settings-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"matchType\",\n                className: \"required-field\",\n                children: \"Match Type:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"matchType\",\n                name: \"matchType\",\n                value: newChallenge.matchType,\n                onChange: handleInputChange,\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"full_time\",\n                  children: \"Full Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"half_time\",\n                  children: \"Half Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"oddsDraw\",\n                children: [\"Draw Odds:\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"info-tooltip\",\n                  title: \"Multiplier for draw results\",\n                  children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                    size: 12,\n                    style: {\n                      marginLeft: '5px',\n                      color: '#6b7280'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                id: \"oddsDraw\",\n                name: \"oddsDraw\",\n                value: newChallenge.oddsDraw,\n                onChange: handleInputChange,\n                step: \"0.1\",\n                min: \"0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"odds-explanation\",\n                children: \"Default: 0.8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"oddsLost\",\n                children: [\"Lost Odds:\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"info-tooltip\",\n                  title: \"Multiplier for lost results\",\n                  children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                    size: 12,\n                    style: {\n                      marginLeft: '5px',\n                      color: '#6b7280'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                id: \"oddsLost\",\n                name: \"oddsLost\",\n                value: newChallenge.oddsLost,\n                onChange: handleInputChange,\n                step: \"0.1\",\n                min: \"0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"odds-explanation\",\n                children: \"Default: 0.2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'row',\n          justifyContent: 'space-between',\n          gap: '20px',\n          width: '100%',\n          position: 'relative',\n          alignItems: 'flex-start'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: '1',\n            backgroundColor: 'white',\n            borderRadius: '6px',\n            padding: '1.25rem',\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',\n            border: '1px solid #e5e7eb',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '0.75rem',\n            width: '48%',\n            maxWidth: '48%'\n          },\n          className: \"team-section team1\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [/*#__PURE__*/_jsxDEV(FaHome, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this), \" HOME TEAM\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"team1\",\n            className: \"required-field\",\n            children: \"Select Team 1:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"team1\",\n            name: \"team1\",\n            value: newChallenge.team1,\n            onChange: handleInputChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select a team\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), teams.map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: team.name,\n              children: team.name\n            }, team.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), newChallenge.team1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-container\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${API_BASE_URL}/${(_teams$find = teams.find(t => t.name === newChallenge.team1)) === null || _teams$find === void 0 ? void 0 : _teams$find.logo}`,\n              alt: \"Team 1 Logo\",\n              className: \"logo-preview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-container empty-logo\",\n            children: /*#__PURE__*/_jsxDEV(FaHome, {\n              size: 40,\n              style: {\n                color: '#d1d5db'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"odds1\",\n            className: \"required-field\",\n            children: [\"Odds for Team 1:\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"info-tooltip\",\n              title: \"Multiplier for team 1 win\",\n              children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                size: 12,\n                style: {\n                  marginLeft: '5px',\n                  color: '#6b7280'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            id: \"odds1\",\n            name: \"odds1\",\n            value: newChallenge.odds1,\n            onChange: handleInputChange,\n            required: true,\n            step: \"0.01\",\n            min: \"1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"odds-explanation\",\n            children: [\"User's bet x \", newChallenge.odds1, \" = Potential winnings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"goalAdvantage1\",\n            children: \"Goal Advantage:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            id: \"goalAdvantage1\",\n            name: \"goalAdvantage1\",\n            value: newChallenge.goalAdvantage1,\n            onChange: handleInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            width: '40px',\n            height: '40px',\n            backgroundColor: '#166534',\n            borderRadius: '50%',\n            color: 'white',\n            fontSize: '1rem',\n            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n            zIndex: '10',\n            alignSelf: 'center',\n            margin: '0 10px'\n          },\n          className: \"vs-divider\",\n          children: /*#__PURE__*/_jsxDEV(FaExchangeAlt, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: '1',\n            backgroundColor: 'white',\n            borderRadius: '6px',\n            padding: '1.25rem',\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',\n            border: '1px solid #e5e7eb',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '0.75rem',\n            width: '48%',\n            maxWidth: '48%'\n          },\n          className: \"team-section team2\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [/*#__PURE__*/_jsxDEV(FaPlane, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this), \" AWAY TEAM\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"team2\",\n            className: \"required-field\",\n            children: \"Select Team 2:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"team2\",\n            name: \"team2\",\n            value: newChallenge.team2,\n            onChange: handleInputChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select a team\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), teams.map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: team.name,\n              children: team.name\n            }, team.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), newChallenge.team2 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-container\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${API_BASE_URL}/${(_teams$find2 = teams.find(t => t.name === newChallenge.team2)) === null || _teams$find2 === void 0 ? void 0 : _teams$find2.logo}`,\n              alt: \"Team 2 Logo\",\n              className: \"logo-preview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-container empty-logo\",\n            children: /*#__PURE__*/_jsxDEV(FaPlane, {\n              size: 40,\n              style: {\n                color: '#d1d5db'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"odds2\",\n            className: \"required-field\",\n            children: [\"Odds for Team 2:\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"info-tooltip\",\n              title: \"Multiplier for team 2 win\",\n              children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                size: 12,\n                style: {\n                  marginLeft: '5px',\n                  color: '#6b7280'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            id: \"odds2\",\n            name: \"odds2\",\n            value: newChallenge.odds2,\n            onChange: handleInputChange,\n            required: true,\n            step: \"0.01\",\n            min: \"1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"odds-explanation\",\n            children: [\"User's bet x \", newChallenge.odds2, \" = Potential winnings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"goalAdvantage2\",\n            children: \"Goal Advantage:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            id: \"goalAdvantage2\",\n            name: \"goalAdvantage2\",\n            value: newChallenge.goalAdvantage2,\n            onChange: handleInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"time-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(FaClock, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 15\n          }, this), \" Time Settings\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"time-groups-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"time-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"startTime\",\n              className: \"required-field\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"time-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), \" Challenge Start Time:\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"datetime-local\",\n              id: \"startTime\",\n              name: \"startTime\",\n              value: newChallenge.startTime,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"time-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"endTime\",\n              className: \"required-field\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"time-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this), \" Challenge End Time:\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"datetime-local\",\n              id: \"endTime\",\n              name: \"endTime\",\n              value: newChallenge.endTime,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"time-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"matchTime\",\n              className: \"required-field\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"time-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this), \" Actual Match Time:\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"datetime-local\",\n              id: \"matchTime\",\n              name: \"matchTime\",\n              value: newChallenge.matchTime,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"challenge-preview\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: [/*#__PURE__*/_jsxDEV(FaFutbol, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this), \" Challenge Preview\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 9\n      }, this), !newChallenge.team1 || !newChallenge.team2 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-preview\",\n        children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Select teams to see the challenge preview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'row',\n            justifyContent: 'space-between',\n            gap: '20px',\n            width: '100%',\n            position: 'relative',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              textAlign: 'center',\n              width: '48%',\n              maxWidth: '48%'\n            },\n            className: \"preview-team\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-container\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: `${API_BASE_URL}/${(_teams$find3 = teams.find(t => t.name === newChallenge.team1)) === null || _teams$find3 === void 0 ? void 0 : _teams$find3.logo}`,\n                alt: \"Team 1 Logo\",\n                className: \"logo-preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"preview-team-name\",\n              children: newChallenge.team1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"preview-odds\",\n              children: [\"Odds: \", newChallenge.odds1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this), newChallenge.goalAdvantage1 > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"preview-advantage\",\n              children: [\"+\", newChallenge.goalAdvantage1, \" Goal Advantage\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              width: '40px',\n              height: '40px',\n              backgroundColor: '#166534',\n              borderRadius: '50%',\n              color: 'white',\n              fontSize: '1.25rem',\n              fontWeight: '700',\n              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n              margin: '0 10px'\n            },\n            className: \"preview-vs\",\n            children: \"VS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              textAlign: 'center',\n              width: '48%',\n              maxWidth: '48%'\n            },\n            className: \"preview-team\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-container\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: `${API_BASE_URL}/${(_teams$find4 = teams.find(t => t.name === newChallenge.team2)) === null || _teams$find4 === void 0 ? void 0 : _teams$find4.logo}`,\n                alt: \"Team 2 Logo\",\n                className: \"logo-preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"preview-team-name\",\n              children: newChallenge.team2\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"preview-odds\",\n              children: [\"Odds: \", newChallenge.odds2]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this), newChallenge.goalAdvantage2 > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"preview-advantage\",\n              children: [\"+\", newChallenge.goalAdvantage2, \" Goal Advantage\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-time-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Start Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n              className: \"time-detail-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this), newChallenge.startTime ? new Date(newChallenge.startTime).toLocaleString() : 'Not set']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"End Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n              className: \"time-detail-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 17\n            }, this), newChallenge.endTime ? new Date(newChallenge.endTime).toLocaleString() : 'Not set']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Match Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n              className: \"time-detail-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 17\n            }, this), newChallenge.matchTime ? new Date(newChallenge.matchTime).toLocaleString() : 'Not set']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Match Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FaFutbol, {\n              className: \"time-detail-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 17\n            }, this), newChallenge.matchType.replace('_', ' ').toUpperCase()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n}\n_s(ChallengeSystem, \"fXE10web/93TsEYG1LHtJbihUCI=\");\n_c = ChallengeSystem;\nexport default ChallengeSystem;\nvar _c;\n$RefreshReg$(_c, \"ChallengeSystem\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaSave", "FaTrash", "FaExchangeAlt", "FaClock", "FaCalendarAlt", "FaFutbol", "FaInfoCircle", "FaCheckCircle", "FaExclamationTriangle", "FaHome", "FaPlane", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE_URL", "ChallengeSystem", "_s", "_teams$find", "_teams$find2", "_teams$find3", "_teams$find4", "challenges", "setChallenges", "teams", "setTeams", "newChallenge", "setNewChallenge", "team1", "team2", "odds1", "odds2", "goalAdvantage1", "goalAdvantage2", "startTime", "endTime", "matchTime", "matchType", "oddsDraw", "oddsLost", "error", "setError", "success", "setSuccess", "fetchTeams", "response", "get", "data", "err", "console", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "formData", "FormData", "team1Data", "find", "t", "team2Data", "append", "logo", "post", "headers", "setTimeout", "message", "handleDiscard", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "backgroundColor", "color", "border", "onSubmit", "htmlFor", "id", "onChange", "required", "title", "size", "marginLeft", "type", "step", "min", "display", "flexDirection", "justifyContent", "gap", "width", "position", "alignItems", "flex", "borderRadius", "padding", "boxShadow", "max<PERSON><PERSON><PERSON>", "map", "team", "src", "alt", "height", "fontSize", "zIndex", "alignSelf", "margin", "marginBottom", "textAlign", "fontWeight", "Date", "toLocaleString", "replace", "toUpperCase", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/ChallengeSystem.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  FaSave,\n  FaTrash,\n  FaExchangeAlt,\n  FaClock,\n  FaCalendarAlt,\n  FaFutbol,\n  FaInfoCircle,\n  FaCheckCircle,\n  FaExclamationTriangle,\n  FaHome,\n  FaPlane\n} from 'react-icons/fa';\nimport './ChallengeSystem.css';\nconst API_BASE_URL = '/backend';\n\nfunction ChallengeSystem() {\n  const [challenges, setChallenges] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const [newChallenge, setNewChallenge] = useState({\n    team1: '',\n    team2: '',\n    odds1: 1.8,\n    odds2: 1.8,\n    goalAdvantage1: 0,\n    goalAdvantage2: 0,\n    startTime: '',\n    endTime: '',\n    matchTime: '',\n    matchType: 'full_time',\n    oddsDraw: 0.8,\n    oddsLost: 0.2,\n  });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  useEffect(() => {\n    fetchTeams();\n  }, []);\n\n  const fetchTeams = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      setTeams(response.data.data);\n    } catch (err) {\n      console.error(\"Error fetching teams:\", err);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setNewChallenge(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n\n    if (!newChallenge.team1 || !newChallenge.team2 || !newChallenge.odds1 || !newChallenge.odds2) {\n      setError('Team names and odds are required.');\n      return;\n    }\n\n    try {\n      const formData = new FormData();\n      const team1Data = teams.find(t => t.name === newChallenge.team1);\n      const team2Data = teams.find(t => t.name === newChallenge.team2);\n\n      formData.append('team1', newChallenge.team1);\n      formData.append('team2', newChallenge.team2);\n      formData.append('odds1', newChallenge.odds1);\n      formData.append('odds2', newChallenge.odds2);\n      formData.append('goalAdvantage1', newChallenge.goalAdvantage1);\n      formData.append('goalAdvantage2', newChallenge.goalAdvantage2);\n      formData.append('startTime', newChallenge.startTime);\n      formData.append('endTime', newChallenge.endTime);\n      formData.append('matchTime', newChallenge.matchTime);\n      formData.append('matchType', newChallenge.matchType);\n      formData.append('oddsDraw', newChallenge.oddsDraw);\n      formData.append('oddsLost', newChallenge.oddsLost);\n      formData.append('logo1', team1Data ? `${API_BASE_URL}/${team1Data.logo}` : '');\n      formData.append('logo2', team2Data ? `${API_BASE_URL}/${team2Data.logo}` : '');\n\n      const response = await axios.post('create_challenge.php', formData, {\n        headers: { 'Content-Type': 'multipart/form-data' }\n      });\n\n      if (response.data.success) {\n        setSuccess('Challenge created successfully!');\n        setNewChallenge({\n          team1: '',\n          team2: '',\n          odds1: 1.8,\n          odds2: 1.8,\n          goalAdvantage1: 0,\n          goalAdvantage2: 0,\n          startTime: '',\n          endTime: '',\n          matchTime: '',\n          matchType: 'full_time',\n          oddsDraw: 0.8,\n          oddsLost: 0.2,\n        });\n        setTimeout(() => {\n          setSuccess('');\n        }, 3000);\n      } else {\n        setError(response.data.message || 'Failed to create challenge');\n        setTimeout(() => {\n          setError('');\n        }, 3000);\n      }\n    } catch (err) {\n      setError('Failed to create challenge');\n    }\n  };\n\n  const handleDiscard = () => {\n    setNewChallenge({\n      team1: '', team2: '', odds1: 1.8, odds2: 1.8,\n      goalAdvantage1: 0, goalAdvantage2: 0,\n      startTime: '', endTime: '', matchTime: '',\n      matchType: 'full_time',\n      oddsDraw: 0.8,\n      oddsLost: 0.2,\n    });\n    setError('');\n    setSuccess('');\n  };\n\n  return (\n    <div className=\"challenge-system\">\n      <h1>Create a New Challenge</h1>\n      <div className=\"header-actions\">\n        <button onClick={handleSubmit} style={{ backgroundColor: '#166534', color: 'white' }}>\n          <FaSave />\n          <span>Save Challenge</span>\n        </button>\n        <button onClick={handleDiscard} style={{ backgroundColor: '#dc2626', color: 'white', border: 'none' }}>\n          <FaTrash />\n          <span>Discard Changes</span>\n        </button>\n      </div>\n      {error && (\n        <div className=\"error-message\">\n          <FaExclamationTriangle />\n          {error}\n        </div>\n      )}\n      {success && (\n        <div className=\"success-message\">\n          <FaCheckCircle />\n          {success}\n        </div>\n      )}\n      <form onSubmit={handleSubmit} className=\"challenge-form\">\n        <div className=\"match-settings\">\n          <div className=\"match-type-section\">\n            <h3><FaFutbol /> Match Settings</h3>\n            <div className=\"match-settings-grid\">\n              <div className=\"form-group\">\n                <label htmlFor=\"matchType\" className=\"required-field\">Match Type:</label>\n                <select\n                  id=\"matchType\"\n                  name=\"matchType\"\n                  value={newChallenge.matchType}\n                  onChange={handleInputChange}\n                  required\n                >\n                  <option value=\"full_time\">Full Time</option>\n                  <option value=\"half_time\">Half Time</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label htmlFor=\"oddsDraw\">\n                  Draw Odds:\n                  <span className=\"info-tooltip\" title=\"Multiplier for draw results\">\n                    <FaInfoCircle size={12} style={{ marginLeft: '5px', color: '#6b7280' }} />\n                  </span>\n                </label>\n                <input\n                  type=\"number\"\n                  id=\"oddsDraw\"\n                  name=\"oddsDraw\"\n                  value={newChallenge.oddsDraw}\n                  onChange={handleInputChange}\n                  step=\"0.1\"\n                  min=\"0\"\n                />\n                <p className=\"odds-explanation\">Default: 0.8</p>\n              </div>\n              <div className=\"form-group\">\n                <label htmlFor=\"oddsLost\">\n                  Lost Odds:\n                  <span className=\"info-tooltip\" title=\"Multiplier for lost results\">\n                    <FaInfoCircle size={12} style={{ marginLeft: '5px', color: '#6b7280' }} />\n                  </span>\n                </label>\n                <input\n                  type=\"number\"\n                  id=\"oddsLost\"\n                  name=\"oddsLost\"\n                  value={newChallenge.oddsLost}\n                  onChange={handleInputChange}\n                  step=\"0.1\"\n                  min=\"0\"\n                />\n                <p className=\"odds-explanation\">Default: 0.2</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div style={{\n            display: 'flex',\n            flexDirection: 'row',\n            justifyContent: 'space-between',\n            gap: '20px',\n            width: '100%',\n            position: 'relative',\n            alignItems: 'flex-start'\n          }}>\n          <div style={{\n            flex: '1',\n            backgroundColor: 'white',\n            borderRadius: '6px',\n            padding: '1.25rem',\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',\n            border: '1px solid #e5e7eb',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '0.75rem',\n            width: '48%',\n            maxWidth: '48%'\n          }} className=\"team-section team1\">\n            <h3><FaHome /> HOME TEAM</h3>\n            <label htmlFor=\"team1\" className=\"required-field\">Select Team 1:</label>\n            <select id=\"team1\" name=\"team1\" value={newChallenge.team1} onChange={handleInputChange} required>\n              <option value=\"\">Select a team</option>\n              {teams.map((team) => (\n                <option key={team.id} value={team.name}>\n                  {team.name}\n                </option>\n              ))}\n            </select>\n\n            {newChallenge.team1 ? (\n              <div className=\"logo-container\">\n                <img\n                  src={`${API_BASE_URL}/${teams.find(t => t.name === newChallenge.team1)?.logo}`}\n                  alt=\"Team 1 Logo\"\n                  className=\"logo-preview\"\n                />\n              </div>\n            ) : (\n              <div className=\"logo-container empty-logo\">\n                <FaHome size={40} style={{ color: '#d1d5db' }} />\n              </div>\n            )}\n\n            <label htmlFor=\"odds1\" className=\"required-field\">\n              Odds for Team 1:\n              <span className=\"info-tooltip\" title=\"Multiplier for team 1 win\">\n                <FaInfoCircle size={12} style={{ marginLeft: '5px', color: '#6b7280' }} />\n              </span>\n            </label>\n            <input\n              type=\"number\"\n              id=\"odds1\"\n              name=\"odds1\"\n              value={newChallenge.odds1}\n              onChange={handleInputChange}\n              required\n              step=\"0.01\"\n              min=\"1\"\n            />\n            <p className=\"odds-explanation\">\n              User's bet x {newChallenge.odds1} = Potential winnings\n            </p>\n\n            <label htmlFor=\"goalAdvantage1\">Goal Advantage:</label>\n            <input\n              type=\"number\"\n              id=\"goalAdvantage1\"\n              name=\"goalAdvantage1\"\n              value={newChallenge.goalAdvantage1}\n              onChange={handleInputChange}\n            />\n          </div>\n\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            width: '40px',\n            height: '40px',\n            backgroundColor: '#166534',\n            borderRadius: '50%',\n            color: 'white',\n            fontSize: '1rem',\n            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n            zIndex: '10',\n            alignSelf: 'center',\n            margin: '0 10px'\n          }} className=\"vs-divider\">\n            <FaExchangeAlt />\n          </div>\n\n          <div style={{\n            flex: '1',\n            backgroundColor: 'white',\n            borderRadius: '6px',\n            padding: '1.25rem',\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',\n            border: '1px solid #e5e7eb',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '0.75rem',\n            width: '48%',\n            maxWidth: '48%'\n          }} className=\"team-section team2\">\n            <h3><FaPlane /> AWAY TEAM</h3>\n            <label htmlFor=\"team2\" className=\"required-field\">Select Team 2:</label>\n            <select id=\"team2\" name=\"team2\" value={newChallenge.team2} onChange={handleInputChange} required>\n              <option value=\"\">Select a team</option>\n              {teams.map((team) => (\n                <option key={team.id} value={team.name}>\n                  {team.name}\n                </option>\n              ))}\n            </select>\n\n            {newChallenge.team2 ? (\n              <div className=\"logo-container\">\n                <img\n                  src={`${API_BASE_URL}/${teams.find(t => t.name === newChallenge.team2)?.logo}`}\n                  alt=\"Team 2 Logo\"\n                  className=\"logo-preview\"\n                />\n              </div>\n            ) : (\n              <div className=\"logo-container empty-logo\">\n                <FaPlane size={40} style={{ color: '#d1d5db' }} />\n              </div>\n            )}\n\n            <label htmlFor=\"odds2\" className=\"required-field\">\n              Odds for Team 2:\n              <span className=\"info-tooltip\" title=\"Multiplier for team 2 win\">\n                <FaInfoCircle size={12} style={{ marginLeft: '5px', color: '#6b7280' }} />\n              </span>\n            </label>\n            <input\n              type=\"number\"\n              id=\"odds2\"\n              name=\"odds2\"\n              value={newChallenge.odds2}\n              onChange={handleInputChange}\n              required\n              step=\"0.01\"\n              min=\"1\"\n            />\n            <p className=\"odds-explanation\">\n              User's bet x {newChallenge.odds2} = Potential winnings\n            </p>\n\n            <label htmlFor=\"goalAdvantage2\">Goal Advantage:</label>\n            <input\n              type=\"number\"\n              id=\"goalAdvantage2\"\n              name=\"goalAdvantage2\"\n              value={newChallenge.goalAdvantage2}\n              onChange={handleInputChange}\n            />\n          </div>\n        </div>\n\n        <div className=\"time-section\">\n          <h3><FaClock /> Time Settings</h3>\n\n          <div className=\"time-groups-container\">\n            <div className=\"time-group\">\n              <label htmlFor=\"startTime\" className=\"required-field\">\n                <FaCalendarAlt className=\"time-icon\" /> Challenge Start Time:\n              </label>\n              <input\n                type=\"datetime-local\"\n                id=\"startTime\"\n                name=\"startTime\"\n                value={newChallenge.startTime}\n                onChange={handleInputChange}\n                required\n              />\n            </div>\n\n            <div className=\"time-group\">\n              <label htmlFor=\"endTime\" className=\"required-field\">\n                <FaCalendarAlt className=\"time-icon\" /> Challenge End Time:\n              </label>\n              <input\n                type=\"datetime-local\"\n                id=\"endTime\"\n                name=\"endTime\"\n                value={newChallenge.endTime}\n                onChange={handleInputChange}\n                required\n              />\n            </div>\n\n            <div className=\"time-group\">\n              <label htmlFor=\"matchTime\" className=\"required-field\">\n                <FaCalendarAlt className=\"time-icon\" /> Actual Match Time:\n              </label>\n              <input\n                type=\"datetime-local\"\n                id=\"matchTime\"\n                name=\"matchTime\"\n                value={newChallenge.matchTime}\n                onChange={handleInputChange}\n                required\n              />\n            </div>\n          </div>\n        </div>\n      </form>\n\n      <div className=\"challenge-preview\">\n        <h3><FaFutbol /> Challenge Preview</h3>\n\n        {(!newChallenge.team1 || !newChallenge.team2) ? (\n          <div className=\"empty-preview\">\n            <FaInfoCircle />\n            <p>Select teams to see the challenge preview</p>\n          </div>\n        ) : (\n          <>\n            <div style={{\n              display: 'flex',\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              gap: '20px',\n              width: '100%',\n              position: 'relative',\n              alignItems: 'center',\n              marginBottom: '1.5rem'\n            }}>\n              <div style={{\n                flex: '1',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                textAlign: 'center',\n                width: '48%',\n                maxWidth: '48%'\n              }} className=\"preview-team\">\n                <div className=\"logo-container\">\n                  <img\n                    src={`${API_BASE_URL}/${teams.find(t => t.name === newChallenge.team1)?.logo}`}\n                    alt=\"Team 1 Logo\"\n                    className=\"logo-preview\"\n                  />\n                </div>\n                <div className=\"preview-team-name\">{newChallenge.team1}</div>\n                <div className=\"preview-odds\">Odds: {newChallenge.odds1}</div>\n                {newChallenge.goalAdvantage1 > 0 && (\n                  <div className=\"preview-advantage\">+{newChallenge.goalAdvantage1} Goal Advantage</div>\n                )}\n              </div>\n\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                width: '40px',\n                height: '40px',\n                backgroundColor: '#166534',\n                borderRadius: '50%',\n                color: 'white',\n                fontSize: '1.25rem',\n                fontWeight: '700',\n                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n                margin: '0 10px'\n              }} className=\"preview-vs\">VS</div>\n\n              <div style={{\n                flex: '1',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                textAlign: 'center',\n                width: '48%',\n                maxWidth: '48%'\n              }} className=\"preview-team\">\n                <div className=\"logo-container\">\n                  <img\n                    src={`${API_BASE_URL}/${teams.find(t => t.name === newChallenge.team2)?.logo}`}\n                    alt=\"Team 2 Logo\"\n                    className=\"logo-preview\"\n                  />\n                </div>\n                <div className=\"preview-team-name\">{newChallenge.team2}</div>\n                <div className=\"preview-odds\">Odds: {newChallenge.odds2}</div>\n                {newChallenge.goalAdvantage2 > 0 && (\n                  <div className=\"preview-advantage\">+{newChallenge.goalAdvantage2} Goal Advantage</div>\n                )}\n              </div>\n            </div>\n\n            <div className=\"preview-time-details\">\n              <p>\n                <span>Start Time</span>\n                <FaCalendarAlt className=\"time-detail-icon\" />\n                {newChallenge.startTime ? new Date(newChallenge.startTime).toLocaleString() : 'Not set'}\n              </p>\n              <p>\n                <span>End Time</span>\n                <FaCalendarAlt className=\"time-detail-icon\" />\n                {newChallenge.endTime ? new Date(newChallenge.endTime).toLocaleString() : 'Not set'}\n              </p>\n              <p>\n                <span>Match Time</span>\n                <FaCalendarAlt className=\"time-detail-icon\" />\n                {newChallenge.matchTime ? new Date(newChallenge.matchTime).toLocaleString() : 'Not set'}\n              </p>\n              <p>\n                <span>Match Type</span>\n                <FaFutbol className=\"time-detail-icon\" />\n                {newChallenge.matchType.replace('_', ' ').toUpperCase()}\n              </p>\n            </div>\n          </>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default ChallengeSystem;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,OAAO,EACPC,aAAa,EACbC,OAAO,EACPC,aAAa,EACbC,QAAQ,EACRC,YAAY,EACZC,aAAa,EACbC,qBAAqB,EACrBC,MAAM,EACNC,OAAO,QACF,gBAAgB;AACvB,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAC/B,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA;EACzB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC;IAC/C+B,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,GAAG;IACVC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACd8C,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM9C,KAAK,CAAC+C,GAAG,CAAC,GAAG/B,YAAY,+BAA+B,CAAC;MAChFU,QAAQ,CAACoB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACT,KAAK,CAAC,uBAAuB,EAAEQ,GAAG,CAAC;IAC7C;EACF,CAAC;EAED,MAAME,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC3B,eAAe,CAAC4B,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBhB,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI,CAACjB,YAAY,CAACE,KAAK,IAAI,CAACF,YAAY,CAACG,KAAK,IAAI,CAACH,YAAY,CAACI,KAAK,IAAI,CAACJ,YAAY,CAACK,KAAK,EAAE;MAC5FU,QAAQ,CAAC,mCAAmC,CAAC;MAC7C;IACF;IAEA,IAAI;MACF,MAAMiB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/B,MAAMC,SAAS,GAAGpC,KAAK,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,IAAI,KAAK1B,YAAY,CAACE,KAAK,CAAC;MAChE,MAAMmC,SAAS,GAAGvC,KAAK,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,IAAI,KAAK1B,YAAY,CAACG,KAAK,CAAC;MAEhE6B,QAAQ,CAACM,MAAM,CAAC,OAAO,EAAEtC,YAAY,CAACE,KAAK,CAAC;MAC5C8B,QAAQ,CAACM,MAAM,CAAC,OAAO,EAAEtC,YAAY,CAACG,KAAK,CAAC;MAC5C6B,QAAQ,CAACM,MAAM,CAAC,OAAO,EAAEtC,YAAY,CAACI,KAAK,CAAC;MAC5C4B,QAAQ,CAACM,MAAM,CAAC,OAAO,EAAEtC,YAAY,CAACK,KAAK,CAAC;MAC5C2B,QAAQ,CAACM,MAAM,CAAC,gBAAgB,EAAEtC,YAAY,CAACM,cAAc,CAAC;MAC9D0B,QAAQ,CAACM,MAAM,CAAC,gBAAgB,EAAEtC,YAAY,CAACO,cAAc,CAAC;MAC9DyB,QAAQ,CAACM,MAAM,CAAC,WAAW,EAAEtC,YAAY,CAACQ,SAAS,CAAC;MACpDwB,QAAQ,CAACM,MAAM,CAAC,SAAS,EAAEtC,YAAY,CAACS,OAAO,CAAC;MAChDuB,QAAQ,CAACM,MAAM,CAAC,WAAW,EAAEtC,YAAY,CAACU,SAAS,CAAC;MACpDsB,QAAQ,CAACM,MAAM,CAAC,WAAW,EAAEtC,YAAY,CAACW,SAAS,CAAC;MACpDqB,QAAQ,CAACM,MAAM,CAAC,UAAU,EAAEtC,YAAY,CAACY,QAAQ,CAAC;MAClDoB,QAAQ,CAACM,MAAM,CAAC,UAAU,EAAEtC,YAAY,CAACa,QAAQ,CAAC;MAClDmB,QAAQ,CAACM,MAAM,CAAC,OAAO,EAAEJ,SAAS,GAAG,GAAG7C,YAAY,IAAI6C,SAAS,CAACK,IAAI,EAAE,GAAG,EAAE,CAAC;MAC9EP,QAAQ,CAACM,MAAM,CAAC,OAAO,EAAED,SAAS,GAAG,GAAGhD,YAAY,IAAIgD,SAAS,CAACE,IAAI,EAAE,GAAG,EAAE,CAAC;MAE9E,MAAMpB,QAAQ,GAAG,MAAM9C,KAAK,CAACmE,IAAI,CAAC,sBAAsB,EAAER,QAAQ,EAAE;QAClES,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MACnD,CAAC,CAAC;MAEF,IAAItB,QAAQ,CAACE,IAAI,CAACL,OAAO,EAAE;QACzBC,UAAU,CAAC,iCAAiC,CAAC;QAC7ChB,eAAe,CAAC;UACdC,KAAK,EAAE,EAAE;UACTC,KAAK,EAAE,EAAE;UACTC,KAAK,EAAE,GAAG;UACVC,KAAK,EAAE,GAAG;UACVC,cAAc,EAAE,CAAC;UACjBC,cAAc,EAAE,CAAC;UACjBC,SAAS,EAAE,EAAE;UACbC,OAAO,EAAE,EAAE;UACXC,SAAS,EAAE,EAAE;UACbC,SAAS,EAAE,WAAW;UACtBC,QAAQ,EAAE,GAAG;UACbC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF6B,UAAU,CAAC,MAAM;UACfzB,UAAU,CAAC,EAAE,CAAC;QAChB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLF,QAAQ,CAACI,QAAQ,CAACE,IAAI,CAACsB,OAAO,IAAI,4BAA4B,CAAC;QAC/DD,UAAU,CAAC,MAAM;UACf3B,QAAQ,CAAC,EAAE,CAAC;QACd,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZP,QAAQ,CAAC,4BAA4B,CAAC;IACxC;EACF,CAAC;EAED,MAAM6B,aAAa,GAAGA,CAAA,KAAM;IAC1B3C,eAAe,CAAC;MACdC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE,GAAG;MAC5CC,cAAc,EAAE,CAAC;MAAEC,cAAc,EAAE,CAAC;MACpCC,SAAS,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,SAAS,EAAE,EAAE;MACzCC,SAAS,EAAE,WAAW;MACtBC,QAAQ,EAAE,GAAG;MACbC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFE,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;EAChB,CAAC;EAED,oBACE/B,OAAA;IAAK2D,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/B5D,OAAA;MAAA4D,QAAA,EAAI;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/BhE,OAAA;MAAK2D,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B5D,OAAA;QAAQiE,OAAO,EAAErB,YAAa;QAACsB,KAAK,EAAE;UAAEC,eAAe,EAAE,SAAS;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAR,QAAA,gBACnF5D,OAAA,CAACZ,MAAM;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVhE,OAAA;UAAA4D,QAAA,EAAM;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACThE,OAAA;QAAQiE,OAAO,EAAEP,aAAc;QAACQ,KAAK,EAAE;UAAEC,eAAe,EAAE,SAAS;UAAEC,KAAK,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAO,CAAE;QAAAT,QAAA,gBACpG5D,OAAA,CAACX,OAAO;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACXhE,OAAA;UAAA4D,QAAA,EAAM;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EACLpC,KAAK,iBACJ5B,OAAA;MAAK2D,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B5D,OAAA,CAACJ,qBAAqB;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxBpC,KAAK;IAAA;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EACAlC,OAAO,iBACN9B,OAAA;MAAK2D,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B5D,OAAA,CAACL,aAAa;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAChBlC,OAAO;IAAA;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eACDhE,OAAA;MAAMsE,QAAQ,EAAE1B,YAAa;MAACe,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBACtD5D,OAAA;QAAK2D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B5D,OAAA;UAAK2D,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC5D,OAAA;YAAA4D,QAAA,gBAAI5D,OAAA,CAACP,QAAQ;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAAe;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpChE,OAAA;YAAK2D,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC5D,OAAA;cAAK2D,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5D,OAAA;gBAAOuE,OAAO,EAAC,WAAW;gBAACZ,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzEhE,OAAA;gBACEwE,EAAE,EAAC,WAAW;gBACdhC,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAE3B,YAAY,CAACW,SAAU;gBAC9BgD,QAAQ,EAAEnC,iBAAkB;gBAC5BoC,QAAQ;gBAAAd,QAAA,gBAER5D,OAAA;kBAAQyC,KAAK,EAAC,WAAW;kBAAAmB,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5ChE,OAAA;kBAAQyC,KAAK,EAAC,WAAW;kBAAAmB,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5D,OAAA;gBAAOuE,OAAO,EAAC,UAAU;gBAAAX,QAAA,GAAC,YAExB,eAAA5D,OAAA;kBAAM2D,SAAS,EAAC,cAAc;kBAACgB,KAAK,EAAC,6BAA6B;kBAAAf,QAAA,eAChE5D,OAAA,CAACN,YAAY;oBAACkF,IAAI,EAAE,EAAG;oBAACV,KAAK,EAAE;sBAAEW,UAAU,EAAE,KAAK;sBAAET,KAAK,EAAE;oBAAU;kBAAE;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACRhE,OAAA;gBACE8E,IAAI,EAAC,QAAQ;gBACbN,EAAE,EAAC,UAAU;gBACbhC,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAE3B,YAAY,CAACY,QAAS;gBAC7B+C,QAAQ,EAAEnC,iBAAkB;gBAC5ByC,IAAI,EAAC,KAAK;gBACVC,GAAG,EAAC;cAAG;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACFhE,OAAA;gBAAG2D,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5D,OAAA;gBAAOuE,OAAO,EAAC,UAAU;gBAAAX,QAAA,GAAC,YAExB,eAAA5D,OAAA;kBAAM2D,SAAS,EAAC,cAAc;kBAACgB,KAAK,EAAC,6BAA6B;kBAAAf,QAAA,eAChE5D,OAAA,CAACN,YAAY;oBAACkF,IAAI,EAAE,EAAG;oBAACV,KAAK,EAAE;sBAAEW,UAAU,EAAE,KAAK;sBAAET,KAAK,EAAE;oBAAU;kBAAE;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACRhE,OAAA;gBACE8E,IAAI,EAAC,QAAQ;gBACbN,EAAE,EAAC,UAAU;gBACbhC,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAE3B,YAAY,CAACa,QAAS;gBAC7B8C,QAAQ,EAAEnC,iBAAkB;gBAC5ByC,IAAI,EAAC,KAAK;gBACVC,GAAG,EAAC;cAAG;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACFhE,OAAA;gBAAG2D,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhE,OAAA;QAAKkE,KAAK,EAAE;UACRe,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,KAAK;UACpBC,cAAc,EAAE,eAAe;UAC/BC,GAAG,EAAE,MAAM;UACXC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAA3B,QAAA,gBACF5D,OAAA;UAAKkE,KAAK,EAAE;YACVsB,IAAI,EAAE,GAAG;YACTrB,eAAe,EAAE,OAAO;YACxBsB,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE,+BAA+B;YAC1CtB,MAAM,EAAE,mBAAmB;YAC3BY,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBE,GAAG,EAAE,SAAS;YACdC,KAAK,EAAE,KAAK;YACZO,QAAQ,EAAE;UACZ,CAAE;UAACjC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAC/B5D,OAAA;YAAA4D,QAAA,gBAAI5D,OAAA,CAACH,MAAM;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAAU;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BhE,OAAA;YAAOuE,OAAO,EAAC,OAAO;YAACZ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxEhE,OAAA;YAAQwE,EAAE,EAAC,OAAO;YAAChC,IAAI,EAAC,OAAO;YAACC,KAAK,EAAE3B,YAAY,CAACE,KAAM;YAACyD,QAAQ,EAAEnC,iBAAkB;YAACoC,QAAQ;YAAAd,QAAA,gBAC9F5D,OAAA;cAAQyC,KAAK,EAAC,EAAE;cAAAmB,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACtCpD,KAAK,CAACiF,GAAG,CAAEC,IAAI,iBACd9F,OAAA;cAAsByC,KAAK,EAAEqD,IAAI,CAACtD,IAAK;cAAAoB,QAAA,EACpCkC,IAAI,CAACtD;YAAI,GADCsD,IAAI,CAACtB,EAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EAERlD,YAAY,CAACE,KAAK,gBACjBhB,OAAA;YAAK2D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7B5D,OAAA;cACE+F,GAAG,EAAE,GAAG5F,YAAY,KAAAG,WAAA,GAAIM,KAAK,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,IAAI,KAAK1B,YAAY,CAACE,KAAK,CAAC,cAAAV,WAAA,uBAA9CA,WAAA,CAAgD+C,IAAI,EAAG;cAC/E2C,GAAG,EAAC,aAAa;cACjBrC,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENhE,OAAA;YAAK2D,SAAS,EAAC,2BAA2B;YAAAC,QAAA,eACxC5D,OAAA,CAACH,MAAM;cAAC+E,IAAI,EAAE,EAAG;cAACV,KAAK,EAAE;gBAAEE,KAAK,EAAE;cAAU;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CACN,eAEDhE,OAAA;YAAOuE,OAAO,EAAC,OAAO;YAACZ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAAC,kBAEhD,eAAA5D,OAAA;cAAM2D,SAAS,EAAC,cAAc;cAACgB,KAAK,EAAC,2BAA2B;cAAAf,QAAA,eAC9D5D,OAAA,CAACN,YAAY;gBAACkF,IAAI,EAAE,EAAG;gBAACV,KAAK,EAAE;kBAAEW,UAAU,EAAE,KAAK;kBAAET,KAAK,EAAE;gBAAU;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACRhE,OAAA;YACE8E,IAAI,EAAC,QAAQ;YACbN,EAAE,EAAC,OAAO;YACVhC,IAAI,EAAC,OAAO;YACZC,KAAK,EAAE3B,YAAY,CAACI,KAAM;YAC1BuD,QAAQ,EAAEnC,iBAAkB;YAC5BoC,QAAQ;YACRK,IAAI,EAAC,MAAM;YACXC,GAAG,EAAC;UAAG;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACFhE,OAAA;YAAG2D,SAAS,EAAC,kBAAkB;YAAAC,QAAA,GAAC,eACjB,EAAC9C,YAAY,CAACI,KAAK,EAAC,uBACnC;UAAA;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJhE,OAAA;YAAOuE,OAAO,EAAC,gBAAgB;YAAAX,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvDhE,OAAA;YACE8E,IAAI,EAAC,QAAQ;YACbN,EAAE,EAAC,gBAAgB;YACnBhC,IAAI,EAAC,gBAAgB;YACrBC,KAAK,EAAE3B,YAAY,CAACM,cAAe;YACnCqD,QAAQ,EAAEnC;UAAkB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENhE,OAAA;UAAKkE,KAAK,EAAE;YACVe,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBJ,cAAc,EAAE,QAAQ;YACxBE,KAAK,EAAE,MAAM;YACbY,MAAM,EAAE,MAAM;YACd9B,eAAe,EAAE,SAAS;YAC1BsB,YAAY,EAAE,KAAK;YACnBrB,KAAK,EAAE,OAAO;YACd8B,QAAQ,EAAE,MAAM;YAChBP,SAAS,EAAE,8BAA8B;YACzCQ,MAAM,EAAE,IAAI;YACZC,SAAS,EAAE,QAAQ;YACnBC,MAAM,EAAE;UACV,CAAE;UAAC1C,SAAS,EAAC,YAAY;UAAAC,QAAA,eACvB5D,OAAA,CAACV,aAAa;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eAENhE,OAAA;UAAKkE,KAAK,EAAE;YACVsB,IAAI,EAAE,GAAG;YACTrB,eAAe,EAAE,OAAO;YACxBsB,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE,+BAA+B;YAC1CtB,MAAM,EAAE,mBAAmB;YAC3BY,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBE,GAAG,EAAE,SAAS;YACdC,KAAK,EAAE,KAAK;YACZO,QAAQ,EAAE;UACZ,CAAE;UAACjC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAC/B5D,OAAA;YAAA4D,QAAA,gBAAI5D,OAAA,CAACF,OAAO;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAAU;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BhE,OAAA;YAAOuE,OAAO,EAAC,OAAO;YAACZ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxEhE,OAAA;YAAQwE,EAAE,EAAC,OAAO;YAAChC,IAAI,EAAC,OAAO;YAACC,KAAK,EAAE3B,YAAY,CAACG,KAAM;YAACwD,QAAQ,EAAEnC,iBAAkB;YAACoC,QAAQ;YAAAd,QAAA,gBAC9F5D,OAAA;cAAQyC,KAAK,EAAC,EAAE;cAAAmB,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACtCpD,KAAK,CAACiF,GAAG,CAAEC,IAAI,iBACd9F,OAAA;cAAsByC,KAAK,EAAEqD,IAAI,CAACtD,IAAK;cAAAoB,QAAA,EACpCkC,IAAI,CAACtD;YAAI,GADCsD,IAAI,CAACtB,EAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EAERlD,YAAY,CAACG,KAAK,gBACjBjB,OAAA;YAAK2D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7B5D,OAAA;cACE+F,GAAG,EAAE,GAAG5F,YAAY,KAAAI,YAAA,GAAIK,KAAK,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,IAAI,KAAK1B,YAAY,CAACG,KAAK,CAAC,cAAAV,YAAA,uBAA9CA,YAAA,CAAgD8C,IAAI,EAAG;cAC/E2C,GAAG,EAAC,aAAa;cACjBrC,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENhE,OAAA;YAAK2D,SAAS,EAAC,2BAA2B;YAAAC,QAAA,eACxC5D,OAAA,CAACF,OAAO;cAAC8E,IAAI,EAAE,EAAG;cAACV,KAAK,EAAE;gBAAEE,KAAK,EAAE;cAAU;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CACN,eAEDhE,OAAA;YAAOuE,OAAO,EAAC,OAAO;YAACZ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAAC,kBAEhD,eAAA5D,OAAA;cAAM2D,SAAS,EAAC,cAAc;cAACgB,KAAK,EAAC,2BAA2B;cAAAf,QAAA,eAC9D5D,OAAA,CAACN,YAAY;gBAACkF,IAAI,EAAE,EAAG;gBAACV,KAAK,EAAE;kBAAEW,UAAU,EAAE,KAAK;kBAAET,KAAK,EAAE;gBAAU;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACRhE,OAAA;YACE8E,IAAI,EAAC,QAAQ;YACbN,EAAE,EAAC,OAAO;YACVhC,IAAI,EAAC,OAAO;YACZC,KAAK,EAAE3B,YAAY,CAACK,KAAM;YAC1BsD,QAAQ,EAAEnC,iBAAkB;YAC5BoC,QAAQ;YACRK,IAAI,EAAC,MAAM;YACXC,GAAG,EAAC;UAAG;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACFhE,OAAA;YAAG2D,SAAS,EAAC,kBAAkB;YAAAC,QAAA,GAAC,eACjB,EAAC9C,YAAY,CAACK,KAAK,EAAC,uBACnC;UAAA;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJhE,OAAA;YAAOuE,OAAO,EAAC,gBAAgB;YAAAX,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvDhE,OAAA;YACE8E,IAAI,EAAC,QAAQ;YACbN,EAAE,EAAC,gBAAgB;YACnBhC,IAAI,EAAC,gBAAgB;YACrBC,KAAK,EAAE3B,YAAY,CAACO,cAAe;YACnCoD,QAAQ,EAAEnC;UAAkB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhE,OAAA;QAAK2D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B5D,OAAA;UAAA4D,QAAA,gBAAI5D,OAAA,CAACT,OAAO;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAAc;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAElChE,OAAA;UAAK2D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpC5D,OAAA;YAAK2D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5D,OAAA;cAAOuE,OAAO,EAAC,WAAW;cAACZ,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBACnD5D,OAAA,CAACR,aAAa;gBAACmE,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,0BACzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhE,OAAA;cACE8E,IAAI,EAAC,gBAAgB;cACrBN,EAAE,EAAC,WAAW;cACdhC,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAE3B,YAAY,CAACQ,SAAU;cAC9BmD,QAAQ,EAAEnC,iBAAkB;cAC5BoC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhE,OAAA;YAAK2D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5D,OAAA;cAAOuE,OAAO,EAAC,SAAS;cAACZ,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBACjD5D,OAAA,CAACR,aAAa;gBAACmE,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBACzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhE,OAAA;cACE8E,IAAI,EAAC,gBAAgB;cACrBN,EAAE,EAAC,SAAS;cACZhC,IAAI,EAAC,SAAS;cACdC,KAAK,EAAE3B,YAAY,CAACS,OAAQ;cAC5BkD,QAAQ,EAAEnC,iBAAkB;cAC5BoC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhE,OAAA;YAAK2D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5D,OAAA;cAAOuE,OAAO,EAAC,WAAW;cAACZ,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBACnD5D,OAAA,CAACR,aAAa;gBAACmE,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uBACzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhE,OAAA;cACE8E,IAAI,EAAC,gBAAgB;cACrBN,EAAE,EAAC,WAAW;cACdhC,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAE3B,YAAY,CAACU,SAAU;cAC9BiD,QAAQ,EAAEnC,iBAAkB;cAC5BoC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPhE,OAAA;MAAK2D,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC5D,OAAA;QAAA4D,QAAA,gBAAI5D,OAAA,CAACP,QAAQ;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,sBAAkB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAErC,CAAClD,YAAY,CAACE,KAAK,IAAI,CAACF,YAAY,CAACG,KAAK,gBAC1CjB,OAAA;QAAK2D,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B5D,OAAA,CAACN,YAAY;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChBhE,OAAA;UAAA4D,QAAA,EAAG;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,gBAENhE,OAAA,CAAAE,SAAA;QAAA0D,QAAA,gBACE5D,OAAA;UAAKkE,KAAK,EAAE;YACVe,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,KAAK;YACpBC,cAAc,EAAE,eAAe;YAC/BC,GAAG,EAAE,MAAM;YACXC,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,QAAQ;YACpBe,YAAY,EAAE;UAChB,CAAE;UAAA1C,QAAA,gBACA5D,OAAA;YAAKkE,KAAK,EAAE;cACVsB,IAAI,EAAE,GAAG;cACTP,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,QAAQ;cACvBK,UAAU,EAAE,QAAQ;cACpBgB,SAAS,EAAE,QAAQ;cACnBlB,KAAK,EAAE,KAAK;cACZO,QAAQ,EAAE;YACZ,CAAE;YAACjC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB5D,OAAA;cAAK2D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7B5D,OAAA;gBACE+F,GAAG,EAAE,GAAG5F,YAAY,KAAAK,YAAA,GAAII,KAAK,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,IAAI,KAAK1B,YAAY,CAACE,KAAK,CAAC,cAAAR,YAAA,uBAA9CA,YAAA,CAAgD6C,IAAI,EAAG;gBAC/E2C,GAAG,EAAC,aAAa;gBACjBrC,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAE9C,YAAY,CAACE;YAAK;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7DhE,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,GAAC,QAAM,EAAC9C,YAAY,CAACI,KAAK;YAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC7DlD,YAAY,CAACM,cAAc,GAAG,CAAC,iBAC9BpB,OAAA;cAAK2D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,GAAC,GAAC,EAAC9C,YAAY,CAACM,cAAc,EAAC,iBAAe;YAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACtF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENhE,OAAA;YAAKkE,KAAK,EAAE;cACVe,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpBJ,cAAc,EAAE,QAAQ;cACxBE,KAAK,EAAE,MAAM;cACbY,MAAM,EAAE,MAAM;cACd9B,eAAe,EAAE,SAAS;cAC1BsB,YAAY,EAAE,KAAK;cACnBrB,KAAK,EAAE,OAAO;cACd8B,QAAQ,EAAE,SAAS;cACnBM,UAAU,EAAE,KAAK;cACjBb,SAAS,EAAE,8BAA8B;cACzCU,MAAM,EAAE;YACV,CAAE;YAAC1C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAElChE,OAAA;YAAKkE,KAAK,EAAE;cACVsB,IAAI,EAAE,GAAG;cACTP,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,QAAQ;cACvBK,UAAU,EAAE,QAAQ;cACpBgB,SAAS,EAAE,QAAQ;cACnBlB,KAAK,EAAE,KAAK;cACZO,QAAQ,EAAE;YACZ,CAAE;YAACjC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB5D,OAAA;cAAK2D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7B5D,OAAA;gBACE+F,GAAG,EAAE,GAAG5F,YAAY,KAAAM,YAAA,GAAIG,KAAK,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,IAAI,KAAK1B,YAAY,CAACG,KAAK,CAAC,cAAAR,YAAA,uBAA9CA,YAAA,CAAgD4C,IAAI,EAAG;gBAC/E2C,GAAG,EAAC,aAAa;gBACjBrC,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhE,OAAA;cAAK2D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAE9C,YAAY,CAACG;YAAK;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7DhE,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,GAAC,QAAM,EAAC9C,YAAY,CAACK,KAAK;YAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC7DlD,YAAY,CAACO,cAAc,GAAG,CAAC,iBAC9BrB,OAAA;cAAK2D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,GAAC,GAAC,EAAC9C,YAAY,CAACO,cAAc,EAAC,iBAAe;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACtF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhE,OAAA;UAAK2D,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC5D,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBhE,OAAA,CAACR,aAAa;cAACmE,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC7ClD,YAAY,CAACQ,SAAS,GAAG,IAAImF,IAAI,CAAC3F,YAAY,CAACQ,SAAS,CAAC,CAACoF,cAAc,CAAC,CAAC,GAAG,SAAS;UAAA;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC,eACJhE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrBhE,OAAA,CAACR,aAAa;cAACmE,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC7ClD,YAAY,CAACS,OAAO,GAAG,IAAIkF,IAAI,CAAC3F,YAAY,CAACS,OAAO,CAAC,CAACmF,cAAc,CAAC,CAAC,GAAG,SAAS;UAAA;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eACJhE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBhE,OAAA,CAACR,aAAa;cAACmE,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC7ClD,YAAY,CAACU,SAAS,GAAG,IAAIiF,IAAI,CAAC3F,YAAY,CAACU,SAAS,CAAC,CAACkF,cAAc,CAAC,CAAC,GAAG,SAAS;UAAA;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC,eACJhE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBhE,OAAA,CAACP,QAAQ;cAACkE,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACxClD,YAAY,CAACW,SAAS,CAACkF,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC;UAAA;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA,eACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC3D,EAAA,CA3gBQD,eAAe;AAAAyG,EAAA,GAAfzG,eAAe;AA6gBxB,eAAeA,eAAe;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}