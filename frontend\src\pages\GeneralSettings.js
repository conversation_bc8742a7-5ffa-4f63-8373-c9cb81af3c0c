import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaCog, FaCheck, FaTimes, FaSave, FaUpload } from 'react-icons/fa';
import { useSiteConfig } from '../contexts/SiteConfigContext';
import { updateFavicon, refreshFavicon } from '../utils/faviconUtils';

const API_BASE_URL = '/backend';

function GeneralSettings() {
    const { refreshConfig } = useSiteConfig();
    const [settings, setSettings] = useState({
        site_name: '',
        site_logo: '',
        contact_email: '',
        contact_phone: '',
        facebook_url: '',
        twitter_url: '',
        instagram_url: '',
        about_text: '',
        terms_conditions: '',
        privacy_policy: '',
        footer_text: ''
    });
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [logoPreview, setLogoPreview] = useState('');
    const [logoFile, setLogoFile] = useState(null);
    const [faviconPreview, setFaviconPreview] = useState('');
    const [faviconFile, setFaviconFile] = useState(null);
    const [testingSmtp, setTestingSmtp] = useState(false);

    useEffect(() => {
        fetchSettings();
        fetchFavicon();
    }, []);

    const fetchSettings = async () => {
        try {
            setLoading(true);
            const response = await axios.get(`${API_BASE_URL}/handlers/get_general_settings.php`);

            if (response.data.success && response.data.settings) {
                const formattedSettings = {};
                Object.keys(response.data.settings).forEach(key => {
                    formattedSettings[key] = response.data.settings[key].value;
                });
                setSettings(formattedSettings);

                // Set logo preview
                if (formattedSettings.site_logo) {
                    setLogoPreview(`${API_BASE_URL}/${formattedSettings.site_logo}`);
                }
            }
        } catch (err) {
            setError('Failed to load settings');
            console.error('Error fetching settings:', err);
        } finally {
            setLoading(false);
        }
    };

    const fetchFavicon = async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/get_favicon.php`);
            if (response.data.success && response.data.favicon_path) {
                setFaviconPreview(`${API_BASE_URL}/${response.data.favicon_path}`);
            } else if (response.data.public_favicon_exists) {
                setFaviconPreview('/favicon.ico');
            }
        } catch (err) {
            console.error('Error fetching favicon:', err);
            // Try to show default favicon
            setFaviconPreview('/favicon.ico');
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setSettings(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleLogoChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];
            if (!allowedTypes.includes(file.type)) {
                setError('Please select a valid image file (JPG, PNG, or SVG)');
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                setError('File size must be less than 5MB');
                return;
            }

            setLogoFile(file);
            setLogoPreview(URL.createObjectURL(file));
        }
    };

    const handleFaviconChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            // Validate file type for favicon
            const allowedTypes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/ico', 'image/icon', 'image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];
            const allowedExtensions = ['ico', 'png', 'jpg', 'jpeg', 'svg'];
            const fileExtension = file.name.split('.').pop().toLowerCase();

            if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
                setError('Please select a valid favicon file (ICO, PNG, JPG, or SVG)');
                return;
            }

            // Validate file size (max 2MB for favicon)
            if (file.size > 2 * 1024 * 1024) {
                setError('Favicon file size must be less than 2MB');
                return;
            }

            setFaviconFile(file);
            setFaviconPreview(URL.createObjectURL(file));
        }
    };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setSaving(true);
      setError('');
      setSuccess('');

      // First upload logo if a new one is selected
      let updatedSettings = { ...settings };

      if (logoFile) {
        const formData = new FormData();
        formData.append('logo', logoFile);

        const uploadResponse = await axios.post(`${API_BASE_URL}/handlers/upload_logo.php`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });

        if (uploadResponse.data.success) {
          updatedSettings.site_logo = uploadResponse.data.file_path;
        } else {
          throw new Error(uploadResponse.data.message || 'Failed to upload logo');
        }
      }

      // Upload favicon if a new one is selected
      if (faviconFile) {
        const faviconFormData = new FormData();
        faviconFormData.append('favicon', faviconFile);

        const faviconUploadResponse = await axios.post(`${API_BASE_URL}/handlers/upload_favicon.php`, faviconFormData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });

        if (faviconUploadResponse.data.success) {
          // Favicon is handled separately in the database, no need to add to settings
          console.log('Favicon uploaded successfully:', faviconUploadResponse.data.file_path);

          // Update the favicon in the browser immediately
          const newFaviconUrl = `${API_BASE_URL}/${faviconUploadResponse.data.file_path}`;
          updateFavicon(newFaviconUrl);

          // Update the preview
          setFaviconPreview(newFaviconUrl);
        } else {
          throw new Error(faviconUploadResponse.data.message || 'Failed to upload favicon');
        }
      }

      // Then update settings
      const response = await axios.post(`${API_BASE_URL}/handlers/update_general_settings.php`, {
        settings: updatedSettings
      });

      if (response.data.success) {
        setSuccess('Settings saved successfully!');
        setTimeout(() => setSuccess(''), 3000);
        // Update settings state with new logo path
        setSettings(updatedSettings);
        // Refresh site config to update throughout the app
        refreshConfig();
      } else {
        throw new Error(response.data.message || 'Failed to save general settings');
      }
    } catch (err) {
      setError(err.message || 'Failed to save settings. Please try again.');
      console.error('Error saving settings:', err);
    } finally {
      setSaving(false);
    }
  };

  const testSmtpConnection = async () => {
    try {
      setTestingSmtp(true);
      setError('');
      setSuccess('');

      const response = await axios.post(`${API_BASE_URL}/handlers/test_smtp.php`, {
        host: 'mail.hostinger.com',
        port: 465,
        username: '<EMAIL>',
        password: 'Money2025@Demo#',
        encryption: 'ssl',
        from_email: '<EMAIL>',
        from_name: 'FanBet247'
      });

      if (response.data.success) {
        setSuccess('SMTP connection test successful!');
      } else {
        setError(response.data.message || 'SMTP connection test failed');
      }
    } catch (err) {
      setError('Failed to test SMTP connection');
      console.error('Error testing SMTP:', err);
    } finally {
      setTestingSmtp(false);
    }
  };

    if (loading) {
        return (
            <div className="p-6">
                <div className="flex items-center justify-center h-64">
                    <div className="text-lg text-gray-600">Loading settings...</div>
                </div>
            </div>
        );
    }

    return (
        <div className="p-6">
            {/* Header */}
            <div className="mb-8">
                <h1 className="text-2xl font-bold text-gray-800 flex items-center gap-3">
                    <FaCog className="text-blue-500" />
                    General Settings
                </h1>
                <p className="text-gray-600 mt-2">
                    Configure basic system settings like site name, logo, and contact information.
                </p>
            </div>

            {/* Alerts */}
            {error && (
                <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
                    <FaTimes className="text-red-500" />
                    <span className="text-red-700">{error}</span>
                </div>
            )}

            {success && (
                <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3">
                    <FaCheck className="text-green-500" />
                    <span className="text-green-700">{success}</span>
                </div>
            )}

        <form onSubmit={handleSubmit} className="admin-form">
          <div className="form-section">
            <h2>Basic Information</h2>
            
            <div className="form-group">
              <label htmlFor="site_name">Site Name</label>
              <input
                type="text"
                id="site_name"
                name="site_name"
                value={settings.site_name}
                onChange={handleInputChange}
                required
                placeholder="FanBet247"
              />
            </div>

            <div className="form-group">
              <label htmlFor="site_logo">Site Logo</label>
              <div className="logo-upload-container">
                {logoPreview && (
                  <div className="logo-preview">
                    <img src={logoPreview} alt="Site Logo" />
                  </div>
                )}
                <div className="logo-upload">
                  <label htmlFor="logo_file" className="upload-button">
                    <FaUpload /> Upload Logo
                  </label>
                  <input
                    type="file"
                    id="logo_file"
                    accept="image/*"
                    onChange={handleLogoChange}
                    style={{ display: 'none' }}
                  />
                </div>
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="site_favicon">Site Favicon</label>
              <div className="favicon-upload-container">
                <div className="favicon-info">
                  <p className="favicon-description">
                    Upload a favicon for your site. Recommended formats: ICO, PNG (16x16, 32x32, or 48x48 pixels)
                  </p>
                </div>
                {faviconPreview && (
                  <div className="favicon-preview">
                    <img src={faviconPreview} alt="Site Favicon" style={{width: '32px', height: '32px'}} />
                    <span>Current Favicon</span>
                  </div>
                )}
                <div className="favicon-upload">
                  <label htmlFor="favicon_file" className="upload-button">
                    <FaUpload /> Upload Favicon
                  </label>
                  <input
                    type="file"
                    id="favicon_file"
                    accept=".ico,.png,.jpg,.jpeg,.svg"
                    onChange={handleFaviconChange}
                    style={{ display: 'none' }}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="form-section">
            <h2>Contact Information</h2>
            
            <div className="form-group">
              <label htmlFor="contact_email">Contact Email</label>
              <input
                type="email"
                id="contact_email"
                name="contact_email"
                value={settings.contact_email}
                onChange={handleInputChange}
                required
                placeholder="<EMAIL>"
              />
            </div>

            <div className="form-group">
              <label htmlFor="contact_phone">Contact Phone</label>
              <input
                type="text"
                id="contact_phone"
                name="contact_phone"
                value={settings.contact_phone}
                onChange={handleInputChange}
                placeholder="+1234567890"
              />
            </div>
          </div>

          <div className="form-section">
            <h2>Social Media</h2>
            
            <div className="form-group">
              <label htmlFor="facebook_url">Facebook URL</label>
              <input
                type="url"
                id="facebook_url"
                name="facebook_url"
                value={settings.facebook_url}
                onChange={handleInputChange}
                placeholder="https://facebook.com/fanbet247"
              />
            </div>

            <div className="form-group">
              <label htmlFor="twitter_url">Twitter URL</label>
              <input
                type="url"
                id="twitter_url"
                name="twitter_url"
                value={settings.twitter_url}
                onChange={handleInputChange}
                placeholder="https://twitter.com/fanbet247"
              />
            </div>

            <div className="form-group">
              <label htmlFor="instagram_url">Instagram URL</label>
              <input
                type="url"
                id="instagram_url"
                name="instagram_url"
                value={settings.instagram_url}
                onChange={handleInputChange}
                placeholder="https://instagram.com/fanbet247"
              />
            </div>
          </div>

          <div className="form-section">
            <h2>Content</h2>
            
            <div className="form-group">
              <label htmlFor="about_text">About Text</label>
              <textarea
                id="about_text"
                name="about_text"
                value={settings.about_text}
                onChange={handleInputChange}
                rows="4"
                placeholder="About FanBet247..."
              ></textarea>
            </div>

            <div className="form-group">
              <label htmlFor="terms_conditions">Terms & Conditions</label>
              <textarea
                id="terms_conditions"
                name="terms_conditions"
                value={settings.terms_conditions}
                onChange={handleInputChange}
                rows="6"
                placeholder="Terms and conditions text goes here..."
              ></textarea>
            </div>

            <div className="form-group">
              <label htmlFor="privacy_policy">Privacy Policy</label>
              <textarea
                id="privacy_policy"
                name="privacy_policy"
                value={settings.privacy_policy}
                onChange={handleInputChange}
                rows="6"
                placeholder="Privacy policy text goes here..."
              ></textarea>
            </div>

            <div className="form-group">
              <label htmlFor="footer_text">Footer Text</label>
              <input
                type="text"
                id="footer_text"
                name="footer_text"
                value={settings.footer_text}
                onChange={handleInputChange}
                placeholder="© 2024 FanBet247. All rights reserved."
              />
            </div>
          </div>

          <div className="form-section">
            <h2>SMTP Test</h2>
            <p>Test the SMTP connection with the configured settings (mail.hostinger.com:465)</p>
            <button
              type="button"
              onClick={testSmtpConnection}
              className="btn btn-secondary"
              disabled={testingSmtp}
            >
              {testingSmtp ? 'Testing...' : 'Test SMTP Connection'}
            </button>
          </div>

          <div className="form-actions">
            <button
              type="submit"
              className="btn btn-primary"
              disabled={saving}
            >
              {saving ? 'Saving...' : 'Save Settings'}
            </button>
          </div>
        </form>

        <style jsx>{`
          .favicon-upload-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
            border: 2px dashed #e2e8f0;
            border-radius: 8px;
            background-color: #f8fafc;
          }

          .favicon-info {
            margin-bottom: 0.5rem;
          }

          .favicon-description {
            font-size: 0.875rem;
            color: #64748b;
            margin: 0;
          }

          .favicon-preview {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background-color: white;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
          }

          .favicon-preview img {
            border: 1px solid #e2e8f0;
            border-radius: 4px;
          }

          .favicon-preview span {
            font-size: 0.875rem;
            color: #475569;
            font-weight: 500;
          }

          .favicon-upload {
            display: flex;
            justify-content: center;
          }

          .favicon-upload .upload-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            transition: background-color 0.2s;
          }

          .favicon-upload .upload-button:hover {
            background-color: #2563eb;
          }
        `}</style>
    </div>
  );
};

export default GeneralSettings;
