{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\GeneralSettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCog, FaCheck, FaTimes, FaSave, FaUpload } from 'react-icons/fa';\nimport { useSiteConfig } from '../contexts/SiteConfigContext';\nimport { updateFavicon, refreshFavicon } from '../utils/faviconUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction GeneralSettings() {\n  _s();\n  const {\n    refreshConfig\n  } = useSiteConfig();\n  const [settings, setSettings] = useState({\n    site_name: '',\n    site_logo: '',\n    contact_email: '',\n    contact_phone: '',\n    facebook_url: '',\n    twitter_url: '',\n    instagram_url: '',\n    about_text: '',\n    terms_conditions: '',\n    privacy_policy: '',\n    footer_text: ''\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [logoPreview, setLogoPreview] = useState('');\n  const [logoFile, setLogoFile] = useState(null);\n  const [faviconPreview, setFaviconPreview] = useState('');\n  const [faviconFile, setFaviconFile] = useState(null);\n  const [testingSmtp, setTestingSmtp] = useState(false);\n  useEffect(() => {\n    fetchSettings();\n    fetchFavicon();\n  }, []);\n  const fetchSettings = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_general_settings.php`);\n      if (response.data.success && response.data.settings) {\n        const formattedSettings = {};\n        Object.keys(response.data.settings).forEach(key => {\n          formattedSettings[key] = response.data.settings[key].value;\n        });\n        setSettings(formattedSettings);\n\n        // Set logo preview\n        if (formattedSettings.site_logo) {\n          setLogoPreview(`${API_BASE_URL}/${formattedSettings.site_logo}`);\n        }\n      }\n    } catch (err) {\n      setError('Failed to load settings');\n      console.error('Error fetching settings:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchFavicon = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_favicon.php`);\n      if (response.data.success && response.data.favicon_path) {\n        setFaviconPreview(`${API_BASE_URL}/${response.data.favicon_path}`);\n      } else if (response.data.public_favicon_exists) {\n        setFaviconPreview('/favicon.ico');\n      }\n    } catch (err) {\n      console.error('Error fetching favicon:', err);\n      // Try to show default favicon\n      setFaviconPreview('/favicon.ico');\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setSettings(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleLogoChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];\n      if (!allowedTypes.includes(file.type)) {\n        setError('Please select a valid image file (JPG, PNG, or SVG)');\n        return;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        setError('File size must be less than 5MB');\n        return;\n      }\n      setLogoFile(file);\n      setLogoPreview(URL.createObjectURL(file));\n    }\n  };\n  const handleFaviconChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type for favicon\n      const allowedTypes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/ico', 'image/icon', 'image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];\n      const allowedExtensions = ['ico', 'png', 'jpg', 'jpeg', 'svg'];\n      const fileExtension = file.name.split('.').pop().toLowerCase();\n      if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {\n        setError('Please select a valid favicon file (ICO, PNG, JPG, or SVG)');\n        return;\n      }\n\n      // Validate file size (max 2MB for favicon)\n      if (file.size > 2 * 1024 * 1024) {\n        setError('Favicon file size must be less than 2MB');\n        return;\n      }\n      setFaviconFile(file);\n      setFaviconPreview(URL.createObjectURL(file));\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      setSaving(true);\n      setError('');\n      setSuccess('');\n\n      // First upload logo if a new one is selected\n      let updatedSettings = {\n        ...settings\n      };\n      if (logoFile) {\n        const formData = new FormData();\n        formData.append('logo', logoFile);\n        const uploadResponse = await axios.post(`${API_BASE_URL}/handlers/upload_logo.php`, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        if (uploadResponse.data.success) {\n          updatedSettings.site_logo = uploadResponse.data.file_path;\n        } else {\n          throw new Error(uploadResponse.data.message || 'Failed to upload logo');\n        }\n      }\n\n      // Upload favicon if a new one is selected\n      if (faviconFile) {\n        const faviconFormData = new FormData();\n        faviconFormData.append('favicon', faviconFile);\n        const faviconUploadResponse = await axios.post(`${API_BASE_URL}/handlers/upload_favicon.php`, faviconFormData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        if (faviconUploadResponse.data.success) {\n          // Favicon is handled separately in the database, no need to add to settings\n          console.log('Favicon uploaded successfully:', faviconUploadResponse.data.file_path);\n        } else {\n          throw new Error(faviconUploadResponse.data.message || 'Failed to upload favicon');\n        }\n      }\n\n      // Then update settings\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_general_settings.php`, {\n        settings: updatedSettings\n      });\n      if (response.data.success) {\n        setSuccess('Settings saved successfully!');\n        setTimeout(() => setSuccess(''), 3000);\n        // Update settings state with new logo path\n        setSettings(updatedSettings);\n        // Refresh site config to update throughout the app\n        refreshConfig();\n      } else {\n        throw new Error(response.data.message || 'Failed to save general settings');\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to save settings. Please try again.');\n      console.error('Error saving settings:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n  const testSmtpConnection = async () => {\n    try {\n      setTestingSmtp(true);\n      setError('');\n      setSuccess('');\n      const response = await axios.post(`${API_BASE_URL}/handlers/test_smtp.php`, {\n        host: 'mail.hostinger.com',\n        port: 465,\n        username: '<EMAIL>',\n        password: 'Money2025@Demo#',\n        encryption: 'ssl',\n        from_email: '<EMAIL>',\n        from_name: 'FanBet247'\n      });\n      if (response.data.success) {\n        setSuccess('SMTP connection test successful!');\n      } else {\n        setError(response.data.message || 'SMTP connection test failed');\n      }\n    } catch (err) {\n      setError('Failed to test SMTP connection');\n      console.error('Error testing SMTP:', err);\n    } finally {\n      setTestingSmtp(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg text-gray-600\",\n          children: \"Loading settings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-800 flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(FaCog, {\n          className: \"text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 21\n        }, this), \"General Settings\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mt-2\",\n        children: \"Configure basic system settings like site name, logo, and contact information.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n        className: \"text-red-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-red-700\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n        className: \"text-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-green-700\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"admin-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Basic Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"site_name\",\n            children: \"Site Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"site_name\",\n            name: \"site_name\",\n            value: settings.site_name,\n            onChange: handleInputChange,\n            required: true,\n            placeholder: \"FanBet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"site_logo\",\n            children: \"Site Logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-upload-container\",\n            children: [logoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-preview\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: logoPreview,\n                alt: \"Site Logo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-upload\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"logo_file\",\n                className: \"upload-button\",\n                children: [/*#__PURE__*/_jsxDEV(FaUpload, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this), \" Upload Logo\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"logo_file\",\n                accept: \"image/*\",\n                onChange: handleLogoChange,\n                style: {\n                  display: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"site_favicon\",\n            children: \"Site Favicon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"favicon-upload-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"favicon-info\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"favicon-description\",\n                children: \"Upload a favicon for your site. Recommended formats: ICO, PNG (16x16, 32x32, or 48x48 pixels)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this), faviconPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"favicon-preview\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: faviconPreview,\n                alt: \"Site Favicon\",\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Current Favicon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"favicon-upload\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"favicon_file\",\n                className: \"upload-button\",\n                children: [/*#__PURE__*/_jsxDEV(FaUpload, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 21\n                }, this), \" Upload Favicon\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"favicon_file\",\n                accept: \".ico,.png,.jpg,.jpeg,.svg\",\n                onChange: handleFaviconChange,\n                style: {\n                  display: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Contact Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"contact_email\",\n            children: \"Contact Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"contact_email\",\n            name: \"contact_email\",\n            value: settings.contact_email,\n            onChange: handleInputChange,\n            required: true,\n            placeholder: \"<EMAIL>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"contact_phone\",\n            children: \"Contact Phone\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"contact_phone\",\n            name: \"contact_phone\",\n            value: settings.contact_phone,\n            onChange: handleInputChange,\n            placeholder: \"+1234567890\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Social Media\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"facebook_url\",\n            children: \"Facebook URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"url\",\n            id: \"facebook_url\",\n            name: \"facebook_url\",\n            value: settings.facebook_url,\n            onChange: handleInputChange,\n            placeholder: \"https://facebook.com/fanbet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"twitter_url\",\n            children: \"Twitter URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"url\",\n            id: \"twitter_url\",\n            name: \"twitter_url\",\n            value: settings.twitter_url,\n            onChange: handleInputChange,\n            placeholder: \"https://twitter.com/fanbet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"instagram_url\",\n            children: \"Instagram URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"url\",\n            id: \"instagram_url\",\n            name: \"instagram_url\",\n            value: settings.instagram_url,\n            onChange: handleInputChange,\n            placeholder: \"https://instagram.com/fanbet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Content\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"about_text\",\n            children: \"About Text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"about_text\",\n            name: \"about_text\",\n            value: settings.about_text,\n            onChange: handleInputChange,\n            rows: \"4\",\n            placeholder: \"About FanBet247...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"terms_conditions\",\n            children: \"Terms & Conditions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"terms_conditions\",\n            name: \"terms_conditions\",\n            value: settings.terms_conditions,\n            onChange: handleInputChange,\n            rows: \"6\",\n            placeholder: \"Terms and conditions text goes here...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"privacy_policy\",\n            children: \"Privacy Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"privacy_policy\",\n            name: \"privacy_policy\",\n            value: settings.privacy_policy,\n            onChange: handleInputChange,\n            rows: \"6\",\n            placeholder: \"Privacy policy text goes here...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"footer_text\",\n            children: \"Footer Text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"footer_text\",\n            name: \"footer_text\",\n            value: settings.footer_text,\n            onChange: handleInputChange,\n            placeholder: \"\\xA9 2024 FanBet247. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"SMTP Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Test the SMTP connection with the configured settings (mail.hostinger.com:465)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: testSmtpConnection,\n          className: \"btn btn-secondary\",\n          disabled: testingSmtp,\n          children: testingSmtp ? 'Testing...' : 'Test SMTP Connection'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: saving,\n          children: saving ? 'Saving...' : 'Save Settings'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n          .favicon-upload-container {\n            display: flex;\n            flex-direction: column;\n            gap: 1rem;\n            padding: 1rem;\n            border: 2px dashed #e2e8f0;\n            border-radius: 8px;\n            background-color: #f8fafc;\n          }\n\n          .favicon-info {\n            margin-bottom: 0.5rem;\n          }\n\n          .favicon-description {\n            font-size: 0.875rem;\n            color: #64748b;\n            margin: 0;\n          }\n\n          .favicon-preview {\n            display: flex;\n            align-items: center;\n            gap: 0.75rem;\n            padding: 0.75rem;\n            background-color: white;\n            border: 1px solid #e2e8f0;\n            border-radius: 6px;\n          }\n\n          .favicon-preview img {\n            border: 1px solid #e2e8f0;\n            border-radius: 4px;\n          }\n\n          .favicon-preview span {\n            font-size: 0.875rem;\n            color: #475569;\n            font-weight: 500;\n          }\n\n          .favicon-upload {\n            display: flex;\n            justify-content: center;\n          }\n\n          .favicon-upload .upload-button {\n            display: inline-flex;\n            align-items: center;\n            gap: 0.5rem;\n            padding: 0.75rem 1.5rem;\n            background-color: #3b82f6;\n            color: white;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-size: 0.875rem;\n            font-weight: 500;\n            transition: background-color 0.2s;\n          }\n\n          .favicon-upload .upload-button:hover {\n            background-color: #2563eb;\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 483,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 241,\n    columnNumber: 9\n  }, this);\n}\n_s(GeneralSettings, \"Ooy3NJXohrVCYBi0iGPiuyNs4O4=\", false, function () {\n  return [useSiteConfig];\n});\n_c = GeneralSettings;\n;\nexport default GeneralSettings;\nvar _c;\n$RefreshReg$(_c, \"GeneralSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaCog", "FaCheck", "FaTimes", "FaSave", "FaUpload", "useSiteConfig", "updateFavicon", "refreshFavicon", "jsxDEV", "_jsxDEV", "API_BASE_URL", "GeneralSettings", "_s", "refreshConfig", "settings", "setSettings", "site_name", "site_logo", "contact_email", "contact_phone", "facebook_url", "twitter_url", "instagram_url", "about_text", "terms_conditions", "privacy_policy", "footer_text", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "logoPreview", "setLogoPreview", "logoFile", "setLogoFile", "faviconPreview", "setFaviconPreview", "faviconFile", "setFaviconFile", "testingSmtp", "setTestingSmtp", "fetchSettings", "fetchFavicon", "response", "get", "data", "formattedSettings", "Object", "keys", "for<PERSON>ach", "key", "value", "err", "console", "favicon_path", "public_favicon_exists", "handleInputChange", "e", "name", "target", "prev", "handleLogoChange", "file", "files", "allowedTypes", "includes", "type", "size", "URL", "createObjectURL", "handleFaviconChange", "allowedExtensions", "fileExtension", "split", "pop", "toLowerCase", "handleSubmit", "preventDefault", "updatedSettings", "formData", "FormData", "append", "uploadResponse", "post", "headers", "file_path", "Error", "message", "faviconFormData", "faviconUploadResponse", "log", "setTimeout", "testSmtpConnection", "host", "port", "username", "password", "encryption", "from_email", "from_name", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "id", "onChange", "required", "placeholder", "src", "alt", "accept", "style", "display", "width", "height", "rows", "onClick", "disabled", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/GeneralSettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCog, FaCheck, FaTimes, FaSave, FaUpload } from 'react-icons/fa';\nimport { useSiteConfig } from '../contexts/SiteConfigContext';\nimport { updateFavicon, refreshFavicon } from '../utils/faviconUtils';\n\nconst API_BASE_URL = '/backend';\n\nfunction GeneralSettings() {\n    const { refreshConfig } = useSiteConfig();\n    const [settings, setSettings] = useState({\n        site_name: '',\n        site_logo: '',\n        contact_email: '',\n        contact_phone: '',\n        facebook_url: '',\n        twitter_url: '',\n        instagram_url: '',\n        about_text: '',\n        terms_conditions: '',\n        privacy_policy: '',\n        footer_text: ''\n    });\n    const [loading, setLoading] = useState(true);\n    const [saving, setSaving] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [logoPreview, setLogoPreview] = useState('');\n    const [logoFile, setLogoFile] = useState(null);\n    const [faviconPreview, setFaviconPreview] = useState('');\n    const [faviconFile, setFaviconFile] = useState(null);\n    const [testingSmtp, setTestingSmtp] = useState(false);\n\n    useEffect(() => {\n        fetchSettings();\n        fetchFavicon();\n    }, []);\n\n    const fetchSettings = async () => {\n        try {\n            setLoading(true);\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_general_settings.php`);\n\n            if (response.data.success && response.data.settings) {\n                const formattedSettings = {};\n                Object.keys(response.data.settings).forEach(key => {\n                    formattedSettings[key] = response.data.settings[key].value;\n                });\n                setSettings(formattedSettings);\n\n                // Set logo preview\n                if (formattedSettings.site_logo) {\n                    setLogoPreview(`${API_BASE_URL}/${formattedSettings.site_logo}`);\n                }\n            }\n        } catch (err) {\n            setError('Failed to load settings');\n            console.error('Error fetching settings:', err);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchFavicon = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_favicon.php`);\n            if (response.data.success && response.data.favicon_path) {\n                setFaviconPreview(`${API_BASE_URL}/${response.data.favicon_path}`);\n            } else if (response.data.public_favicon_exists) {\n                setFaviconPreview('/favicon.ico');\n            }\n        } catch (err) {\n            console.error('Error fetching favicon:', err);\n            // Try to show default favicon\n            setFaviconPreview('/favicon.ico');\n        }\n    };\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setSettings(prev => ({\n            ...prev,\n            [name]: value\n        }));\n    };\n\n    const handleLogoChange = (e) => {\n        const file = e.target.files[0];\n        if (file) {\n            // Validate file type\n            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];\n            if (!allowedTypes.includes(file.type)) {\n                setError('Please select a valid image file (JPG, PNG, or SVG)');\n                return;\n            }\n\n            // Validate file size (max 5MB)\n            if (file.size > 5 * 1024 * 1024) {\n                setError('File size must be less than 5MB');\n                return;\n            }\n\n            setLogoFile(file);\n            setLogoPreview(URL.createObjectURL(file));\n        }\n    };\n\n    const handleFaviconChange = (e) => {\n        const file = e.target.files[0];\n        if (file) {\n            // Validate file type for favicon\n            const allowedTypes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/ico', 'image/icon', 'image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];\n            const allowedExtensions = ['ico', 'png', 'jpg', 'jpeg', 'svg'];\n            const fileExtension = file.name.split('.').pop().toLowerCase();\n\n            if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {\n                setError('Please select a valid favicon file (ICO, PNG, JPG, or SVG)');\n                return;\n            }\n\n            // Validate file size (max 2MB for favicon)\n            if (file.size > 2 * 1024 * 1024) {\n                setError('Favicon file size must be less than 2MB');\n                return;\n            }\n\n            setFaviconFile(file);\n            setFaviconPreview(URL.createObjectURL(file));\n        }\n    };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      setSaving(true);\n      setError('');\n      setSuccess('');\n\n      // First upload logo if a new one is selected\n      let updatedSettings = { ...settings };\n\n      if (logoFile) {\n        const formData = new FormData();\n        formData.append('logo', logoFile);\n\n        const uploadResponse = await axios.post(`${API_BASE_URL}/handlers/upload_logo.php`, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n\n        if (uploadResponse.data.success) {\n          updatedSettings.site_logo = uploadResponse.data.file_path;\n        } else {\n          throw new Error(uploadResponse.data.message || 'Failed to upload logo');\n        }\n      }\n\n      // Upload favicon if a new one is selected\n      if (faviconFile) {\n        const faviconFormData = new FormData();\n        faviconFormData.append('favicon', faviconFile);\n\n        const faviconUploadResponse = await axios.post(`${API_BASE_URL}/handlers/upload_favicon.php`, faviconFormData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n\n        if (faviconUploadResponse.data.success) {\n          // Favicon is handled separately in the database, no need to add to settings\n          console.log('Favicon uploaded successfully:', faviconUploadResponse.data.file_path);\n        } else {\n          throw new Error(faviconUploadResponse.data.message || 'Failed to upload favicon');\n        }\n      }\n\n      // Then update settings\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_general_settings.php`, {\n        settings: updatedSettings\n      });\n\n      if (response.data.success) {\n        setSuccess('Settings saved successfully!');\n        setTimeout(() => setSuccess(''), 3000);\n        // Update settings state with new logo path\n        setSettings(updatedSettings);\n        // Refresh site config to update throughout the app\n        refreshConfig();\n      } else {\n        throw new Error(response.data.message || 'Failed to save general settings');\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to save settings. Please try again.');\n      console.error('Error saving settings:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const testSmtpConnection = async () => {\n    try {\n      setTestingSmtp(true);\n      setError('');\n      setSuccess('');\n\n      const response = await axios.post(`${API_BASE_URL}/handlers/test_smtp.php`, {\n        host: 'mail.hostinger.com',\n        port: 465,\n        username: '<EMAIL>',\n        password: 'Money2025@Demo#',\n        encryption: 'ssl',\n        from_email: '<EMAIL>',\n        from_name: 'FanBet247'\n      });\n\n      if (response.data.success) {\n        setSuccess('SMTP connection test successful!');\n      } else {\n        setError(response.data.message || 'SMTP connection test failed');\n      }\n    } catch (err) {\n      setError('Failed to test SMTP connection');\n      console.error('Error testing SMTP:', err);\n    } finally {\n      setTestingSmtp(false);\n    }\n  };\n\n    if (loading) {\n        return (\n            <div className=\"p-6\">\n                <div className=\"flex items-center justify-center h-64\">\n                    <div className=\"text-lg text-gray-600\">Loading settings...</div>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"p-6\">\n            {/* Header */}\n            <div className=\"mb-8\">\n                <h1 className=\"text-2xl font-bold text-gray-800 flex items-center gap-3\">\n                    <FaCog className=\"text-blue-500\" />\n                    General Settings\n                </h1>\n                <p className=\"text-gray-600 mt-2\">\n                    Configure basic system settings like site name, logo, and contact information.\n                </p>\n            </div>\n\n            {/* Alerts */}\n            {error && (\n                <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\">\n                    <FaTimes className=\"text-red-500\" />\n                    <span className=\"text-red-700\">{error}</span>\n                </div>\n            )}\n\n            {success && (\n                <div className=\"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\">\n                    <FaCheck className=\"text-green-500\" />\n                    <span className=\"text-green-700\">{success}</span>\n                </div>\n            )}\n\n        <form onSubmit={handleSubmit} className=\"admin-form\">\n          <div className=\"form-section\">\n            <h2>Basic Information</h2>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"site_name\">Site Name</label>\n              <input\n                type=\"text\"\n                id=\"site_name\"\n                name=\"site_name\"\n                value={settings.site_name}\n                onChange={handleInputChange}\n                required\n                placeholder=\"FanBet247\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"site_logo\">Site Logo</label>\n              <div className=\"logo-upload-container\">\n                {logoPreview && (\n                  <div className=\"logo-preview\">\n                    <img src={logoPreview} alt=\"Site Logo\" />\n                  </div>\n                )}\n                <div className=\"logo-upload\">\n                  <label htmlFor=\"logo_file\" className=\"upload-button\">\n                    <FaUpload /> Upload Logo\n                  </label>\n                  <input\n                    type=\"file\"\n                    id=\"logo_file\"\n                    accept=\"image/*\"\n                    onChange={handleLogoChange}\n                    style={{ display: 'none' }}\n                  />\n                </div>\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"site_favicon\">Site Favicon</label>\n              <div className=\"favicon-upload-container\">\n                <div className=\"favicon-info\">\n                  <p className=\"favicon-description\">\n                    Upload a favicon for your site. Recommended formats: ICO, PNG (16x16, 32x32, or 48x48 pixels)\n                  </p>\n                </div>\n                {faviconPreview && (\n                  <div className=\"favicon-preview\">\n                    <img src={faviconPreview} alt=\"Site Favicon\" style={{width: '32px', height: '32px'}} />\n                    <span>Current Favicon</span>\n                  </div>\n                )}\n                <div className=\"favicon-upload\">\n                  <label htmlFor=\"favicon_file\" className=\"upload-button\">\n                    <FaUpload /> Upload Favicon\n                  </label>\n                  <input\n                    type=\"file\"\n                    id=\"favicon_file\"\n                    accept=\".ico,.png,.jpg,.jpeg,.svg\"\n                    onChange={handleFaviconChange}\n                    style={{ display: 'none' }}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h2>Contact Information</h2>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"contact_email\">Contact Email</label>\n              <input\n                type=\"email\"\n                id=\"contact_email\"\n                name=\"contact_email\"\n                value={settings.contact_email}\n                onChange={handleInputChange}\n                required\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"contact_phone\">Contact Phone</label>\n              <input\n                type=\"text\"\n                id=\"contact_phone\"\n                name=\"contact_phone\"\n                value={settings.contact_phone}\n                onChange={handleInputChange}\n                placeholder=\"+1234567890\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h2>Social Media</h2>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"facebook_url\">Facebook URL</label>\n              <input\n                type=\"url\"\n                id=\"facebook_url\"\n                name=\"facebook_url\"\n                value={settings.facebook_url}\n                onChange={handleInputChange}\n                placeholder=\"https://facebook.com/fanbet247\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"twitter_url\">Twitter URL</label>\n              <input\n                type=\"url\"\n                id=\"twitter_url\"\n                name=\"twitter_url\"\n                value={settings.twitter_url}\n                onChange={handleInputChange}\n                placeholder=\"https://twitter.com/fanbet247\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"instagram_url\">Instagram URL</label>\n              <input\n                type=\"url\"\n                id=\"instagram_url\"\n                name=\"instagram_url\"\n                value={settings.instagram_url}\n                onChange={handleInputChange}\n                placeholder=\"https://instagram.com/fanbet247\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h2>Content</h2>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"about_text\">About Text</label>\n              <textarea\n                id=\"about_text\"\n                name=\"about_text\"\n                value={settings.about_text}\n                onChange={handleInputChange}\n                rows=\"4\"\n                placeholder=\"About FanBet247...\"\n              ></textarea>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"terms_conditions\">Terms & Conditions</label>\n              <textarea\n                id=\"terms_conditions\"\n                name=\"terms_conditions\"\n                value={settings.terms_conditions}\n                onChange={handleInputChange}\n                rows=\"6\"\n                placeholder=\"Terms and conditions text goes here...\"\n              ></textarea>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"privacy_policy\">Privacy Policy</label>\n              <textarea\n                id=\"privacy_policy\"\n                name=\"privacy_policy\"\n                value={settings.privacy_policy}\n                onChange={handleInputChange}\n                rows=\"6\"\n                placeholder=\"Privacy policy text goes here...\"\n              ></textarea>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"footer_text\">Footer Text</label>\n              <input\n                type=\"text\"\n                id=\"footer_text\"\n                name=\"footer_text\"\n                value={settings.footer_text}\n                onChange={handleInputChange}\n                placeholder=\"© 2024 FanBet247. All rights reserved.\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h2>SMTP Test</h2>\n            <p>Test the SMTP connection with the configured settings (mail.hostinger.com:465)</p>\n            <button\n              type=\"button\"\n              onClick={testSmtpConnection}\n              className=\"btn btn-secondary\"\n              disabled={testingSmtp}\n            >\n              {testingSmtp ? 'Testing...' : 'Test SMTP Connection'}\n            </button>\n          </div>\n\n          <div className=\"form-actions\">\n            <button\n              type=\"submit\"\n              className=\"btn btn-primary\"\n              disabled={saving}\n            >\n              {saving ? 'Saving...' : 'Save Settings'}\n            </button>\n          </div>\n        </form>\n\n        <style jsx>{`\n          .favicon-upload-container {\n            display: flex;\n            flex-direction: column;\n            gap: 1rem;\n            padding: 1rem;\n            border: 2px dashed #e2e8f0;\n            border-radius: 8px;\n            background-color: #f8fafc;\n          }\n\n          .favicon-info {\n            margin-bottom: 0.5rem;\n          }\n\n          .favicon-description {\n            font-size: 0.875rem;\n            color: #64748b;\n            margin: 0;\n          }\n\n          .favicon-preview {\n            display: flex;\n            align-items: center;\n            gap: 0.75rem;\n            padding: 0.75rem;\n            background-color: white;\n            border: 1px solid #e2e8f0;\n            border-radius: 6px;\n          }\n\n          .favicon-preview img {\n            border: 1px solid #e2e8f0;\n            border-radius: 4px;\n          }\n\n          .favicon-preview span {\n            font-size: 0.875rem;\n            color: #475569;\n            font-weight: 500;\n          }\n\n          .favicon-upload {\n            display: flex;\n            justify-content: center;\n          }\n\n          .favicon-upload .upload-button {\n            display: inline-flex;\n            align-items: center;\n            gap: 0.5rem;\n            padding: 0.75rem 1.5rem;\n            background-color: #3b82f6;\n            color: white;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-size: 0.875rem;\n            font-weight: 500;\n            transition: background-color 0.2s;\n          }\n\n          .favicon-upload .upload-button:hover {\n            background-color: #2563eb;\n          }\n        `}</style>\n    </div>\n  );\n};\n\nexport default GeneralSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AAC1E,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,aAAa,EAAEC,cAAc,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAc,CAAC,GAAGR,aAAa,CAAC,CAAC;EACzC,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC;IACrCmB,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,UAAU,EAAE,EAAE;IACdC,gBAAgB,EAAE,EAAE;IACpBC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,MAAM,EAAEC,SAAS,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAErDC,SAAS,CAAC,MAAM;IACZ+C,aAAa,CAAC,CAAC;IACfC,YAAY,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACAjB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMmB,QAAQ,GAAG,MAAMhD,KAAK,CAACiD,GAAG,CAAC,GAAGtC,YAAY,oCAAoC,CAAC;MAErF,IAAIqC,QAAQ,CAACE,IAAI,CAAChB,OAAO,IAAIc,QAAQ,CAACE,IAAI,CAACnC,QAAQ,EAAE;QACjD,MAAMoC,iBAAiB,GAAG,CAAC,CAAC;QAC5BC,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACE,IAAI,CAACnC,QAAQ,CAAC,CAACuC,OAAO,CAACC,GAAG,IAAI;UAC/CJ,iBAAiB,CAACI,GAAG,CAAC,GAAGP,QAAQ,CAACE,IAAI,CAACnC,QAAQ,CAACwC,GAAG,CAAC,CAACC,KAAK;QAC9D,CAAC,CAAC;QACFxC,WAAW,CAACmC,iBAAiB,CAAC;;QAE9B;QACA,IAAIA,iBAAiB,CAACjC,SAAS,EAAE;UAC7BmB,cAAc,CAAC,GAAG1B,YAAY,IAAIwC,iBAAiB,CAACjC,SAAS,EAAE,CAAC;QACpE;MACJ;IACJ,CAAC,CAAC,OAAOuC,GAAG,EAAE;MACVxB,QAAQ,CAAC,yBAAyB,CAAC;MACnCyB,OAAO,CAAC1B,KAAK,CAAC,0BAA0B,EAAEyB,GAAG,CAAC;IAClD,CAAC,SAAS;MACN5B,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMkB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMhD,KAAK,CAACiD,GAAG,CAAC,GAAGtC,YAAY,2BAA2B,CAAC;MAC5E,IAAIqC,QAAQ,CAACE,IAAI,CAAChB,OAAO,IAAIc,QAAQ,CAACE,IAAI,CAACS,YAAY,EAAE;QACrDlB,iBAAiB,CAAC,GAAG9B,YAAY,IAAIqC,QAAQ,CAACE,IAAI,CAACS,YAAY,EAAE,CAAC;MACtE,CAAC,MAAM,IAAIX,QAAQ,CAACE,IAAI,CAACU,qBAAqB,EAAE;QAC5CnB,iBAAiB,CAAC,cAAc,CAAC;MACrC;IACJ,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACVC,OAAO,CAAC1B,KAAK,CAAC,yBAAyB,EAAEyB,GAAG,CAAC;MAC7C;MACAhB,iBAAiB,CAAC,cAAc,CAAC;IACrC;EACJ,CAAC;EAED,MAAMoB,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEP;IAAM,CAAC,GAAGM,CAAC,CAACE,MAAM;IAChChD,WAAW,CAACiD,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACF,IAAI,GAAGP;IACZ,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMU,gBAAgB,GAAIJ,CAAC,IAAK;IAC5B,MAAMK,IAAI,GAAGL,CAAC,CAACE,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACN;MACA,MAAME,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,CAAC;MAC9E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;QACnCtC,QAAQ,CAAC,qDAAqD,CAAC;QAC/D;MACJ;;MAEA;MACA,IAAIkC,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC7BvC,QAAQ,CAAC,iCAAiC,CAAC;QAC3C;MACJ;MAEAM,WAAW,CAAC4B,IAAI,CAAC;MACjB9B,cAAc,CAACoC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC,CAAC;IAC7C;EACJ,CAAC;EAED,MAAMQ,mBAAmB,GAAIb,CAAC,IAAK;IAC/B,MAAMK,IAAI,GAAGL,CAAC,CAACE,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACN;MACA,MAAME,YAAY,GAAG,CAAC,cAAc,EAAE,0BAA0B,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,eAAe,CAAC;MACrJ,MAAMO,iBAAiB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;MAC9D,MAAMC,aAAa,GAAGV,IAAI,CAACJ,IAAI,CAACe,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAE9D,IAAI,CAACX,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,IAAI,CAACK,iBAAiB,CAACN,QAAQ,CAACO,aAAa,CAAC,EAAE;QACjF5C,QAAQ,CAAC,4DAA4D,CAAC;QACtE;MACJ;;MAEA;MACA,IAAIkC,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC7BvC,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACJ;MAEAU,cAAc,CAACwB,IAAI,CAAC;MACpB1B,iBAAiB,CAACgC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC,CAAC;IAChD;EACJ,CAAC;EAEH,MAAMc,YAAY,GAAG,MAAOnB,CAAC,IAAK;IAChCA,CAAC,CAACoB,cAAc,CAAC,CAAC;IAClB,IAAI;MACFnD,SAAS,CAAC,IAAI,CAAC;MACfE,QAAQ,CAAC,EAAE,CAAC;MACZE,UAAU,CAAC,EAAE,CAAC;;MAEd;MACA,IAAIgD,eAAe,GAAG;QAAE,GAAGpE;MAAS,CAAC;MAErC,IAAIuB,QAAQ,EAAE;QACZ,MAAM8C,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEhD,QAAQ,CAAC;QAEjC,MAAMiD,cAAc,GAAG,MAAMvF,KAAK,CAACwF,IAAI,CAAC,GAAG7E,YAAY,2BAA2B,EAAEyE,QAAQ,EAAE;UAC5FK,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIF,cAAc,CAACrC,IAAI,CAAChB,OAAO,EAAE;UAC/BiD,eAAe,CAACjE,SAAS,GAAGqE,cAAc,CAACrC,IAAI,CAACwC,SAAS;QAC3D,CAAC,MAAM;UACL,MAAM,IAAIC,KAAK,CAACJ,cAAc,CAACrC,IAAI,CAAC0C,OAAO,IAAI,uBAAuB,CAAC;QACzE;MACF;;MAEA;MACA,IAAIlD,WAAW,EAAE;QACf,MAAMmD,eAAe,GAAG,IAAIR,QAAQ,CAAC,CAAC;QACtCQ,eAAe,CAACP,MAAM,CAAC,SAAS,EAAE5C,WAAW,CAAC;QAE9C,MAAMoD,qBAAqB,GAAG,MAAM9F,KAAK,CAACwF,IAAI,CAAC,GAAG7E,YAAY,8BAA8B,EAAEkF,eAAe,EAAE;UAC7GJ,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIK,qBAAqB,CAAC5C,IAAI,CAAChB,OAAO,EAAE;UACtC;UACAwB,OAAO,CAACqC,GAAG,CAAC,gCAAgC,EAAED,qBAAqB,CAAC5C,IAAI,CAACwC,SAAS,CAAC;QACrF,CAAC,MAAM;UACL,MAAM,IAAIC,KAAK,CAACG,qBAAqB,CAAC5C,IAAI,CAAC0C,OAAO,IAAI,0BAA0B,CAAC;QACnF;MACF;;MAEA;MACA,MAAM5C,QAAQ,GAAG,MAAMhD,KAAK,CAACwF,IAAI,CAAC,GAAG7E,YAAY,uCAAuC,EAAE;QACxFI,QAAQ,EAAEoE;MACZ,CAAC,CAAC;MAEF,IAAInC,QAAQ,CAACE,IAAI,CAAChB,OAAO,EAAE;QACzBC,UAAU,CAAC,8BAA8B,CAAC;QAC1C6D,UAAU,CAAC,MAAM7D,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QACtC;QACAnB,WAAW,CAACmE,eAAe,CAAC;QAC5B;QACArE,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL,MAAM,IAAI6E,KAAK,CAAC3C,QAAQ,CAACE,IAAI,CAAC0C,OAAO,IAAI,iCAAiC,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOnC,GAAG,EAAE;MACZxB,QAAQ,CAACwB,GAAG,CAACmC,OAAO,IAAI,4CAA4C,CAAC;MACrElC,OAAO,CAAC1B,KAAK,CAAC,wBAAwB,EAAEyB,GAAG,CAAC;IAC9C,CAAC,SAAS;MACR1B,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMkE,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFpD,cAAc,CAAC,IAAI,CAAC;MACpBZ,QAAQ,CAAC,EAAE,CAAC;MACZE,UAAU,CAAC,EAAE,CAAC;MAEd,MAAMa,QAAQ,GAAG,MAAMhD,KAAK,CAACwF,IAAI,CAAC,GAAG7E,YAAY,yBAAyB,EAAE;QAC1EuF,IAAI,EAAE,oBAAoB;QAC1BC,IAAI,EAAE,GAAG;QACTC,QAAQ,EAAE,2BAA2B;QACrCC,QAAQ,EAAE,iBAAiB;QAC3BC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE,2BAA2B;QACvCC,SAAS,EAAE;MACb,CAAC,CAAC;MAEF,IAAIxD,QAAQ,CAACE,IAAI,CAAChB,OAAO,EAAE;QACzBC,UAAU,CAAC,kCAAkC,CAAC;MAChD,CAAC,MAAM;QACLF,QAAQ,CAACe,QAAQ,CAACE,IAAI,CAAC0C,OAAO,IAAI,6BAA6B,CAAC;MAClE;IACF,CAAC,CAAC,OAAOnC,GAAG,EAAE;MACZxB,QAAQ,CAAC,gCAAgC,CAAC;MAC1CyB,OAAO,CAAC1B,KAAK,CAAC,qBAAqB,EAAEyB,GAAG,CAAC;IAC3C,CAAC,SAAS;MACRZ,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAEC,IAAIjB,OAAO,EAAE;IACT,oBACIlB,OAAA;MAAK+F,SAAS,EAAC,KAAK;MAAAC,QAAA,eAChBhG,OAAA;QAAK+F,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eAClDhG,OAAA;UAAK+F,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIpG,OAAA;IAAK+F,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAEhBhG,OAAA;MAAK+F,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBhG,OAAA;QAAI+F,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACpEhG,OAAA,CAACT,KAAK;UAACwG,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLpG,OAAA;QAAG+F,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9E,KAAK,iBACFtB,OAAA;MAAK+F,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBACxFhG,OAAA,CAACP,OAAO;QAACsG,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpCpG,OAAA;QAAM+F,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAE1E;MAAK;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CACR,EAEA5E,OAAO,iBACJxB,OAAA;MAAK+F,SAAS,EAAC,iFAAiF;MAAAC,QAAA,gBAC5FhG,OAAA,CAACR,OAAO;QAACuG,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtCpG,OAAA;QAAM+F,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAExE;MAAO;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CACR,eAELpG,OAAA;MAAMqG,QAAQ,EAAE9B,YAAa;MAACwB,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAClDhG,OAAA;QAAK+F,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhG,OAAA;UAAAgG,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE1BpG,OAAA;UAAK+F,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhG,OAAA;YAAOsG,OAAO,EAAC,WAAW;YAAAN,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CpG,OAAA;YACE6D,IAAI,EAAC,MAAM;YACX0C,EAAE,EAAC,WAAW;YACdlD,IAAI,EAAC,WAAW;YAChBP,KAAK,EAAEzC,QAAQ,CAACE,SAAU;YAC1BiG,QAAQ,EAAErD,iBAAkB;YAC5BsD,QAAQ;YACRC,WAAW,EAAC;UAAW;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpG,OAAA;UAAK+F,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhG,OAAA;YAAOsG,OAAO,EAAC,WAAW;YAAAN,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CpG,OAAA;YAAK+F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GACnCtE,WAAW,iBACV1B,OAAA;cAAK+F,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BhG,OAAA;gBAAK2G,GAAG,EAAEjF,WAAY;gBAACkF,GAAG,EAAC;cAAW;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CACN,eACDpG,OAAA;cAAK+F,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhG,OAAA;gBAAOsG,OAAO,EAAC,WAAW;gBAACP,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAClDhG,OAAA,CAACL,QAAQ;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBACd;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpG,OAAA;gBACE6D,IAAI,EAAC,MAAM;gBACX0C,EAAE,EAAC,WAAW;gBACdM,MAAM,EAAC,SAAS;gBAChBL,QAAQ,EAAEhD,gBAAiB;gBAC3BsD,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAO;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpG,OAAA;UAAK+F,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhG,OAAA;YAAOsG,OAAO,EAAC,cAAc;YAAAN,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClDpG,OAAA;YAAK+F,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvChG,OAAA;cAAK+F,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BhG,OAAA;gBAAG+F,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAEnC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EACLtE,cAAc,iBACb9B,OAAA;cAAK+F,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BhG,OAAA;gBAAK2G,GAAG,EAAE7E,cAAe;gBAAC8E,GAAG,EAAC,cAAc;gBAACE,KAAK,EAAE;kBAACE,KAAK,EAAE,MAAM;kBAAEC,MAAM,EAAE;gBAAM;cAAE;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvFpG,OAAA;gBAAAgG,QAAA,EAAM;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CACN,eACDpG,OAAA;cAAK+F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhG,OAAA;gBAAOsG,OAAO,EAAC,cAAc;gBAACP,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBACrDhG,OAAA,CAACL,QAAQ;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mBACd;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpG,OAAA;gBACE6D,IAAI,EAAC,MAAM;gBACX0C,EAAE,EAAC,cAAc;gBACjBM,MAAM,EAAC,2BAA2B;gBAClCL,QAAQ,EAAEvC,mBAAoB;gBAC9B6C,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAO;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpG,OAAA;QAAK+F,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhG,OAAA;UAAAgG,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE5BpG,OAAA;UAAK+F,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhG,OAAA;YAAOsG,OAAO,EAAC,eAAe;YAAAN,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpDpG,OAAA;YACE6D,IAAI,EAAC,OAAO;YACZ0C,EAAE,EAAC,eAAe;YAClBlD,IAAI,EAAC,eAAe;YACpBP,KAAK,EAAEzC,QAAQ,CAACI,aAAc;YAC9B+F,QAAQ,EAAErD,iBAAkB;YAC5BsD,QAAQ;YACRC,WAAW,EAAC;UAAuB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpG,OAAA;UAAK+F,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhG,OAAA;YAAOsG,OAAO,EAAC,eAAe;YAAAN,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpDpG,OAAA;YACE6D,IAAI,EAAC,MAAM;YACX0C,EAAE,EAAC,eAAe;YAClBlD,IAAI,EAAC,eAAe;YACpBP,KAAK,EAAEzC,QAAQ,CAACK,aAAc;YAC9B8F,QAAQ,EAAErD,iBAAkB;YAC5BuD,WAAW,EAAC;UAAa;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpG,OAAA;QAAK+F,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhG,OAAA;UAAAgG,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAErBpG,OAAA;UAAK+F,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhG,OAAA;YAAOsG,OAAO,EAAC,cAAc;YAAAN,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClDpG,OAAA;YACE6D,IAAI,EAAC,KAAK;YACV0C,EAAE,EAAC,cAAc;YACjBlD,IAAI,EAAC,cAAc;YACnBP,KAAK,EAAEzC,QAAQ,CAACM,YAAa;YAC7B6F,QAAQ,EAAErD,iBAAkB;YAC5BuD,WAAW,EAAC;UAAgC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpG,OAAA;UAAK+F,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhG,OAAA;YAAOsG,OAAO,EAAC,aAAa;YAAAN,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChDpG,OAAA;YACE6D,IAAI,EAAC,KAAK;YACV0C,EAAE,EAAC,aAAa;YAChBlD,IAAI,EAAC,aAAa;YAClBP,KAAK,EAAEzC,QAAQ,CAACO,WAAY;YAC5B4F,QAAQ,EAAErD,iBAAkB;YAC5BuD,WAAW,EAAC;UAA+B;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpG,OAAA;UAAK+F,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhG,OAAA;YAAOsG,OAAO,EAAC,eAAe;YAAAN,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpDpG,OAAA;YACE6D,IAAI,EAAC,KAAK;YACV0C,EAAE,EAAC,eAAe;YAClBlD,IAAI,EAAC,eAAe;YACpBP,KAAK,EAAEzC,QAAQ,CAACQ,aAAc;YAC9B2F,QAAQ,EAAErD,iBAAkB;YAC5BuD,WAAW,EAAC;UAAiC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpG,OAAA;QAAK+F,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhG,OAAA;UAAAgG,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEhBpG,OAAA;UAAK+F,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhG,OAAA;YAAOsG,OAAO,EAAC,YAAY;YAAAN,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9CpG,OAAA;YACEuG,EAAE,EAAC,YAAY;YACflD,IAAI,EAAC,YAAY;YACjBP,KAAK,EAAEzC,QAAQ,CAACS,UAAW;YAC3B0F,QAAQ,EAAErD,iBAAkB;YAC5B+D,IAAI,EAAC,GAAG;YACRR,WAAW,EAAC;UAAoB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENpG,OAAA;UAAK+F,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhG,OAAA;YAAOsG,OAAO,EAAC,kBAAkB;YAAAN,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5DpG,OAAA;YACEuG,EAAE,EAAC,kBAAkB;YACrBlD,IAAI,EAAC,kBAAkB;YACvBP,KAAK,EAAEzC,QAAQ,CAACU,gBAAiB;YACjCyF,QAAQ,EAAErD,iBAAkB;YAC5B+D,IAAI,EAAC,GAAG;YACRR,WAAW,EAAC;UAAwC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENpG,OAAA;UAAK+F,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhG,OAAA;YAAOsG,OAAO,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtDpG,OAAA;YACEuG,EAAE,EAAC,gBAAgB;YACnBlD,IAAI,EAAC,gBAAgB;YACrBP,KAAK,EAAEzC,QAAQ,CAACW,cAAe;YAC/BwF,QAAQ,EAAErD,iBAAkB;YAC5B+D,IAAI,EAAC,GAAG;YACRR,WAAW,EAAC;UAAkC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENpG,OAAA;UAAK+F,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhG,OAAA;YAAOsG,OAAO,EAAC,aAAa;YAAAN,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChDpG,OAAA;YACE6D,IAAI,EAAC,MAAM;YACX0C,EAAE,EAAC,aAAa;YAChBlD,IAAI,EAAC,aAAa;YAClBP,KAAK,EAAEzC,QAAQ,CAACY,WAAY;YAC5BuF,QAAQ,EAAErD,iBAAkB;YAC5BuD,WAAW,EAAC;UAAwC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpG,OAAA;QAAK+F,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhG,OAAA;UAAAgG,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClBpG,OAAA;UAAAgG,QAAA,EAAG;QAA8E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrFpG,OAAA;UACE6D,IAAI,EAAC,QAAQ;UACbsD,OAAO,EAAE5B,kBAAmB;UAC5BQ,SAAS,EAAC,mBAAmB;UAC7BqB,QAAQ,EAAElF,WAAY;UAAA8D,QAAA,EAErB9D,WAAW,GAAG,YAAY,GAAG;QAAsB;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENpG,OAAA;QAAK+F,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BhG,OAAA;UACE6D,IAAI,EAAC,QAAQ;UACbkC,SAAS,EAAC,iBAAiB;UAC3BqB,QAAQ,EAAEhG,MAAO;UAAA4E,QAAA,EAEhB5E,MAAM,GAAG,WAAW,GAAG;QAAe;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPpG,OAAA;MAAOqH,GAAG;MAAArB,QAAA,EAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV;AAACjG,EAAA,CA9hBQD,eAAe;EAAA,QACMN,aAAa;AAAA;AAAA0H,EAAA,GADlCpH,eAAe;AA8hBvB;AAED,eAAeA,eAAe;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}