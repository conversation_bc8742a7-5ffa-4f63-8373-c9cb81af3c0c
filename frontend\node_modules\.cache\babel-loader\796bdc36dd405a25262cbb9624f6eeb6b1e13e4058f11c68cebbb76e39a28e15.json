{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\Friends.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { FaUserMinus, FaEnvelope, FaSearch, FaExclamationCircle, FaUserFriends, FaTrophy, FaStar, FaUserPlus, FaUserCircle } from 'react-icons/fa';\nimport './Friends.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction Friends() {\n  _s();\n  const [friends, setFriends] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showMessageModal, setShowMessageModal] = useState(false);\n  const [selectedFriend, setSelectedFriend] = useState(null);\n  const [messageText, setMessageText] = useState('');\n  const [messageStatus, setMessageStatus] = useState({\n    show: false,\n    type: '',\n    message: ''\n  });\n  const userId = localStorage.getItem('userId');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchType, setSearchType] = useState('friends'); // 'friends' or 'new'\n  const [newFriendResults, setNewFriendResults] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\n  const [friendToUnfriend, setFriendToUnfriend] = useState(null);\n  const navigate = useNavigate();\n  useEffect(() => {\n    fetchFriends();\n    fetchTeams();\n  }, []);\n  const fetchFriends = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching friends for user:', userId);\n      const response = await axios.get(`${API_BASE_URL}/handlers/friends.php?user_id=${userId}`);\n      console.log('Friends response:', response.data);\n      if (response.data.success) {\n        setFriends(response.data.friends || []);\n      } else {\n        setError(response.data.message || 'Failed to fetch friends');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error fetching friends:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'An error occurred while fetching friends');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchTeams = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      if (response.data.status === 200) {\n        setTeams(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching teams:', error);\n    }\n  };\n  const getTeamLogo = teamName => {\n    const team = teams.find(team => team.name === teamName);\n    return team ? `${API_BASE_URL}/${team.logo}` : '';\n  };\n  const handleUnfriend = async friend => {\n    setFriendToUnfriend(friend);\n    setShowConfirmModal(true);\n  };\n  const confirmUnfriend = async () => {\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/friends.php`, {\n        action: 'unfriend',\n        user_id: userId,\n        friend_id: friendToUnfriend.id\n      });\n      if (response.data.success) {\n        setFriends(friends.filter(friend => friend.id !== friendToUnfriend.id));\n        setMessageStatus({\n          show: true,\n          type: 'success',\n          message: 'Friend removed successfully'\n        });\n      } else {\n        setMessageStatus({\n          show: true,\n          type: 'error',\n          message: 'Failed to remove friend'\n        });\n      }\n    } catch (error) {\n      setMessageStatus({\n        show: true,\n        type: 'error',\n        message: 'An error occurred'\n      });\n      console.error('Error:', error);\n    } finally {\n      setShowConfirmModal(false);\n      setFriendToUnfriend(null);\n    }\n  };\n  const handleSendMessage = async () => {\n    if (!messageText.trim()) {\n      setMessageStatus({\n        show: true,\n        type: 'error',\n        message: 'Please enter a message'\n      });\n      return;\n    }\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/send_message.php`, {\n        sender_id: userId,\n        receiver_id: selectedFriend.id,\n        message: messageText\n      });\n      if (response.data.success) {\n        setMessageStatus({\n          show: true,\n          type: 'success',\n          message: 'Message sent successfully'\n        });\n        setMessageText('');\n        setShowMessageModal(false);\n      } else {\n        setMessageStatus({\n          show: true,\n          type: 'error',\n          message: response.data.message || 'Failed to send message'\n        });\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Error sending message:', error);\n      setMessageStatus({\n        show: true,\n        type: 'error',\n        message: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'An error occurred while sending the message'\n      });\n    }\n  };\n  const handleFriendSearch = () => {\n    if (!searchQuery.trim()) {\n      setMessageStatus({\n        show: true,\n        type: 'error',\n        message: 'Please enter a username to search your friends list'\n      });\n      return;\n    }\n    const filteredFriends = friends.filter(friend => friend.username.toLowerCase().includes(searchQuery.toLowerCase()));\n    if (filteredFriends.length === 0) {\n      setMessageStatus({\n        show: true,\n        type: 'info',\n        message: `No friends found with username \"${searchQuery}\". Try using \"Find New Friends\" to search for new users.`\n      });\n      // Keep showing existing friends\n      setFriends(friends);\n    } else {\n      setFriends(filteredFriends);\n      setMessageStatus({\n        show: true,\n        type: 'success',\n        message: `Found ${filteredFriends.length} friend${filteredFriends.length > 1 ? 's' : ''}`\n      });\n    }\n  };\n  const handleNewFriendSearch = async () => {\n    if (!searchQuery.trim()) {\n      setMessageStatus({\n        show: true,\n        type: 'error',\n        message: 'Please enter a username to search for new friends'\n      });\n      return;\n    }\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/search_users.php?query=${searchQuery}`);\n      if (response.data.success) {\n        const currentUserId = localStorage.getItem('userId');\n        // Filter out current user and existing friends\n        const filteredUsers = response.data.users.filter(user => user.user_id !== currentUserId && !friends.some(friend => friend.id === user.user_id));\n        if (filteredUsers.length === 0) {\n          setMessageStatus({\n            show: true,\n            type: 'info',\n            message: `No new users found with username \"${searchQuery}\". Try a different username or check your spelling.`\n          });\n          setNewFriendResults([]);\n        } else {\n          setNewFriendResults(filteredUsers);\n          setMessageStatus({\n            show: true,\n            type: 'success',\n            message: `Found ${filteredUsers.length} potential new friend${filteredUsers.length > 1 ? 's' : ''}`\n          });\n        }\n      }\n    } catch (error) {\n      setMessageStatus({\n        show: true,\n        type: 'error',\n        message: 'Error searching for users. Please try again.'\n      });\n      console.error('Error searching users:', error);\n    }\n  };\n  const sendFriendRequest = async userId => {\n    try {\n      const response = await axios.post('add_friend.php', {\n        user_id: localStorage.getItem('userId'),\n        friend_id: userId,\n        action: 'request'\n      });\n      if (response.data.success) {\n        setMessageStatus({\n          show: true,\n          type: 'success',\n          message: 'Friend request sent successfully!'\n        });\n        // Remove user from search results\n        setNewFriendResults(prev => prev.filter(user => user.user_id !== userId));\n      }\n    } catch (error) {\n      setMessageStatus({\n        show: true,\n        type: 'error',\n        message: 'Failed to send friend request'\n      });\n    }\n  };\n  const handleViewProfile = username => {\n    navigate(`/user/profile/${username}`);\n  };\n  const handleChallenge = friend => {\n    // Handle challenge logic\n    console.log('Challenge friend:', friend);\n    // You can implement the challenge functionality here\n  };\n  const renderFriendsList = () => {\n    if (loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 20\n      }, this);\n    }\n    if (error) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-status error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 20\n      }, this);\n    }\n    if (friends.length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-status info\",\n        children: \"No friends found.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 20\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"friends-grid\",\n      children: friends.map(friend => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"friend-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"friend-banner\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"friend-avatar\",\n            children: friend.favorite_team ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: getTeamLogo(friend.favorite_team),\n              alt: friend.favorite_team,\n              className: \"team-avatar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 37\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"avatar-placeholder\",\n              children: friend.username.charAt(0).toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"friend-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              onClick: () => handleViewProfile(friend.username),\n              style: {\n                cursor: 'pointer'\n              },\n              children: friend.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 33\n            }, this), friend.favorite_team && /*#__PURE__*/_jsxDEV(\"span\", {\n              children: friend.favorite_team\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 58\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"friend-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Total Bets\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: friend.total_bets\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Win Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: [friend.win_rate, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: friend.leaderboard_score || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"friend-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"message-btn\",\n            onClick: () => {\n              setSelectedFriend(friend);\n              setShowMessageModal(true);\n            },\n            children: \"Message\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"unfriend-btn\",\n            onClick: () => handleUnfriend(friend),\n            children: [/*#__PURE__*/_jsxDEV(FaUserMinus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 33\n            }, this), \" Unfriend\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 25\n        }, this)]\n      }, friend.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 13\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"friends-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"friends-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"friends-title-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"My Friends\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"friends-count\",\n          children: [friends.length, \" Friends\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"friends-search-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-type-toggle\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `toggle-btn ${searchType === 'friends' ? 'active' : ''}`,\n            onClick: () => {\n              setSearchType('friends');\n              setSearchQuery('');\n              setNewFriendResults([]);\n              setMessageStatus({\n                show: false\n              });\n            },\n            children: \"Search My Friends\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `toggle-btn ${searchType === 'new' ? 'active' : ''}`,\n            onClick: () => {\n              setSearchType('new');\n              setSearchQuery('');\n              setMessageStatus({\n                show: false\n              });\n            },\n            children: \"Find New Friends\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-instructions\",\n          children: searchType === 'friends' ? /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Search through your existing friends list\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Search for new users to add as friends\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: searchType === 'friends' ? \"Enter friend's username...\" : \"Search for new friends by username...\",\n            value: searchQuery,\n            onChange: e => setSearchQuery(e.target.value),\n            onKeyPress: e => {\n              if (e.key === 'Enter') {\n                searchType === 'friends' ? handleFriendSearch() : handleNewFriendSearch();\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: searchType === 'friends' ? handleFriendSearch : handleNewFriendSearch,\n            children: [/*#__PURE__*/_jsxDEV(FaSearch, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 29\n            }, this), \" Search\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 13\n    }, this), messageStatus.show && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `alert alert-${messageStatus.type}`,\n      children: messageStatus.message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 17\n    }, this), renderFriendsList(), showMessageModal && selectedFriend && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-button\",\n          onClick: () => setShowMessageModal(false),\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-user-preview\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-user-avatar\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: getTeamLogo(selectedFriend.favorite_team),\n              alt: selectedFriend.favorite_team\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-user-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: selectedFriend.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"modal-team-name\",\n              children: selectedFriend.favorite_team\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-user-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-stat\",\n              children: [/*#__PURE__*/_jsxDEV(FaTrophy, {\n                className: \"modal-stat-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"modal-stat-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Total Wins\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: selectedFriend.wins || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-stat\",\n              children: [/*#__PURE__*/_jsxDEV(FaStar, {\n                className: \"modal-stat-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"modal-stat-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Points\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: selectedFriend.leaderboard_score || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-user-stats-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-stat-grid-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Wins\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: selectedFriend.wins || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-stat-grid-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Draws\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: selectedFriend.draws || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-stat-grid-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Losses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: selectedFriend.losses || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"message-input-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-input-header\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              children: [\"Send Message to \", selectedFriend.username]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-textarea-container\",\n            children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n              className: \"message-textarea\",\n              value: messageText,\n              onChange: e => setMessageText(e.target.value),\n              placeholder: \"Type your message here...\",\n              rows: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-modal-footer\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"modal-btn cancel-btn\",\n              onClick: () => {\n                setShowMessageModal(false);\n                setMessageText('');\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"modal-btn send-btn\",\n              onClick: handleSendMessage,\n              disabled: !messageText.trim(),\n              children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                className: \"send-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 37\n              }, this), \"Send Message\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 17\n    }, this), searchType === 'new' && newFriendResults.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"new-friends-results\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Search Results\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-results-grid\",\n        children: newFriendResults.map(user => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"friend-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"friend-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"friend-avatar\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: getTeamLogo(user.favorite_team),\n                alt: user.favorite_team\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"friend-basic-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: user.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"favorite-team\",\n                children: user.favorite_team\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"friend-stats-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Total Bets\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: user.total_bets || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Win Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [user.win_rate || 0, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Score\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: user.leaderboard_score || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"add-friend-btn\",\n            onClick: () => sendFriendRequest(user.user_id),\n            children: [/*#__PURE__*/_jsxDEV(FaUserPlus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 37\n            }, this), \" Send Friend Request\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 33\n          }, this)]\n        }, user.user_id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 29\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 17\n    }, this), showConfirmModal && friendToUnfriend && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"confirm-modal\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirm-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"confirm-icon\",\n            children: /*#__PURE__*/_jsxDEV(FaUserMinus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Unfriend Confirmation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Are you sure you want to unfriend \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"highlight\",\n              children: friendToUnfriend.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 66\n            }, this), \"?\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"confirm-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"cancel-btn\",\n              onClick: () => {\n                setShowConfirmModal(false);\n                setFriendToUnfriend(null);\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"confirm-btn\",\n              onClick: confirmUnfriend,\n              children: \"Confirm Unfriend\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 334,\n    columnNumber: 9\n  }, this);\n}\n_s(Friends, \"JuHJD7kL4cO1OU0PNr+WKZ+JXqU=\", false, function () {\n  return [useNavigate];\n});\n_c = Friends;\nexport default Friends;\nvar _c;\n$RefreshReg$(_c, \"Friends\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useNavigate", "FaUserMinus", "FaEnvelope", "FaSearch", "FaExclamationCircle", "FaUserFriends", "FaTrophy", "FaStar", "FaUserPlus", "FaUserCircle", "jsxDEV", "_jsxDEV", "API_BASE_URL", "Friends", "_s", "friends", "setFriends", "loading", "setLoading", "error", "setError", "showMessageModal", "setShowMessageModal", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedFriend", "messageText", "setMessageText", "messageStatus", "setMessageStatus", "show", "type", "message", "userId", "localStorage", "getItem", "searchQuery", "setSearch<PERSON>uery", "searchType", "setSearchType", "newFriendResults", "setNewFriendResults", "teams", "setTeams", "showConfirmModal", "setShowConfirmModal", "friendToUnfriend", "setFriendToUnfriend", "navigate", "fetchFriends", "fetchTeams", "console", "log", "response", "get", "data", "success", "_error$response", "_error$response$data", "status", "getTeamLogo", "teamName", "team", "find", "name", "logo", "handleUnfriend", "friend", "confirmUnfriend", "post", "action", "user_id", "friend_id", "id", "filter", "handleSendMessage", "trim", "sender_id", "receiver_id", "_error$response2", "_error$response2$data", "handleFriendSearch", "filteredFriends", "username", "toLowerCase", "includes", "length", "handleNewFriendSearch", "currentUserId", "filteredUsers", "users", "user", "some", "sendFriendRequest", "prev", "handleViewProfile", "handleChallenge", "renderFriendsList", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "map", "favorite_team", "src", "alt", "char<PERSON>t", "toUpperCase", "onClick", "style", "cursor", "total_bets", "win_rate", "leaderboard_score", "placeholder", "value", "onChange", "e", "target", "onKeyPress", "key", "wins", "draws", "losses", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/Friends.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { FaUserMinus, FaEnvelope, FaSearch, FaExclamationCircle, FaUserFriends, FaTrophy, FaStar, FaUserPlus, FaUserCircle } from 'react-icons/fa';\nimport './Friends.css';\n\nconst API_BASE_URL = '/backend';\n\nfunction Friends() {\n    const [friends, setFriends] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [showMessageModal, setShowMessageModal] = useState(false);\n    const [selectedFriend, setSelectedFriend] = useState(null);\n    const [messageText, setMessageText] = useState('');\n    const [messageStatus, setMessageStatus] = useState({ show: false, type: '', message: '' });\n    const userId = localStorage.getItem('userId');\n    const [searchQuery, setSearchQuery] = useState('');\n    const [searchType, setSearchType] = useState('friends'); // 'friends' or 'new'\n    const [newFriendResults, setNewFriendResults] = useState([]);\n    const [teams, setTeams] = useState([]);\n    const [showConfirmModal, setShowConfirmModal] = useState(false);\n    const [friendToUnfriend, setFriendToUnfriend] = useState(null);\n    const navigate = useNavigate();\n\n    useEffect(() => {\n        fetchFriends();\n        fetchTeams();\n    }, []);\n\n    const fetchFriends = async () => {\n        try {\n            setLoading(true);\n            console.log('Fetching friends for user:', userId);\n            const response = await axios.get(`${API_BASE_URL}/handlers/friends.php?user_id=${userId}`);\n            console.log('Friends response:', response.data);\n            if (response.data.success) {\n                setFriends(response.data.friends || []);\n            } else {\n                setError(response.data.message || 'Failed to fetch friends');\n            }\n        } catch (error) {\n            console.error('Error fetching friends:', error);\n            setError(error.response?.data?.message || 'An error occurred while fetching friends');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchTeams = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n            if (response.data.status === 200) {\n                setTeams(response.data.data);\n            }\n        } catch (error) {\n            console.error('Error fetching teams:', error);\n        }\n    };\n\n    const getTeamLogo = (teamName) => {\n        const team = teams.find(team => team.name === teamName);\n        return team ? `${API_BASE_URL}/${team.logo}` : '';\n    };\n\n    const handleUnfriend = async (friend) => {\n        setFriendToUnfriend(friend);\n        setShowConfirmModal(true);\n    };\n\n    const confirmUnfriend = async () => {\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/friends.php`, {\n                action: 'unfriend',\n                user_id: userId,\n                friend_id: friendToUnfriend.id\n            });\n\n            if (response.data.success) {\n                setFriends(friends.filter(friend => friend.id !== friendToUnfriend.id));\n                setMessageStatus({\n                    show: true,\n                    type: 'success',\n                    message: 'Friend removed successfully'\n                });\n            } else {\n                setMessageStatus({\n                    show: true,\n                    type: 'error',\n                    message: 'Failed to remove friend'\n                });\n            }\n        } catch (error) {\n            setMessageStatus({\n                show: true,\n                type: 'error',\n                message: 'An error occurred'\n            });\n            console.error('Error:', error);\n        } finally {\n            setShowConfirmModal(false);\n            setFriendToUnfriend(null);\n        }\n    };\n\n    const handleSendMessage = async () => {\n        if (!messageText.trim()) {\n            setMessageStatus({\n                show: true,\n                type: 'error',\n                message: 'Please enter a message'\n            });\n            return;\n        }\n\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/send_message.php`, {\n                sender_id: userId,\n                receiver_id: selectedFriend.id,\n                message: messageText\n            });\n\n            if (response.data.success) {\n                setMessageStatus({\n                    show: true,\n                    type: 'success',\n                    message: 'Message sent successfully'\n                });\n                setMessageText('');\n                setShowMessageModal(false);\n            } else {\n                setMessageStatus({\n                    show: true,\n                    type: 'error',\n                    message: response.data.message || 'Failed to send message'\n                });\n            }\n        } catch (error) {\n            console.error('Error sending message:', error);\n            setMessageStatus({\n                show: true,\n                type: 'error',\n                message: error.response?.data?.message || 'An error occurred while sending the message'\n            });\n        }\n    };\n\n    const handleFriendSearch = () => {\n        if (!searchQuery.trim()) {\n            setMessageStatus({\n                show: true,\n                type: 'error',\n                message: 'Please enter a username to search your friends list'\n            });\n            return;\n        }\n        \n        const filteredFriends = friends.filter(friend => \n            friend.username.toLowerCase().includes(searchQuery.toLowerCase())\n        );\n\n        if (filteredFriends.length === 0) {\n            setMessageStatus({\n                show: true,\n                type: 'info',\n                message: `No friends found with username \"${searchQuery}\". Try using \"Find New Friends\" to search for new users.`\n            });\n            // Keep showing existing friends\n            setFriends(friends);\n        } else {\n            setFriends(filteredFriends);\n            setMessageStatus({\n                show: true,\n                type: 'success',\n                message: `Found ${filteredFriends.length} friend${filteredFriends.length > 1 ? 's' : ''}`\n            });\n        }\n    };\n\n    const handleNewFriendSearch = async () => {\n        if (!searchQuery.trim()) {\n            setMessageStatus({\n                show: true,\n                type: 'error',\n                message: 'Please enter a username to search for new friends'\n            });\n            return;\n        }\n\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/search_users.php?query=${searchQuery}`);\n            if (response.data.success) {\n                const currentUserId = localStorage.getItem('userId');\n                // Filter out current user and existing friends\n                const filteredUsers = response.data.users.filter(user => \n                    user.user_id !== currentUserId && \n                    !friends.some(friend => friend.id === user.user_id)\n                );\n\n                if (filteredUsers.length === 0) {\n                    setMessageStatus({\n                        show: true,\n                        type: 'info',\n                        message: `No new users found with username \"${searchQuery}\". Try a different username or check your spelling.`\n                    });\n                    setNewFriendResults([]);\n                } else {\n                    setNewFriendResults(filteredUsers);\n                    setMessageStatus({\n                        show: true,\n                        type: 'success',\n                        message: `Found ${filteredUsers.length} potential new friend${filteredUsers.length > 1 ? 's' : ''}`\n                    });\n                }\n            }\n        } catch (error) {\n            setMessageStatus({\n                show: true,\n                type: 'error',\n                message: 'Error searching for users. Please try again.'\n            });\n            console.error('Error searching users:', error);\n        }\n    };\n\n    const sendFriendRequest = async (userId) => {\n        try {\n            const response = await axios.post('add_friend.php', {\n                user_id: localStorage.getItem('userId'),\n                friend_id: userId,\n                action: 'request'\n            });\n\n            if (response.data.success) {\n                setMessageStatus({\n                    show: true,\n                    type: 'success',\n                    message: 'Friend request sent successfully!'\n                });\n                // Remove user from search results\n                setNewFriendResults(prev => prev.filter(user => user.user_id !== userId));\n            }\n        } catch (error) {\n            setMessageStatus({\n                show: true,\n                type: 'error',\n                message: 'Failed to send friend request'\n            });\n        }\n    };\n\n    const handleViewProfile = (username) => {\n        navigate(`/user/profile/${username}`);\n    };\n\n    const handleChallenge = (friend) => {\n        // Handle challenge logic\n        console.log('Challenge friend:', friend);\n        // You can implement the challenge functionality here\n    };\n\n    const renderFriendsList = () => {\n        if (loading) {\n            return <div>Loading...</div>;\n        }\n\n        if (error) {\n            return <div className=\"message-status error\">{error}</div>;\n        }\n\n        if (friends.length === 0) {\n            return <div className=\"message-status info\">No friends found.</div>;\n        }\n\n        return (\n            <div className=\"friends-grid\">\n                {friends.map((friend) => (\n                    <div key={friend.id} className=\"friend-card\">\n                        <div className=\"friend-banner\">\n                            <div className=\"friend-avatar\">\n                                {friend.favorite_team ? (\n                                    <img src={getTeamLogo(friend.favorite_team)} alt={friend.favorite_team} className=\"team-avatar\" />\n                                ) : (\n                                    <div className=\"avatar-placeholder\">{friend.username.charAt(0).toUpperCase()}</div>\n                                )}\n                            </div>\n                            <div className=\"friend-info\">\n                                <h3 onClick={() => handleViewProfile(friend.username)} style={{ cursor: 'pointer' }}>\n                                    {friend.username}\n                                </h3>\n                                {friend.favorite_team && <span>{friend.favorite_team}</span>}\n                            </div>\n                        </div>\n                        \n                        <div className=\"friend-stats\">\n                            <div className=\"stat-item\">\n                                <span className=\"stat-label\">Total Bets</span>\n                                <span className=\"stat-value\">{friend.total_bets}</span>\n                            </div>\n                            <div className=\"stat-item\">\n                                <span className=\"stat-label\">Win Rate</span>\n                                <span className=\"stat-value\">{friend.win_rate}%</span>\n                            </div>\n                            <div className=\"stat-item\">\n                                <span className=\"stat-label\">Score</span>\n                                <span className=\"stat-value\">{friend.leaderboard_score || 0}</span>\n                            </div>\n                        </div>\n\n                        <div className=\"friend-actions\">\n                            <button \n                                className=\"message-btn\"\n                                onClick={() => {\n                                    setSelectedFriend(friend);\n                                    setShowMessageModal(true);\n                                }}\n                            >\n                                Message\n                            </button>\n                            <button \n                                className=\"unfriend-btn\"\n                                onClick={() => handleUnfriend(friend)}\n                            >\n                                <FaUserMinus /> Unfriend\n                            </button>\n                        </div>\n                    </div>\n                ))}\n            </div>\n        );\n    };\n\n    return (\n        <div className=\"friends-container\">\n            <div className=\"friends-header\">\n                <div className=\"friends-title-section\">\n                    <h1>My Friends</h1>\n                    <span className=\"friends-count\">{friends.length} Friends</span>\n                </div>\n                \n                <div className=\"friends-search-section\">\n                    <div className=\"search-type-toggle\">\n                        <button \n                            className={`toggle-btn ${searchType === 'friends' ? 'active' : ''}`}\n                            onClick={() => {\n                                setSearchType('friends');\n                                setSearchQuery('');\n                                setNewFriendResults([]);\n                                setMessageStatus({ show: false });\n                            }}\n                        >\n                            Search My Friends\n                        </button>\n                        <button \n                            className={`toggle-btn ${searchType === 'new' ? 'active' : ''}`}\n                            onClick={() => {\n                                setSearchType('new');\n                                setSearchQuery('');\n                                setMessageStatus({ show: false });\n                            }}\n                        >\n                            Find New Friends\n                        </button>\n                    </div>\n                    \n                    <div className=\"search-instructions\">\n                        {searchType === 'friends' ? (\n                            <p>Search through your existing friends list</p>\n                        ) : (\n                            <p>Search for new users to add as friends</p>\n                        )}\n                    </div>\n                    \n                    <div className=\"search-wrapper\">\n                        <input\n                            type=\"text\"\n                            placeholder={searchType === 'friends' ? \n                                \"Enter friend's username...\" : \n                                \"Search for new friends by username...\"\n                            }\n                            value={searchQuery}\n                            onChange={(e) => setSearchQuery(e.target.value)}\n                            onKeyPress={(e) => {\n                                if (e.key === 'Enter') {\n                                    searchType === 'friends' ? handleFriendSearch() : handleNewFriendSearch();\n                                }\n                            }}\n                        />\n                        <button onClick={searchType === 'friends' ? handleFriendSearch : handleNewFriendSearch}>\n                            <FaSearch /> Search\n                        </button>\n                    </div>\n                </div>\n            </div>\n\n            {messageStatus.show && (\n                <div className={`alert alert-${messageStatus.type}`}>\n                    {messageStatus.message}\n                </div>\n            )}\n            \n            {renderFriendsList()}\n\n            {showMessageModal && selectedFriend && (\n                <div className=\"modal-overlay\">\n                    <div className=\"message-modal\">\n                        <button className=\"close-button\" onClick={() => setShowMessageModal(false)}>×</button>\n                        \n                        <div className=\"modal-user-preview\">\n                            <div className=\"modal-user-avatar\">\n                                <img \n                                    src={getTeamLogo(selectedFriend.favorite_team)} \n                                    alt={selectedFriend.favorite_team}\n                                />\n                            </div>\n                            <div className=\"modal-user-info\">\n                                <h3>{selectedFriend.username}</h3>\n                                <span className=\"modal-team-name\">{selectedFriend.favorite_team}</span>\n                            </div>\n                            <div className=\"modal-user-stats\">\n                                <div className=\"modal-stat\">\n                                    <FaTrophy className=\"modal-stat-icon\" />\n                                    <div className=\"modal-stat-details\">\n                                        <label>Total Wins</label>\n                                        <span>{selectedFriend.wins || 0}</span>\n                                    </div>\n                                </div>\n                                <div className=\"modal-stat\">\n                                    <FaStar className=\"modal-stat-icon\" />\n                                    <div className=\"modal-stat-details\">\n                                        <label>Points</label>\n                                        <span>{selectedFriend.leaderboard_score || 0}</span>\n                                    </div>\n                                </div>\n                            </div>\n                            <div className=\"modal-user-stats-grid\">\n                                <div className=\"modal-stat-grid-item\">\n                                    <label>Wins</label>\n                                    <span>{selectedFriend.wins || 0}</span>\n                                </div>\n                                <div className=\"modal-stat-grid-item\">\n                                    <label>Draws</label>\n                                    <span>{selectedFriend.draws || 0}</span>\n                                </div>\n                                <div className=\"modal-stat-grid-item\">\n                                    <label>Losses</label>\n                                    <span>{selectedFriend.losses || 0}</span>\n                                </div>\n                            </div>\n                        </div>\n\n                        <div className=\"message-input-section\">\n                            <div className=\"message-input-header\">\n                                <label>Send Message to {selectedFriend.username}</label>\n                            </div>\n                            <div className=\"message-textarea-container\">\n                                <textarea\n                                    className=\"message-textarea\"\n                                    value={messageText}\n                                    onChange={(e) => setMessageText(e.target.value)}\n                                    placeholder=\"Type your message here...\"\n                                    rows=\"6\"\n                                />\n                            </div>\n                            <div className=\"message-modal-footer\">\n                                <button \n                                    className=\"modal-btn cancel-btn\"\n                                    onClick={() => {\n                                        setShowMessageModal(false);\n                                        setMessageText('');\n                                    }}\n                                >\n                                    Cancel\n                                </button>\n                                <button \n                                    className=\"modal-btn send-btn\"\n                                    onClick={handleSendMessage}\n                                    disabled={!messageText.trim()}\n                                >\n                                    <FaEnvelope className=\"send-icon\" />\n                                    Send Message\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {searchType === 'new' && newFriendResults.length > 0 && (\n                <div className=\"new-friends-results\">\n                    <h3>Search Results</h3>\n                    <div className=\"search-results-grid\">\n                        {newFriendResults.map(user => (\n                            <div key={user.user_id} className=\"friend-card\">\n                                <div className=\"friend-header\">\n                                    <div className=\"friend-avatar\">\n                                        <img \n                                            src={getTeamLogo(user.favorite_team)} \n                                            alt={user.favorite_team}\n                                        />\n                                    </div>\n                                    <div className=\"friend-basic-info\">\n                                        <h3>{user.username}</h3>\n                                        <span className=\"favorite-team\">{user.favorite_team}</span>\n                                    </div>\n                                </div>\n                                \n                                <div className=\"friend-stats-grid\">\n                                    <div className=\"stat-item\">\n                                        <label>Total Bets</label>\n                                        <span>{user.total_bets || 0}</span>\n                                    </div>\n                                    <div className=\"stat-item\">\n                                        <label>Win Rate</label>\n                                        <span>{user.win_rate || 0}%</span>\n                                    </div>\n                                    <div className=\"stat-item\">\n                                        <label>Score</label>\n                                        <span>{user.leaderboard_score || 0}</span>\n                                    </div>\n                                </div>\n                                \n                                <button \n                                    className=\"add-friend-btn\"\n                                    onClick={() => sendFriendRequest(user.user_id)}\n                                >\n                                    <FaUserPlus /> Send Friend Request\n                                </button>\n                            </div>\n                        ))}\n                    </div>\n                </div>\n            )}\n\n            {showConfirmModal && friendToUnfriend && (\n                <div className=\"modal-overlay\">\n                    <div className=\"confirm-modal\">\n                        <div className=\"confirm-content\">\n                            <div className=\"confirm-icon\">\n                                <FaUserMinus />\n                            </div>\n                            <h3>Unfriend Confirmation</h3>\n                            <p>Are you sure you want to unfriend <span className=\"highlight\">{friendToUnfriend.username}</span>?</p>\n                            <div className=\"confirm-actions\">\n                                <button \n                                    className=\"cancel-btn\"\n                                    onClick={() => {\n                                        setShowConfirmModal(false);\n                                        setFriendToUnfriend(null);\n                                    }}\n                                >\n                                    Cancel\n                                </button>\n                                <button \n                                    className=\"confirm-btn\"\n                                    onClick={confirmUnfriend}\n                                >\n                                    Confirm Unfriend\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n}\n\nexport default Friends;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAAU,EAAEC,YAAY,QAAQ,gBAAgB;AAClJ,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,OAAOA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC;IAAEgC,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG,CAAC,CAAC;EAC1F,MAAMC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAC7C,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC0C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAMkD,QAAQ,GAAG/C,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACZkD,YAAY,CAAC,CAAC;IACdC,UAAU,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA9B,UAAU,CAAC,IAAI,CAAC;MAChBgC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEnB,MAAM,CAAC;MACjD,MAAMoB,QAAQ,GAAG,MAAMrD,KAAK,CAACsD,GAAG,CAAC,GAAGzC,YAAY,iCAAiCoB,MAAM,EAAE,CAAC;MAC1FkB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAC/C,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvBvC,UAAU,CAACoC,QAAQ,CAACE,IAAI,CAACvC,OAAO,IAAI,EAAE,CAAC;MAC3C,CAAC,MAAM;QACHK,QAAQ,CAACgC,QAAQ,CAACE,IAAI,CAACvB,OAAO,IAAI,yBAAyB,CAAC;MAChE;IACJ,CAAC,CAAC,OAAOZ,KAAK,EAAE;MAAA,IAAAqC,eAAA,EAAAC,oBAAA;MACZP,OAAO,CAAC/B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,EAAAoC,eAAA,GAAArC,KAAK,CAACiC,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsB1B,OAAO,KAAI,0CAA0C,CAAC;IACzF,CAAC,SAAS;MACNb,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM+B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAMG,QAAQ,GAAG,MAAMrD,KAAK,CAACsD,GAAG,CAAC,GAAGzC,YAAY,+BAA+B,CAAC;MAChF,IAAIwC,QAAQ,CAACE,IAAI,CAACI,MAAM,KAAK,GAAG,EAAE;QAC9BhB,QAAQ,CAACU,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MAChC;IACJ,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACZ+B,OAAO,CAAC/B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IACjD;EACJ,CAAC;EAED,MAAMwC,WAAW,GAAIC,QAAQ,IAAK;IAC9B,MAAMC,IAAI,GAAGpB,KAAK,CAACqB,IAAI,CAACD,IAAI,IAAIA,IAAI,CAACE,IAAI,KAAKH,QAAQ,CAAC;IACvD,OAAOC,IAAI,GAAG,GAAGjD,YAAY,IAAIiD,IAAI,CAACG,IAAI,EAAE,GAAG,EAAE;EACrD,CAAC;EAED,MAAMC,cAAc,GAAG,MAAOC,MAAM,IAAK;IACrCpB,mBAAmB,CAACoB,MAAM,CAAC;IAC3BtB,mBAAmB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMuB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACA,MAAMf,QAAQ,GAAG,MAAMrD,KAAK,CAACqE,IAAI,CAAC,GAAGxD,YAAY,uBAAuB,EAAE;QACtEyD,MAAM,EAAE,UAAU;QAClBC,OAAO,EAAEtC,MAAM;QACfuC,SAAS,EAAE1B,gBAAgB,CAAC2B;MAChC,CAAC,CAAC;MAEF,IAAIpB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvBvC,UAAU,CAACD,OAAO,CAAC0D,MAAM,CAACP,MAAM,IAAIA,MAAM,CAACM,EAAE,KAAK3B,gBAAgB,CAAC2B,EAAE,CAAC,CAAC;QACvE5C,gBAAgB,CAAC;UACbC,IAAI,EAAE,IAAI;UACVC,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,MAAM;QACHH,gBAAgB,CAAC;UACbC,IAAI,EAAE,IAAI;UACVC,IAAI,EAAE,OAAO;UACbC,OAAO,EAAE;QACb,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACZS,gBAAgB,CAAC;QACbC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE,OAAO;QACbC,OAAO,EAAE;MACb,CAAC,CAAC;MACFmB,OAAO,CAAC/B,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAClC,CAAC,SAAS;MACNyB,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,mBAAmB,CAAC,IAAI,CAAC;IAC7B;EACJ,CAAC;EAED,MAAM4B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACjD,WAAW,CAACkD,IAAI,CAAC,CAAC,EAAE;MACrB/C,gBAAgB,CAAC;QACbC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE,OAAO;QACbC,OAAO,EAAE;MACb,CAAC,CAAC;MACF;IACJ;IAEA,IAAI;MACA,MAAMqB,QAAQ,GAAG,MAAMrD,KAAK,CAACqE,IAAI,CAAC,GAAGxD,YAAY,4BAA4B,EAAE;QAC3EgE,SAAS,EAAE5C,MAAM;QACjB6C,WAAW,EAAEtD,cAAc,CAACiD,EAAE;QAC9BzC,OAAO,EAAEN;MACb,CAAC,CAAC;MAEF,IAAI2B,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvB3B,gBAAgB,CAAC;UACbC,IAAI,EAAE,IAAI;UACVC,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE;QACb,CAAC,CAAC;QACFL,cAAc,CAAC,EAAE,CAAC;QAClBJ,mBAAmB,CAAC,KAAK,CAAC;MAC9B,CAAC,MAAM;QACHM,gBAAgB,CAAC;UACbC,IAAI,EAAE,IAAI;UACVC,IAAI,EAAE,OAAO;UACbC,OAAO,EAAEqB,QAAQ,CAACE,IAAI,CAACvB,OAAO,IAAI;QACtC,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,OAAOZ,KAAK,EAAE;MAAA,IAAA2D,gBAAA,EAAAC,qBAAA;MACZ7B,OAAO,CAAC/B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CS,gBAAgB,CAAC;QACbC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE,OAAO;QACbC,OAAO,EAAE,EAAA+C,gBAAA,GAAA3D,KAAK,CAACiC,QAAQ,cAAA0B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxB,IAAI,cAAAyB,qBAAA,uBAApBA,qBAAA,CAAsBhD,OAAO,KAAI;MAC9C,CAAC,CAAC;IACN;EACJ,CAAC;EAED,MAAMiD,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAC7C,WAAW,CAACwC,IAAI,CAAC,CAAC,EAAE;MACrB/C,gBAAgB,CAAC;QACbC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE,OAAO;QACbC,OAAO,EAAE;MACb,CAAC,CAAC;MACF;IACJ;IAEA,MAAMkD,eAAe,GAAGlE,OAAO,CAAC0D,MAAM,CAACP,MAAM,IACzCA,MAAM,CAACgB,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjD,WAAW,CAACgD,WAAW,CAAC,CAAC,CACpE,CAAC;IAED,IAAIF,eAAe,CAACI,MAAM,KAAK,CAAC,EAAE;MAC9BzD,gBAAgB,CAAC;QACbC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,mCAAmCI,WAAW;MAC3D,CAAC,CAAC;MACF;MACAnB,UAAU,CAACD,OAAO,CAAC;IACvB,CAAC,MAAM;MACHC,UAAU,CAACiE,eAAe,CAAC;MAC3BrD,gBAAgB,CAAC;QACbC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE,SAAS;QACfC,OAAO,EAAE,SAASkD,eAAe,CAACI,MAAM,UAAUJ,eAAe,CAACI,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;MAC3F,CAAC,CAAC;IACN;EACJ,CAAC;EAED,MAAMC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACnD,WAAW,CAACwC,IAAI,CAAC,CAAC,EAAE;MACrB/C,gBAAgB,CAAC;QACbC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE,OAAO;QACbC,OAAO,EAAE;MACb,CAAC,CAAC;MACF;IACJ;IAEA,IAAI;MACA,MAAMqB,QAAQ,GAAG,MAAMrD,KAAK,CAACsD,GAAG,CAAC,GAAGzC,YAAY,oCAAoCuB,WAAW,EAAE,CAAC;MAClG,IAAIiB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvB,MAAMgC,aAAa,GAAGtD,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;QACpD;QACA,MAAMsD,aAAa,GAAGpC,QAAQ,CAACE,IAAI,CAACmC,KAAK,CAAChB,MAAM,CAACiB,IAAI,IACjDA,IAAI,CAACpB,OAAO,KAAKiB,aAAa,IAC9B,CAACxE,OAAO,CAAC4E,IAAI,CAACzB,MAAM,IAAIA,MAAM,CAACM,EAAE,KAAKkB,IAAI,CAACpB,OAAO,CACtD,CAAC;QAED,IAAIkB,aAAa,CAACH,MAAM,KAAK,CAAC,EAAE;UAC5BzD,gBAAgB,CAAC;YACbC,IAAI,EAAE,IAAI;YACVC,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,qCAAqCI,WAAW;UAC7D,CAAC,CAAC;UACFK,mBAAmB,CAAC,EAAE,CAAC;QAC3B,CAAC,MAAM;UACHA,mBAAmB,CAACgD,aAAa,CAAC;UAClC5D,gBAAgB,CAAC;YACbC,IAAI,EAAE,IAAI;YACVC,IAAI,EAAE,SAAS;YACfC,OAAO,EAAE,SAASyD,aAAa,CAACH,MAAM,wBAAwBG,aAAa,CAACH,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;UACrG,CAAC,CAAC;QACN;MACJ;IACJ,CAAC,CAAC,OAAOlE,KAAK,EAAE;MACZS,gBAAgB,CAAC;QACbC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE,OAAO;QACbC,OAAO,EAAE;MACb,CAAC,CAAC;MACFmB,OAAO,CAAC/B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAClD;EACJ,CAAC;EAED,MAAMyE,iBAAiB,GAAG,MAAO5D,MAAM,IAAK;IACxC,IAAI;MACA,MAAMoB,QAAQ,GAAG,MAAMrD,KAAK,CAACqE,IAAI,CAAC,gBAAgB,EAAE;QAChDE,OAAO,EAAErC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;QACvCqC,SAAS,EAAEvC,MAAM;QACjBqC,MAAM,EAAE;MACZ,CAAC,CAAC;MAEF,IAAIjB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvB3B,gBAAgB,CAAC;UACbC,IAAI,EAAE,IAAI;UACVC,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE;QACb,CAAC,CAAC;QACF;QACAS,mBAAmB,CAACqD,IAAI,IAAIA,IAAI,CAACpB,MAAM,CAACiB,IAAI,IAAIA,IAAI,CAACpB,OAAO,KAAKtC,MAAM,CAAC,CAAC;MAC7E;IACJ,CAAC,CAAC,OAAOb,KAAK,EAAE;MACZS,gBAAgB,CAAC;QACbC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE,OAAO;QACbC,OAAO,EAAE;MACb,CAAC,CAAC;IACN;EACJ,CAAC;EAED,MAAM+D,iBAAiB,GAAIZ,QAAQ,IAAK;IACpCnC,QAAQ,CAAC,iBAAiBmC,QAAQ,EAAE,CAAC;EACzC,CAAC;EAED,MAAMa,eAAe,GAAI7B,MAAM,IAAK;IAChC;IACAhB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEe,MAAM,CAAC;IACxC;EACJ,CAAC;EAED,MAAM8B,iBAAiB,GAAGA,CAAA,KAAM;IAC5B,IAAI/E,OAAO,EAAE;MACT,oBAAON,OAAA;QAAAsF,QAAA,EAAK;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAChC;IAEA,IAAIlF,KAAK,EAAE;MACP,oBAAOR,OAAA;QAAK2F,SAAS,EAAC,sBAAsB;QAAAL,QAAA,EAAE9E;MAAK;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAC9D;IAEA,IAAItF,OAAO,CAACsE,MAAM,KAAK,CAAC,EAAE;MACtB,oBAAO1E,OAAA;QAAK2F,SAAS,EAAC,qBAAqB;QAAAL,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IACvE;IAEA,oBACI1F,OAAA;MAAK2F,SAAS,EAAC,cAAc;MAAAL,QAAA,EACxBlF,OAAO,CAACwF,GAAG,CAAErC,MAAM,iBAChBvD,OAAA;QAAqB2F,SAAS,EAAC,aAAa;QAAAL,QAAA,gBACxCtF,OAAA;UAAK2F,SAAS,EAAC,eAAe;UAAAL,QAAA,gBAC1BtF,OAAA;YAAK2F,SAAS,EAAC,eAAe;YAAAL,QAAA,EACzB/B,MAAM,CAACsC,aAAa,gBACjB7F,OAAA;cAAK8F,GAAG,EAAE9C,WAAW,CAACO,MAAM,CAACsC,aAAa,CAAE;cAACE,GAAG,EAAExC,MAAM,CAACsC,aAAc;cAACF,SAAS,EAAC;YAAa;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAElG1F,OAAA;cAAK2F,SAAS,EAAC,oBAAoB;cAAAL,QAAA,EAAE/B,MAAM,CAACgB,QAAQ,CAACyB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;YAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UACrF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACN1F,OAAA;YAAK2F,SAAS,EAAC,aAAa;YAAAL,QAAA,gBACxBtF,OAAA;cAAIkG,OAAO,EAAEA,CAAA,KAAMf,iBAAiB,CAAC5B,MAAM,CAACgB,QAAQ,CAAE;cAAC4B,KAAK,EAAE;gBAAEC,MAAM,EAAE;cAAU,CAAE;cAAAd,QAAA,EAC/E/B,MAAM,CAACgB;YAAQ;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,EACJnC,MAAM,CAACsC,aAAa,iBAAI7F,OAAA;cAAAsF,QAAA,EAAO/B,MAAM,CAACsC;YAAa;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN1F,OAAA;UAAK2F,SAAS,EAAC,cAAc;UAAAL,QAAA,gBACzBtF,OAAA;YAAK2F,SAAS,EAAC,WAAW;YAAAL,QAAA,gBACtBtF,OAAA;cAAM2F,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9C1F,OAAA;cAAM2F,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAE/B,MAAM,CAAC8C;YAAU;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACN1F,OAAA;YAAK2F,SAAS,EAAC,WAAW;YAAAL,QAAA,gBACtBtF,OAAA;cAAM2F,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5C1F,OAAA;cAAM2F,SAAS,EAAC,YAAY;cAAAL,QAAA,GAAE/B,MAAM,CAAC+C,QAAQ,EAAC,GAAC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACN1F,OAAA;YAAK2F,SAAS,EAAC,WAAW;YAAAL,QAAA,gBACtBtF,OAAA;cAAM2F,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzC1F,OAAA;cAAM2F,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAE/B,MAAM,CAACgD,iBAAiB,IAAI;YAAC;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN1F,OAAA;UAAK2F,SAAS,EAAC,gBAAgB;UAAAL,QAAA,gBAC3BtF,OAAA;YACI2F,SAAS,EAAC,aAAa;YACvBO,OAAO,EAAEA,CAAA,KAAM;cACXrF,iBAAiB,CAAC0C,MAAM,CAAC;cACzB5C,mBAAmB,CAAC,IAAI,CAAC;YAC7B,CAAE;YAAA2E,QAAA,EACL;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1F,OAAA;YACI2F,SAAS,EAAC,cAAc;YACxBO,OAAO,EAAEA,CAAA,KAAM5C,cAAc,CAACC,MAAM,CAAE;YAAA+B,QAAA,gBAEtCtF,OAAA,CAACV,WAAW;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aACnB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA,GAhDAnC,MAAM,CAACM,EAAE;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiDd,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEd,CAAC;EAED,oBACI1F,OAAA;IAAK2F,SAAS,EAAC,mBAAmB;IAAAL,QAAA,gBAC9BtF,OAAA;MAAK2F,SAAS,EAAC,gBAAgB;MAAAL,QAAA,gBAC3BtF,OAAA;QAAK2F,SAAS,EAAC,uBAAuB;QAAAL,QAAA,gBAClCtF,OAAA;UAAAsF,QAAA,EAAI;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnB1F,OAAA;UAAM2F,SAAS,EAAC,eAAe;UAAAL,QAAA,GAAElF,OAAO,CAACsE,MAAM,EAAC,UAAQ;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAEN1F,OAAA;QAAK2F,SAAS,EAAC,wBAAwB;QAAAL,QAAA,gBACnCtF,OAAA;UAAK2F,SAAS,EAAC,oBAAoB;UAAAL,QAAA,gBAC/BtF,OAAA;YACI2F,SAAS,EAAE,cAAcjE,UAAU,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;YACpEwE,OAAO,EAAEA,CAAA,KAAM;cACXvE,aAAa,CAAC,SAAS,CAAC;cACxBF,cAAc,CAAC,EAAE,CAAC;cAClBI,mBAAmB,CAAC,EAAE,CAAC;cACvBZ,gBAAgB,CAAC;gBAAEC,IAAI,EAAE;cAAM,CAAC,CAAC;YACrC,CAAE;YAAAoE,QAAA,EACL;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1F,OAAA;YACI2F,SAAS,EAAE,cAAcjE,UAAU,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;YAChEwE,OAAO,EAAEA,CAAA,KAAM;cACXvE,aAAa,CAAC,KAAK,CAAC;cACpBF,cAAc,CAAC,EAAE,CAAC;cAClBR,gBAAgB,CAAC;gBAAEC,IAAI,EAAE;cAAM,CAAC,CAAC;YACrC,CAAE;YAAAoE,QAAA,EACL;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEN1F,OAAA;UAAK2F,SAAS,EAAC,qBAAqB;UAAAL,QAAA,EAC/B5D,UAAU,KAAK,SAAS,gBACrB1B,OAAA;YAAAsF,QAAA,EAAG;UAAyC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,gBAEhD1F,OAAA;YAAAsF,QAAA,EAAG;UAAsC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC/C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEN1F,OAAA;UAAK2F,SAAS,EAAC,gBAAgB;UAAAL,QAAA,gBAC3BtF,OAAA;YACImB,IAAI,EAAC,MAAM;YACXqF,WAAW,EAAE9E,UAAU,KAAK,SAAS,GACjC,4BAA4B,GAC5B,uCACH;YACD+E,KAAK,EAAEjF,WAAY;YACnBkF,QAAQ,EAAGC,CAAC,IAAKlF,cAAc,CAACkF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAChDI,UAAU,EAAGF,CAAC,IAAK;cACf,IAAIA,CAAC,CAACG,GAAG,KAAK,OAAO,EAAE;gBACnBpF,UAAU,KAAK,SAAS,GAAG2C,kBAAkB,CAAC,CAAC,GAAGM,qBAAqB,CAAC,CAAC;cAC7E;YACJ;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACF1F,OAAA;YAAQkG,OAAO,EAAExE,UAAU,KAAK,SAAS,GAAG2C,kBAAkB,GAAGM,qBAAsB;YAAAW,QAAA,gBACnFtF,OAAA,CAACR,QAAQ;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAChB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEL1E,aAAa,CAACE,IAAI,iBACflB,OAAA;MAAK2F,SAAS,EAAE,eAAe3E,aAAa,CAACG,IAAI,EAAG;MAAAmE,QAAA,EAC/CtE,aAAa,CAACI;IAAO;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACR,EAEAL,iBAAiB,CAAC,CAAC,EAEnB3E,gBAAgB,IAAIE,cAAc,iBAC/BZ,OAAA;MAAK2F,SAAS,EAAC,eAAe;MAAAL,QAAA,eAC1BtF,OAAA;QAAK2F,SAAS,EAAC,eAAe;QAAAL,QAAA,gBAC1BtF,OAAA;UAAQ2F,SAAS,EAAC,cAAc;UAACO,OAAO,EAAEA,CAAA,KAAMvF,mBAAmB,CAAC,KAAK,CAAE;UAAA2E,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAEtF1F,OAAA;UAAK2F,SAAS,EAAC,oBAAoB;UAAAL,QAAA,gBAC/BtF,OAAA;YAAK2F,SAAS,EAAC,mBAAmB;YAAAL,QAAA,eAC9BtF,OAAA;cACI8F,GAAG,EAAE9C,WAAW,CAACpC,cAAc,CAACiF,aAAa,CAAE;cAC/CE,GAAG,EAAEnF,cAAc,CAACiF;YAAc;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN1F,OAAA;YAAK2F,SAAS,EAAC,iBAAiB;YAAAL,QAAA,gBAC5BtF,OAAA;cAAAsF,QAAA,EAAK1E,cAAc,CAAC2D;YAAQ;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClC1F,OAAA;cAAM2F,SAAS,EAAC,iBAAiB;cAAAL,QAAA,EAAE1E,cAAc,CAACiF;YAAa;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACN1F,OAAA;YAAK2F,SAAS,EAAC,kBAAkB;YAAAL,QAAA,gBAC7BtF,OAAA;cAAK2F,SAAS,EAAC,YAAY;cAAAL,QAAA,gBACvBtF,OAAA,CAACL,QAAQ;gBAACgG,SAAS,EAAC;cAAiB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxC1F,OAAA;gBAAK2F,SAAS,EAAC,oBAAoB;gBAAAL,QAAA,gBAC/BtF,OAAA;kBAAAsF,QAAA,EAAO;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzB1F,OAAA;kBAAAsF,QAAA,EAAO1E,cAAc,CAACmG,IAAI,IAAI;gBAAC;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACN1F,OAAA;cAAK2F,SAAS,EAAC,YAAY;cAAAL,QAAA,gBACvBtF,OAAA,CAACJ,MAAM;gBAAC+F,SAAS,EAAC;cAAiB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtC1F,OAAA;gBAAK2F,SAAS,EAAC,oBAAoB;gBAAAL,QAAA,gBAC/BtF,OAAA;kBAAAsF,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrB1F,OAAA;kBAAAsF,QAAA,EAAO1E,cAAc,CAAC2F,iBAAiB,IAAI;gBAAC;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACN1F,OAAA;YAAK2F,SAAS,EAAC,uBAAuB;YAAAL,QAAA,gBAClCtF,OAAA;cAAK2F,SAAS,EAAC,sBAAsB;cAAAL,QAAA,gBACjCtF,OAAA;gBAAAsF,QAAA,EAAO;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnB1F,OAAA;gBAAAsF,QAAA,EAAO1E,cAAc,CAACmG,IAAI,IAAI;cAAC;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACN1F,OAAA;cAAK2F,SAAS,EAAC,sBAAsB;cAAAL,QAAA,gBACjCtF,OAAA;gBAAAsF,QAAA,EAAO;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpB1F,OAAA;gBAAAsF,QAAA,EAAO1E,cAAc,CAACoG,KAAK,IAAI;cAAC;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACN1F,OAAA;cAAK2F,SAAS,EAAC,sBAAsB;cAAAL,QAAA,gBACjCtF,OAAA;gBAAAsF,QAAA,EAAO;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrB1F,OAAA;gBAAAsF,QAAA,EAAO1E,cAAc,CAACqG,MAAM,IAAI;cAAC;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN1F,OAAA;UAAK2F,SAAS,EAAC,uBAAuB;UAAAL,QAAA,gBAClCtF,OAAA;YAAK2F,SAAS,EAAC,sBAAsB;YAAAL,QAAA,eACjCtF,OAAA;cAAAsF,QAAA,GAAO,kBAAgB,EAAC1E,cAAc,CAAC2D,QAAQ;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACN1F,OAAA;YAAK2F,SAAS,EAAC,4BAA4B;YAAAL,QAAA,eACvCtF,OAAA;cACI2F,SAAS,EAAC,kBAAkB;cAC5Bc,KAAK,EAAE3F,WAAY;cACnB4F,QAAQ,EAAGC,CAAC,IAAK5F,cAAc,CAAC4F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAChDD,WAAW,EAAC,2BAA2B;cACvCU,IAAI,EAAC;YAAG;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN1F,OAAA;YAAK2F,SAAS,EAAC,sBAAsB;YAAAL,QAAA,gBACjCtF,OAAA;cACI2F,SAAS,EAAC,sBAAsB;cAChCO,OAAO,EAAEA,CAAA,KAAM;gBACXvF,mBAAmB,CAAC,KAAK,CAAC;gBAC1BI,cAAc,CAAC,EAAE,CAAC;cACtB,CAAE;cAAAuE,QAAA,EACL;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1F,OAAA;cACI2F,SAAS,EAAC,oBAAoB;cAC9BO,OAAO,EAAEnC,iBAAkB;cAC3BoD,QAAQ,EAAE,CAACrG,WAAW,CAACkD,IAAI,CAAC,CAAE;cAAAsB,QAAA,gBAE9BtF,OAAA,CAACT,UAAU;gBAACoG,SAAS,EAAC;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAEAhE,UAAU,KAAK,KAAK,IAAIE,gBAAgB,CAAC8C,MAAM,GAAG,CAAC,iBAChD1E,OAAA;MAAK2F,SAAS,EAAC,qBAAqB;MAAAL,QAAA,gBAChCtF,OAAA;QAAAsF,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvB1F,OAAA;QAAK2F,SAAS,EAAC,qBAAqB;QAAAL,QAAA,EAC/B1D,gBAAgB,CAACgE,GAAG,CAACb,IAAI,iBACtB/E,OAAA;UAAwB2F,SAAS,EAAC,aAAa;UAAAL,QAAA,gBAC3CtF,OAAA;YAAK2F,SAAS,EAAC,eAAe;YAAAL,QAAA,gBAC1BtF,OAAA;cAAK2F,SAAS,EAAC,eAAe;cAAAL,QAAA,eAC1BtF,OAAA;gBACI8F,GAAG,EAAE9C,WAAW,CAAC+B,IAAI,CAACc,aAAa,CAAE;gBACrCE,GAAG,EAAEhB,IAAI,CAACc;cAAc;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN1F,OAAA;cAAK2F,SAAS,EAAC,mBAAmB;cAAAL,QAAA,gBAC9BtF,OAAA;gBAAAsF,QAAA,EAAKP,IAAI,CAACR;cAAQ;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxB1F,OAAA;gBAAM2F,SAAS,EAAC,eAAe;gBAAAL,QAAA,EAAEP,IAAI,CAACc;cAAa;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN1F,OAAA;YAAK2F,SAAS,EAAC,mBAAmB;YAAAL,QAAA,gBAC9BtF,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAL,QAAA,gBACtBtF,OAAA;gBAAAsF,QAAA,EAAO;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzB1F,OAAA;gBAAAsF,QAAA,EAAOP,IAAI,CAACsB,UAAU,IAAI;cAAC;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACN1F,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAL,QAAA,gBACtBtF,OAAA;gBAAAsF,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvB1F,OAAA;gBAAAsF,QAAA,GAAOP,IAAI,CAACuB,QAAQ,IAAI,CAAC,EAAC,GAAC;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACN1F,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAL,QAAA,gBACtBtF,OAAA;gBAAAsF,QAAA,EAAO;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpB1F,OAAA;gBAAAsF,QAAA,EAAOP,IAAI,CAACwB,iBAAiB,IAAI;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN1F,OAAA;YACI2F,SAAS,EAAC,gBAAgB;YAC1BO,OAAO,EAAEA,CAAA,KAAMjB,iBAAiB,CAACF,IAAI,CAACpB,OAAO,CAAE;YAAA2B,QAAA,gBAE/CtF,OAAA,CAACH,UAAU;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBAClB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GAlCHX,IAAI,CAACpB,OAAO;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmCjB,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAEA1D,gBAAgB,IAAIE,gBAAgB,iBACjClC,OAAA;MAAK2F,SAAS,EAAC,eAAe;MAAAL,QAAA,eAC1BtF,OAAA;QAAK2F,SAAS,EAAC,eAAe;QAAAL,QAAA,eAC1BtF,OAAA;UAAK2F,SAAS,EAAC,iBAAiB;UAAAL,QAAA,gBAC5BtF,OAAA;YAAK2F,SAAS,EAAC,cAAc;YAAAL,QAAA,eACzBtF,OAAA,CAACV,WAAW;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACN1F,OAAA;YAAAsF,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9B1F,OAAA;YAAAsF,QAAA,GAAG,oCAAkC,eAAAtF,OAAA;cAAM2F,SAAS,EAAC,WAAW;cAAAL,QAAA,EAAEpD,gBAAgB,CAACqC;YAAQ;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxG1F,OAAA;YAAK2F,SAAS,EAAC,iBAAiB;YAAAL,QAAA,gBAC5BtF,OAAA;cACI2F,SAAS,EAAC,YAAY;cACtBO,OAAO,EAAEA,CAAA,KAAM;gBACXjE,mBAAmB,CAAC,KAAK,CAAC;gBAC1BE,mBAAmB,CAAC,IAAI,CAAC;cAC7B,CAAE;cAAAmD,QAAA,EACL;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1F,OAAA;cACI2F,SAAS,EAAC,aAAa;cACvBO,OAAO,EAAE1C,eAAgB;cAAA8B,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAACvF,EAAA,CA9iBQD,OAAO;EAAA,QAeKb,WAAW;AAAA;AAAA+H,EAAA,GAfvBlH,OAAO;AAgjBhB,eAAeA,OAAO;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}