{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\contexts\\\\SiteConfigContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { API_BASE_URL } from '../config';\nimport { refreshFavicon } from '../utils/faviconUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SiteConfigContext = /*#__PURE__*/createContext();\nexport const useSiteConfig = () => {\n  _s();\n  const context = useContext(SiteConfigContext);\n  if (!context) {\n    throw new Error('useSiteConfig must be used within a SiteConfigProvider');\n  }\n  return context;\n};\n_s(useSiteConfig, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const SiteConfigProvider = ({\n  children\n}) => {\n  _s2();\n  const [config, setConfig] = useState({\n    site_name: 'FanBet247',\n    site_logo: 'uploads/logo/fanbet247_logo.svg',\n    contact_email: '<EMAIL>',\n    footer_text: '© 2024 FanBet247. All rights reserved.'\n  });\n  const [loading, setLoading] = useState(true);\n  const fetchConfig = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_site_config.php`);\n      if (response.data.success) {\n        setConfig(response.data.config);\n      }\n    } catch (error) {\n      console.error('Failed to fetch site config:', error);\n      // Keep default values if fetch fails\n    } finally {\n      setLoading(false);\n    }\n  };\n  const updateConfig = newConfig => {\n    setConfig(prev => ({\n      ...prev,\n      ...newConfig\n    }));\n  };\n  const refreshSiteFavicon = async () => {\n    try {\n      await refreshFavicon();\n    } catch (error) {\n      console.error('Failed to refresh favicon:', error);\n    }\n  };\n  useEffect(() => {\n    fetchConfig();\n    // Also initialize favicon on app startup\n    refreshSiteFavicon();\n  }, []);\n  const value = {\n    config,\n    loading,\n    updateConfig,\n    refreshConfig: fetchConfig,\n    refreshSiteFavicon\n  };\n  return /*#__PURE__*/_jsxDEV(SiteConfigContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 9\n  }, this);\n};\n_s2(SiteConfigProvider, \"2RnjRH0RKQdD3JBrKYbyhNgR3v0=\");\n_c = SiteConfigProvider;\nexport default SiteConfigContext;\nvar _c;\n$RefreshReg$(_c, \"SiteConfigProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "API_BASE_URL", "refreshFavicon", "jsxDEV", "_jsxDEV", "SiteConfigContext", "useSiteConfig", "_s", "context", "Error", "SiteConfigProvider", "children", "_s2", "config", "setConfig", "site_name", "site_logo", "contact_email", "footer_text", "loading", "setLoading", "fetchConfig", "response", "get", "data", "success", "error", "console", "updateConfig", "newConfig", "prev", "refreshSiteFavicon", "value", "refreshConfig", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/contexts/SiteConfigContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { API_BASE_URL } from '../config';\nimport { refreshFavicon } from '../utils/faviconUtils';\n\nconst SiteConfigContext = createContext();\n\nexport const useSiteConfig = () => {\n    const context = useContext(SiteConfigContext);\n    if (!context) {\n        throw new Error('useSiteConfig must be used within a SiteConfigProvider');\n    }\n    return context;\n};\n\nexport const SiteConfigProvider = ({ children }) => {\n    const [config, setConfig] = useState({\n        site_name: 'FanBet247',\n        site_logo: 'uploads/logo/fanbet247_logo.svg',\n        contact_email: '<EMAIL>',\n        footer_text: '© 2024 FanBet247. All rights reserved.'\n    });\n    const [loading, setLoading] = useState(true);\n\n    const fetchConfig = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_site_config.php`);\n            if (response.data.success) {\n                setConfig(response.data.config);\n            }\n        } catch (error) {\n            console.error('Failed to fetch site config:', error);\n            // Keep default values if fetch fails\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const updateConfig = (newConfig) => {\n        setConfig(prev => ({ ...prev, ...newConfig }));\n    };\n\n    const refreshSiteFavicon = async () => {\n        try {\n            await refreshFavicon();\n        } catch (error) {\n            console.error('Failed to refresh favicon:', error);\n        }\n    };\n\n    useEffect(() => {\n        fetchConfig();\n        // Also initialize favicon on app startup\n        refreshSiteFavicon();\n    }, []);\n\n    const value = {\n        config,\n        loading,\n        updateConfig,\n        refreshConfig: fetchConfig,\n        refreshSiteFavicon\n    };\n\n    return (\n        <SiteConfigContext.Provider value={value}>\n            {children}\n        </SiteConfigContext.Provider>\n    );\n};\n\nexport default SiteConfigContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,WAAW;AACxC,SAASC,cAAc,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,iBAAiB,gBAAGT,aAAa,CAAC,CAAC;AAEzC,OAAO,MAAMU,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,OAAO,GAAGX,UAAU,CAACQ,iBAAiB,CAAC;EAC7C,IAAI,CAACG,OAAO,EAAE;IACV,MAAM,IAAIC,KAAK,CAAC,wDAAwD,CAAC;EAC7E;EACA,OAAOD,OAAO;AAClB,CAAC;AAACD,EAAA,CANWD,aAAa;AAQ1B,OAAO,MAAMI,kBAAkB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAChD,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC;IACjCiB,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,iCAAiC;IAC5CC,aAAa,EAAE,uBAAuB;IACtCC,WAAW,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMuB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMtB,KAAK,CAACuB,GAAG,CAAC,GAAGtB,YAAY,+BAA+B,CAAC;MAChF,IAAIqB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvBX,SAAS,CAACQ,QAAQ,CAACE,IAAI,CAACX,MAAM,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOa,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD;IACJ,CAAC,SAAS;MACNN,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMQ,YAAY,GAAIC,SAAS,IAAK;IAChCf,SAAS,CAACgB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,GAAGD;IAAU,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAME,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACA,MAAM7B,cAAc,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACtD;EACJ,CAAC;EAED3B,SAAS,CAAC,MAAM;IACZsB,WAAW,CAAC,CAAC;IACb;IACAU,kBAAkB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,KAAK,GAAG;IACVnB,MAAM;IACNM,OAAO;IACPS,YAAY;IACZK,aAAa,EAAEZ,WAAW;IAC1BU;EACJ,CAAC;EAED,oBACI3B,OAAA,CAACC,iBAAiB,CAAC6B,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAArB,QAAA,EACpCA;EAAQ;IAAAwB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAErC,CAAC;AAAC1B,GAAA,CAtDWF,kBAAkB;AAAA6B,EAAA,GAAlB7B,kBAAkB;AAwD/B,eAAeL,iBAAiB;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}