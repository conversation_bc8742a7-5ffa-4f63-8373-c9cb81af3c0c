{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\LeagueDetails.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport { API_BASE_URL } from '../config';\nimport '../styles/leagueDetails.css';\nimport { FaTrophy, FaMedal } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LeagueDetails = () => {\n  _s();\n  const {\n    leagueId\n  } = useParams();\n  const navigate = useNavigate();\n  const [league, setLeague] = useState(null);\n  const [leaderboard, setLeaderboard] = useState([]);\n  const [userPosition, setUserPosition] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const userId = localStorage.getItem('userId');\n  const formatNumber = num => {\n    return new Intl.NumberFormat('en-US', {\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(num);\n  };\n  const getOrdinalSuffix = num => {\n    const j = num % 10;\n    const k = num % 100;\n    if (j === 1 && k !== 11) return num + \"st\";\n    if (j === 2 && k !== 12) return num + \"nd\";\n    if (j === 3 && k !== 13) return num + \"rd\";\n    return num + \"th\";\n  };\n  useEffect(() => {\n    const fetchLeagueDetails = async () => {\n      try {\n        setLoading(true);\n        setError('');\n        const response = await axios.get(`league_details.php?league_id=${leagueId}${userId ? `&user_id=${userId}` : ''}`);\n        console.log('API Response:', response.data);\n        if (response.data.status === 200) {\n          const {\n            league: leagueData,\n            leaderboard: leaderboardData,\n            user_position: userPositionData\n          } = response.data.data;\n          console.log('League data:', leagueData);\n          if (!leagueData || typeof leagueData.id !== 'number') {\n            throw new Error('Invalid league data received');\n          }\n          const formattedLeaderboard = leaderboardData.map((player, index) => ({\n            ...player,\n            deposit_amount_formatted: formatNumber(player.deposit_amount) + ' FC',\n            rank_display: getOrdinalSuffix(player.rank_position),\n            rank_position: index + 1\n          }));\n          const formattedUserPosition = userPositionData ? {\n            ...userPositionData,\n            deposit_amount_formatted: formatNumber(userPositionData.deposit_amount) + ' FC',\n            rank_display: getOrdinalSuffix(userPositionData.rank)\n          } : null;\n          setLeague(leagueData);\n          setLeaderboard(formattedLeaderboard);\n          setUserPosition(formattedUserPosition);\n        } else {\n          setError(response.data.message || 'Failed to load league details');\n        }\n      } catch (err) {\n        var _err$response;\n        console.error('Error fetching league details:', err);\n        if (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status) === 401) {\n          navigate('/login');\n        } else {\n          setError('Failed to load league details. Please try again.');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (leagueId) {\n      fetchLeagueDetails();\n    }\n  }, [leagueId, userId, navigate]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"league-details__container\",\n    children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 17\n    }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 17\n    }, this) : !league ? /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"No league found\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"league-details__banner-wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: league.league_banner,\n          alt: `${league.name} banner`,\n          className: \"league-details__banner-image\",\n          onError: e => {\n            e.target.src = '/images/default-league-banner.jpg';\n            e.target.onerror = null;\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"league-details__banner-overlay\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"league-details__icon-wrapper\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: league.league_icon,\n              alt: `${league.name} icon`,\n              className: \"league-details__icon-image\",\n              onError: e => {\n                e.target.src = '/images/default-league-icon.png';\n                e.target.onerror = null;\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"league-details__banner-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"league-details__title\",\n              children: league.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"league-details__description\",\n              children: league.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 21\n      }, this), userPosition && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"league-details__position-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"league-details__position-title\",\n          children: [/*#__PURE__*/_jsxDEV(FaTrophy, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 33\n          }, this), \" Your Position\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"league-details__stats-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"league-details__stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"league-details__stat-label\",\n              children: \"Rank\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"league-details__stat-value\",\n              children: userPosition.rank_display\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"league-details__stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"league-details__stat-label\",\n              children: \"Points\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"league-details__stat-value\",\n              children: userPosition.points\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"league-details__stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"league-details__stat-label\",\n              children: \"Streak\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"league-details__stat-value\",\n              children: userPosition.current_streak\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"league-details__stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"league-details__stat-label\",\n              children: \"W/D/L\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"league-details__stat-value\",\n              children: [userPosition.wins, \"/\", userPosition.draws, \"/\", userPosition.losses]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"league-details__stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"league-details__stat-label\",\n              children: \"Total Bets\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"league-details__stat-value\",\n              children: userPosition.total_bets\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"league-details__stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"league-details__stat-label\",\n              children: \"Deposit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"league-details__stat-value\",\n              children: userPosition.deposit_amount_formatted\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"league-details__progress-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"league-details__progress-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Progress to Next Rank\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [Math.max(50 - userPosition.points, 0), \" points needed\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"league-details__progress-container\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"league-details__progress-bar\",\n              style: {\n                width: `${Math.min(userPosition.points / 50 * 100, 100)}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 25\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"league-details__leaderboard\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"league-details__leaderboard-title\",\n          children: [/*#__PURE__*/_jsxDEV(FaTrophy, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 29\n          }, this), \" League Leaderboard\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"league-details__table-container\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"league-details__table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Rank\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Player\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Points\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Streak\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"W/D/L\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Total Bets\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Deposit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Join Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: leaderboard.map((player, index) => {\n                var _player$membership_id;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: (userPosition === null || userPosition === void 0 ? void 0 : userPosition.user_id) === player.user_id ? 'current-user' : '',\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"league-details__rank\",\n                      children: [player.rank_display, player.rank_position === 1 && /*#__PURE__*/_jsxDEV(FaTrophy, {\n                        className: \"league-details__rank-icon gold\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 211,\n                        columnNumber: 57\n                      }, this), player.rank_position === 2 && /*#__PURE__*/_jsxDEV(FaMedal, {\n                        className: \"league-details__rank-icon silver\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 214,\n                        columnNumber: 57\n                      }, this), player.rank_position === 3 && /*#__PURE__*/_jsxDEV(FaMedal, {\n                        className: \"league-details__rank-icon bronze\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: player.username\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"league-details__points\",\n                    children: [player.points, /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"league-details__progress-container\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"league-details__progress-bar\",\n                        style: {\n                          width: `${Math.min(player.points / 50 * 100, 100)}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 225,\n                        columnNumber: 53\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: player.current_streak\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [player.wins, \"/\", player.draws, \"/\", player.losses]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: player.total_bets\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: player.deposit_amount_formatted\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: new Date(player.join_date).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 45\n                  }, this)]\n                }, `${player.user_id}-${(_player$membership_id = player.membership_id) !== null && _player$membership_id !== void 0 ? _player$membership_id : index}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 41\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 9\n  }, this);\n};\n_s(LeagueDetails, \"w3B3GM/MaGQdI2/5fr5HS4W4bao=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = LeagueDetails;\nexport default LeagueDetails;\nvar _c;\n$RefreshReg$(_c, \"LeagueDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "axios", "API_BASE_URL", "FaTrophy", "FaMedal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LeagueDetails", "_s", "leagueId", "navigate", "league", "<PERSON><PERSON><PERSON><PERSON>", "leaderboard", "setLeaderboard", "userPosition", "setUserPosition", "loading", "setLoading", "error", "setError", "userId", "localStorage", "getItem", "formatNumber", "num", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "getOrdinalSuffix", "j", "k", "fetchLeagueDetails", "response", "get", "console", "log", "data", "status", "leagueData", "leaderboardData", "user_position", "userPositionData", "id", "Error", "formattedLeaderboard", "map", "player", "index", "deposit_amount_formatted", "deposit_amount", "rank_display", "rank_position", "formattedUserPosition", "rank", "message", "err", "_err$response", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "league_banner", "alt", "name", "onError", "e", "target", "onerror", "league_icon", "description", "points", "current_streak", "wins", "draws", "losses", "total_bets", "Math", "max", "style", "width", "min", "_player$membership_id", "user_id", "username", "Date", "join_date", "toLocaleDateString", "membership_id", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/LeagueDetails.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport { API_BASE_URL } from '../config';\nimport '../styles/leagueDetails.css';\nimport {\n    FaTrophy, FaMedal\n} from 'react-icons/fa';\n\nconst LeagueDetails = () => {\n    const { leagueId } = useParams();\n    const navigate = useNavigate();\n    const [league, setLeague] = useState(null);\n    const [leaderboard, setLeaderboard] = useState([]);\n    const [userPosition, setUserPosition] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const userId = localStorage.getItem('userId');\n\n    const formatNumber = (num) => {\n        return new Intl.NumberFormat('en-US', {\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(num);\n    };\n\n    const getOrdinalSuffix = (num) => {\n        const j = num % 10;\n        const k = num % 100;\n        if (j === 1 && k !== 11) return num + \"st\";\n        if (j === 2 && k !== 12) return num + \"nd\";\n        if (j === 3 && k !== 13) return num + \"rd\";\n        return num + \"th\";\n    };\n\n    useEffect(() => {\n        const fetchLeagueDetails = async () => {\n            try {\n                setLoading(true);\n                setError('');\n                const response = await axios.get(\n                    `league_details.php?league_id=${leagueId}${userId ? `&user_id=${userId}` : ''}`\n                );\n                console.log('API Response:', response.data);\n                \n                if (response.data.status === 200) {\n                    const {\n                        league: leagueData,\n                        leaderboard: leaderboardData,\n                        user_position: userPositionData\n                    } = response.data.data;\n                    console.log('League data:', leagueData);\n                    \n                    if (!leagueData || typeof leagueData.id !== 'number') {\n                        throw new Error('Invalid league data received');\n                    }\n\n                    const formattedLeaderboard = leaderboardData.map((player, index) => ({\n                        ...player,\n                        deposit_amount_formatted: formatNumber(player.deposit_amount) + ' FC',\n                        rank_display: getOrdinalSuffix(player.rank_position),\n                        rank_position: index + 1\n                    }));\n                    \n                    const formattedUserPosition = userPositionData\n                        ? {\n                            ...userPositionData,\n                            deposit_amount_formatted: formatNumber(userPositionData.deposit_amount) + ' FC',\n                            rank_display: getOrdinalSuffix(userPositionData.rank)\n                        }\n                        : null;\n                    \n                    setLeague(leagueData);\n                    setLeaderboard(formattedLeaderboard);\n                    setUserPosition(formattedUserPosition);\n                } else {\n                    setError(response.data.message || 'Failed to load league details');\n                }\n            } catch (err) {\n                console.error('Error fetching league details:', err);\n                if (err.response?.status === 401) {\n                    navigate('/login');\n                } else {\n                    setError('Failed to load league details. Please try again.');\n                }\n            } finally {\n                setLoading(false);\n            }\n        };\n\n        if (leagueId) {\n            fetchLeagueDetails();\n        }\n    }, [leagueId, userId, navigate]);\n\n    return (\n        <div className=\"league-details__container\">\n            {loading ? (\n                <div>Loading...</div>\n            ) : error ? (\n                <div className=\"error-message\">{error}</div>\n            ) : !league ? (\n                <div>No league found</div>\n            ) : (\n                <>\n                    <div className=\"league-details__banner-wrapper\">\n                        <img \n                            src={league.league_banner}\n                            alt={`${league.name} banner`}\n                            className=\"league-details__banner-image\"\n                            onError={(e) => {\n                                e.target.src = '/images/default-league-banner.jpg';\n                                e.target.onerror = null;\n                            }}\n                        />\n                        <div className=\"league-details__banner-overlay\">\n                            <div className=\"league-details__icon-wrapper\">\n                                <img \n                                    src={league.league_icon}\n                                    alt={`${league.name} icon`}\n                                    className=\"league-details__icon-image\"\n                                    onError={(e) => {\n                                        e.target.src = '/images/default-league-icon.png';\n                                        e.target.onerror = null;\n                                    }}\n                                />\n                            </div>\n                            <div className=\"league-details__banner-content\">\n                                <h1 className=\"league-details__title\">{league.name}</h1>\n                                <p className=\"league-details__description\">{league.description}</p>\n                            </div>\n                        </div>\n                    </div>\n\n                    {userPosition && (\n                        <div className=\"league-details__position-card\">\n                            <h2 className=\"league-details__position-title\">\n                                <FaTrophy /> Your Position\n                            </h2>\n                            <div className=\"league-details__stats-grid\">\n                                <div className=\"league-details__stat-item\">\n                                    <span className=\"league-details__stat-label\">Rank</span>\n                                    <span className=\"league-details__stat-value\">{userPosition.rank_display}</span>\n                                </div>\n                                <div className=\"league-details__stat-item\">\n                                    <span className=\"league-details__stat-label\">Points</span>\n                                    <span className=\"league-details__stat-value\">{userPosition.points}</span>\n                                </div>\n                                <div className=\"league-details__stat-item\">\n                                    <span className=\"league-details__stat-label\">Streak</span>\n                                    <span className=\"league-details__stat-value\">{userPosition.current_streak}</span>\n                                </div>\n                                <div className=\"league-details__stat-item\">\n                                    <span className=\"league-details__stat-label\">W/D/L</span>\n                                    <span className=\"league-details__stat-value\">\n                                        {userPosition.wins}/{userPosition.draws}/{userPosition.losses}\n                                    </span>\n                                </div>\n                                <div className=\"league-details__stat-item\">\n                                    <span className=\"league-details__stat-label\">Total Bets</span>\n                                    <span className=\"league-details__stat-value\">{userPosition.total_bets}</span>\n                                </div>\n                                <div className=\"league-details__stat-item\">\n                                    <span className=\"league-details__stat-label\">Deposit</span>\n                                    <span className=\"league-details__stat-value\">{userPosition.deposit_amount_formatted}</span>\n                                </div>\n                            </div>\n                            <div className=\"league-details__progress-section\">\n                                <div className=\"league-details__progress-info\">\n                                    <span>Progress to Next Rank</span>\n                                    <span>{Math.max(50 - userPosition.points, 0)} points needed</span>\n                                </div>\n                                <div className=\"league-details__progress-container\">\n                                    <div \n                                        className=\"league-details__progress-bar\"\n                                        style={{ width: `${Math.min((userPosition.points / 50) * 100, 100)}%` }}\n                                    ></div>\n                                </div>\n                            </div>\n                        </div>\n                    )}\n                    {/* Leaderboard */}\n                    <div className=\"league-details__leaderboard\">\n                        <h2 className=\"league-details__leaderboard-title\">\n                            <FaTrophy /> League Leaderboard\n                        </h2>\n                        <div className=\"league-details__table-container\">\n                            <table className=\"league-details__table\">\n                                <thead>\n                                    <tr>\n                                        <th>Rank</th>\n                                        <th>Player</th>\n                                        <th>Points</th>\n                                        <th>Streak</th>\n                                        <th>W/D/L</th>\n                                        <th>Total Bets</th>\n                                        <th>Deposit</th>\n                                        <th>Join Date</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {leaderboard.map((player, index) => (\n                                        <tr \n                                            key={`${player.user_id}-${player.membership_id ?? index}`}\n                                            className={userPosition?.user_id === player.user_id ? 'current-user' : ''}\n                                        >\n                                            <td>\n                                                <div className=\"league-details__rank\">\n                                                    {player.rank_display}\n                                                    {player.rank_position === 1 && (\n                                                        <FaTrophy className=\"league-details__rank-icon gold\" />\n                                                    )}\n                                                    {player.rank_position === 2 && (\n                                                        <FaMedal className=\"league-details__rank-icon silver\" />\n                                                    )}\n                                                    {player.rank_position === 3 && (\n                                                        <FaMedal className=\"league-details__rank-icon bronze\" />\n                                                    )}\n                                                </div>\n                                            </td>\n                                            <td>{player.username}</td>\n                                            <td className=\"league-details__points\">\n                                                {player.points}\n                                                <div className=\"league-details__progress-container\">\n                                                    <div \n                                                        className=\"league-details__progress-bar\"\n                                                        style={{ width: `${Math.min((player.points / 50) * 100, 100)}%` }}\n                                                    />\n                                                </div>\n                                            </td>\n                                            <td>{player.current_streak}</td>\n                                            <td>{player.wins}/{player.draws}/{player.losses}</td>\n                                            <td>{player.total_bets}</td>\n                                            <td>{player.deposit_amount_formatted}</td>\n                                            <td>{new Date(player.join_date).toLocaleDateString()}</td>\n                                        </tr>\n                                    ))}\n                                </tbody>\n                            </table>\n                        </div>\n                    </div>\n                </>\n            )}\n        </div>\n    );\n};\n\nexport default LeagueDetails;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,6BAA6B;AACpC,SACIC,QAAQ,EAAEC,OAAO,QACd,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAS,CAAC,GAAGZ,SAAS,CAAC,CAAC;EAChC,MAAMa,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM0B,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAE7C,MAAMC,YAAY,GAAIC,GAAG,IAAK;IAC1B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAClCC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IAC3B,CAAC,CAAC,CAACC,MAAM,CAACL,GAAG,CAAC;EAClB,CAAC;EAED,MAAMM,gBAAgB,GAAIN,GAAG,IAAK;IAC9B,MAAMO,CAAC,GAAGP,GAAG,GAAG,EAAE;IAClB,MAAMQ,CAAC,GAAGR,GAAG,GAAG,GAAG;IACnB,IAAIO,CAAC,KAAK,CAAC,IAAIC,CAAC,KAAK,EAAE,EAAE,OAAOR,GAAG,GAAG,IAAI;IAC1C,IAAIO,CAAC,KAAK,CAAC,IAAIC,CAAC,KAAK,EAAE,EAAE,OAAOR,GAAG,GAAG,IAAI;IAC1C,IAAIO,CAAC,KAAK,CAAC,IAAIC,CAAC,KAAK,EAAE,EAAE,OAAOR,GAAG,GAAG,IAAI;IAC1C,OAAOA,GAAG,GAAG,IAAI;EACrB,CAAC;EAED7B,SAAS,CAAC,MAAM;IACZ,MAAMsC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACAhB,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,EAAE,CAAC;QACZ,MAAMe,QAAQ,GAAG,MAAMpC,KAAK,CAACqC,GAAG,CAC5B,gCAAgC3B,QAAQ,GAAGY,MAAM,GAAG,YAAYA,MAAM,EAAE,GAAG,EAAE,EACjF,CAAC;QACDgB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEH,QAAQ,CAACI,IAAI,CAAC;QAE3C,IAAIJ,QAAQ,CAACI,IAAI,CAACC,MAAM,KAAK,GAAG,EAAE;UAC9B,MAAM;YACF7B,MAAM,EAAE8B,UAAU;YAClB5B,WAAW,EAAE6B,eAAe;YAC5BC,aAAa,EAAEC;UACnB,CAAC,GAAGT,QAAQ,CAACI,IAAI,CAACA,IAAI;UACtBF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEG,UAAU,CAAC;UAEvC,IAAI,CAACA,UAAU,IAAI,OAAOA,UAAU,CAACI,EAAE,KAAK,QAAQ,EAAE;YAClD,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;UACnD;UAEA,MAAMC,oBAAoB,GAAGL,eAAe,CAACM,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,MAAM;YACjE,GAAGD,MAAM;YACTE,wBAAwB,EAAE3B,YAAY,CAACyB,MAAM,CAACG,cAAc,CAAC,GAAG,KAAK;YACrEC,YAAY,EAAEtB,gBAAgB,CAACkB,MAAM,CAACK,aAAa,CAAC;YACpDA,aAAa,EAAEJ,KAAK,GAAG;UAC3B,CAAC,CAAC,CAAC;UAEH,MAAMK,qBAAqB,GAAGX,gBAAgB,GACxC;YACE,GAAGA,gBAAgB;YACnBO,wBAAwB,EAAE3B,YAAY,CAACoB,gBAAgB,CAACQ,cAAc,CAAC,GAAG,KAAK;YAC/EC,YAAY,EAAEtB,gBAAgB,CAACa,gBAAgB,CAACY,IAAI;UACxD,CAAC,GACC,IAAI;UAEV5C,SAAS,CAAC6B,UAAU,CAAC;UACrB3B,cAAc,CAACiC,oBAAoB,CAAC;UACpC/B,eAAe,CAACuC,qBAAqB,CAAC;QAC1C,CAAC,MAAM;UACHnC,QAAQ,CAACe,QAAQ,CAACI,IAAI,CAACkB,OAAO,IAAI,+BAA+B,CAAC;QACtE;MACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAC,aAAA;QACVtB,OAAO,CAAClB,KAAK,CAAC,gCAAgC,EAAEuC,GAAG,CAAC;QACpD,IAAI,EAAAC,aAAA,GAAAD,GAAG,CAACvB,QAAQ,cAAAwB,aAAA,uBAAZA,aAAA,CAAcnB,MAAM,MAAK,GAAG,EAAE;UAC9B9B,QAAQ,CAAC,QAAQ,CAAC;QACtB,CAAC,MAAM;UACHU,QAAQ,CAAC,kDAAkD,CAAC;QAChE;MACJ,CAAC,SAAS;QACNF,UAAU,CAAC,KAAK,CAAC;MACrB;IACJ,CAAC;IAED,IAAIT,QAAQ,EAAE;MACVyB,kBAAkB,CAAC,CAAC;IACxB;EACJ,CAAC,EAAE,CAACzB,QAAQ,EAAEY,MAAM,EAAEX,QAAQ,CAAC,CAAC;EAEhC,oBACIN,OAAA;IAAKwD,SAAS,EAAC,2BAA2B;IAAAC,QAAA,EACrC5C,OAAO,gBACJb,OAAA;MAAAyD,QAAA,EAAK;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,GACrB9C,KAAK,gBACLf,OAAA;MAAKwD,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAE1C;IAAK;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,GAC5C,CAACtD,MAAM,gBACPP,OAAA;MAAAyD,QAAA,EAAK;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,gBAE1B7D,OAAA,CAAAE,SAAA;MAAAuD,QAAA,gBACIzD,OAAA;QAAKwD,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC3CzD,OAAA;UACI8D,GAAG,EAAEvD,MAAM,CAACwD,aAAc;UAC1BC,GAAG,EAAE,GAAGzD,MAAM,CAAC0D,IAAI,SAAU;UAC7BT,SAAS,EAAC,8BAA8B;UACxCU,OAAO,EAAGC,CAAC,IAAK;YACZA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,mCAAmC;YAClDK,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;UAC3B;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACF7D,OAAA;UAAKwD,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC3CzD,OAAA;YAAKwD,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eACzCzD,OAAA;cACI8D,GAAG,EAAEvD,MAAM,CAAC+D,WAAY;cACxBN,GAAG,EAAE,GAAGzD,MAAM,CAAC0D,IAAI,OAAQ;cAC3BT,SAAS,EAAC,4BAA4B;cACtCU,OAAO,EAAGC,CAAC,IAAK;gBACZA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,iCAAiC;gBAChDK,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;cAC3B;YAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN7D,OAAA;YAAKwD,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC3CzD,OAAA;cAAIwD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAElD,MAAM,CAAC0D;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxD7D,OAAA;cAAGwD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAElD,MAAM,CAACgE;YAAW;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAELlD,YAAY,iBACTX,OAAA;QAAKwD,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC1CzD,OAAA;UAAIwD,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC1CzD,OAAA,CAACH,QAAQ;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAChB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL7D,OAAA;UAAKwD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACvCzD,OAAA;YAAKwD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACtCzD,OAAA;cAAMwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxD7D,OAAA;cAAMwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAE9C,YAAY,CAACsC;YAAY;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eACN7D,OAAA;YAAKwD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACtCzD,OAAA;cAAMwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1D7D,OAAA;cAAMwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAE9C,YAAY,CAAC6D;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eACN7D,OAAA;YAAKwD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACtCzD,OAAA;cAAMwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1D7D,OAAA;cAAMwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAE9C,YAAY,CAAC8D;YAAc;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC,eACN7D,OAAA;YAAKwD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACtCzD,OAAA;cAAMwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzD7D,OAAA;cAAMwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GACvC9C,YAAY,CAAC+D,IAAI,EAAC,GAAC,EAAC/D,YAAY,CAACgE,KAAK,EAAC,GAAC,EAAChE,YAAY,CAACiE,MAAM;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN7D,OAAA;YAAKwD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACtCzD,OAAA;cAAMwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9D7D,OAAA;cAAMwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAE9C,YAAY,CAACkE;YAAU;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC,eACN7D,OAAA;YAAKwD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACtCzD,OAAA;cAAMwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3D7D,OAAA;cAAMwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAE9C,YAAY,CAACoC;YAAwB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN7D,OAAA;UAAKwD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC7CzD,OAAA;YAAKwD,SAAS,EAAC,+BAA+B;YAAAC,QAAA,gBAC1CzD,OAAA;cAAAyD,QAAA,EAAM;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClC7D,OAAA;cAAAyD,QAAA,GAAOqB,IAAI,CAACC,GAAG,CAAC,EAAE,GAAGpE,YAAY,CAAC6D,MAAM,EAAE,CAAC,CAAC,EAAC,gBAAc;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACN7D,OAAA;YAAKwD,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eAC/CzD,OAAA;cACIwD,SAAS,EAAC,8BAA8B;cACxCwB,KAAK,EAAE;gBAAEC,KAAK,EAAE,GAAGH,IAAI,CAACI,GAAG,CAAEvE,YAAY,CAAC6D,MAAM,GAAG,EAAE,GAAI,GAAG,EAAE,GAAG,CAAC;cAAI;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR,eAED7D,OAAA;QAAKwD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBACxCzD,OAAA;UAAIwD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAC7CzD,OAAA,CAACH,QAAQ;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAChB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL7D,OAAA;UAAKwD,SAAS,EAAC,iCAAiC;UAAAC,QAAA,eAC5CzD,OAAA;YAAOwD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCzD,OAAA;cAAAyD,QAAA,eACIzD,OAAA;gBAAAyD,QAAA,gBACIzD,OAAA;kBAAAyD,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb7D,OAAA;kBAAAyD,QAAA,EAAI;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACf7D,OAAA;kBAAAyD,QAAA,EAAI;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACf7D,OAAA;kBAAAyD,QAAA,EAAI;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACf7D,OAAA;kBAAAyD,QAAA,EAAI;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACd7D,OAAA;kBAAAyD,QAAA,EAAI;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnB7D,OAAA;kBAAAyD,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChB7D,OAAA;kBAAAyD,QAAA,EAAI;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACR7D,OAAA;cAAAyD,QAAA,EACKhD,WAAW,CAACmC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK;gBAAA,IAAAqC,qBAAA;gBAAA,oBAC3BnF,OAAA;kBAEIwD,SAAS,EAAE,CAAA7C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEyE,OAAO,MAAKvC,MAAM,CAACuC,OAAO,GAAG,cAAc,GAAG,EAAG;kBAAA3B,QAAA,gBAE1EzD,OAAA;oBAAAyD,QAAA,eACIzD,OAAA;sBAAKwD,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,GAChCZ,MAAM,CAACI,YAAY,EACnBJ,MAAM,CAACK,aAAa,KAAK,CAAC,iBACvBlD,OAAA,CAACH,QAAQ;wBAAC2D,SAAS,EAAC;sBAAgC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CACzD,EACAhB,MAAM,CAACK,aAAa,KAAK,CAAC,iBACvBlD,OAAA,CAACF,OAAO;wBAAC0D,SAAS,EAAC;sBAAkC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAC1D,EACAhB,MAAM,CAACK,aAAa,KAAK,CAAC,iBACvBlD,OAAA,CAACF,OAAO;wBAAC0D,SAAS,EAAC;sBAAkC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAC1D;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACL7D,OAAA;oBAAAyD,QAAA,EAAKZ,MAAM,CAACwC;kBAAQ;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1B7D,OAAA;oBAAIwD,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,GACjCZ,MAAM,CAAC2B,MAAM,eACdxE,OAAA;sBAAKwD,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,eAC/CzD,OAAA;wBACIwD,SAAS,EAAC,8BAA8B;wBACxCwB,KAAK,EAAE;0BAAEC,KAAK,EAAE,GAAGH,IAAI,CAACI,GAAG,CAAErC,MAAM,CAAC2B,MAAM,GAAG,EAAE,GAAI,GAAG,EAAE,GAAG,CAAC;wBAAI;sBAAE;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACL7D,OAAA;oBAAAyD,QAAA,EAAKZ,MAAM,CAAC4B;kBAAc;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChC7D,OAAA;oBAAAyD,QAAA,GAAKZ,MAAM,CAAC6B,IAAI,EAAC,GAAC,EAAC7B,MAAM,CAAC8B,KAAK,EAAC,GAAC,EAAC9B,MAAM,CAAC+B,MAAM;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrD7D,OAAA;oBAAAyD,QAAA,EAAKZ,MAAM,CAACgC;kBAAU;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5B7D,OAAA;oBAAAyD,QAAA,EAAKZ,MAAM,CAACE;kBAAwB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1C7D,OAAA;oBAAAyD,QAAA,EAAK,IAAI6B,IAAI,CAACzC,MAAM,CAAC0C,SAAS,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA,GA/BrD,GAAGhB,MAAM,CAACuC,OAAO,KAAAD,qBAAA,GAAItC,MAAM,CAAC4C,aAAa,cAAAN,qBAAA,cAAAA,qBAAA,GAAIrC,KAAK,EAAE;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgCzD,CAAC;cAAA,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,eACR;EACL;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACzD,EAAA,CA5OID,aAAa;EAAA,QACMV,SAAS,EACbC,WAAW;AAAA;AAAAgG,EAAA,GAF1BvF,aAAa;AA8OnB,eAAeA,aAAa;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}