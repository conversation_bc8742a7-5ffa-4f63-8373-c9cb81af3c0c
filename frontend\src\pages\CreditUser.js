import React, { useState, useEffect } from 'react';
import axios from '../utils/axiosConfig';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaUser, FaMoneyBillWave, FaPlus } from 'react-icons/fa';

function CreditUser() {
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState('');
  const [amount, setAmount] = useState('');
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
      fetchUsers();
  }, []);

  const fetchUsers = async () => {
      try {
          const response = await axios.get('get_users.php');
          if (response.data.success) {
              setUsers(response.data.users);
          }
      } catch (err) {
          setError('Failed to fetch users');
      }
  };

  const handleCredit = async (e) => {
      e.preventDefault();

      if (!selectedUser || !amount) {
          setError('Please fill in all fields');
          return;
      }

      try {
          const formData = new FormData();
          formData.append('user_id', selectedUser);
          formData.append('amount', amount);

          const response = await axios.post(
              `${API_BASE_URL}/handlers/credit_user.php`,
              formData,
              { headers: { 'Content-Type': 'multipart/form-data' } }
          );

          if (response.data.success) {
              setSuccess('User credited successfully!');
              setAmount('');
              setSelectedUser('');
              fetchUsers(); // Refresh user list to show updated balance
          } else {
              setError(response.data.message);
          }
      } catch (err) {
          setError('Failed to credit user');
      }
  };

  return (
      <div className="p-6 bg-gray-50 min-h-screen">
          {/* Page Header */}
          <div className="mb-8">
              <h1 className="text-2xl font-bold text-gray-800">Credit User</h1>
              <p className="text-gray-600">Add funds to a user's account balance</p>
          </div>

          {/* Notification Messages */}
          {error && (
              <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                  <span className="block sm:inline">{error}</span>
              </div>
          )}
          {success && (
              <div className="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                  <span className="block sm:inline">{success}</span>
              </div>
          )}

          {/* Form Card */}
          <div className="bg-white rounded-lg shadow-sm p-6 w-full">
              <div className="flex items-center mb-6">
                  <div className="rounded-full bg-green-100 p-3 mr-4">
                      <FaCoins className="text-green-500 text-xl" />
                  </div>
                  <h2 className="text-lg font-semibold text-gray-800">Credit User Account</h2>
              </div>

              <form onSubmit={handleCredit} className="space-y-6">
                  {/* Select User */}
                  <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Select User</label>
                      <div className="relative rounded-md shadow-sm">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                              <FaUser className="h-4 w-4 text-green-600" />
                          </div>
                          <select
                              value={selectedUser}
                              onChange={(e) => setSelectedUser(e.target.value)}
                              className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                              required
                              style={{paddingLeft: '2.5rem'}}
                          >
                              <option value="">Select a user</option>
                              {users.map(user => (
                                  <option key={user.user_id} value={user.user_id}>
                                      {user.username} - Current Balance: {user.balance} FC
                                  </option>
                              ))}
                          </select>
                      </div>
                  </div>

                  {/* Credit Amount */}
                  <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Credit Amount (FanCoins)</label>
                      <div className="relative rounded-md shadow-sm">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                              <FaMoneyBillWave className="h-4 w-4 text-green-600" />
                          </div>
                          <input
                              type="number"
                              value={amount}
                              onChange={(e) => setAmount(e.target.value)}
                              className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                              placeholder="Enter amount to credit"
                              min="1"
                              required
                              style={{paddingLeft: '2.5rem'}}
                          />
                      </div>
                  </div>

                  {/* Submit Button */}
                  <div className="pt-4">
                      <button
                          type="submit"
                          className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-700 hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                      >
                          <FaPlus className="mr-2 h-5 w-5" /> Credit User
                      </button>
                  </div>
              </form>
          </div>
      </div>
  );
}

export default CreditUser;