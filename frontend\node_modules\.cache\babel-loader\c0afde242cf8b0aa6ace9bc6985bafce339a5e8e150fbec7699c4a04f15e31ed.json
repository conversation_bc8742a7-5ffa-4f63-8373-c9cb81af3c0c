{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\Challenges.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from '../utils/axiosConfig';\nimport { Link, useNavigate } from 'react-router-dom';\nimport Countdown from 'react-countdown';\nimport './Challenges.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ITEMS_PER_PAGE = 20;\nfunction Challenges() {\n  _s();\n  const navigate = useNavigate();\n  const [challenges, setChallenges] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const [error, setError] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [filter, setFilter] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  useEffect(() => {\n    const userId = localStorage.getItem('userId');\n    if (!userId) {\n      navigate('/user/login');\n      return;\n    }\n    const initializePage = async () => {\n      try {\n        setError(null);\n        await Promise.all([fetchChallenges(), fetchTeams()]);\n      } catch (err) {\n        console.error('Page initialization error:', err);\n        setError('Failed to load challenges. Please try again later.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    initializePage();\n  }, [navigate]);\n  const fetchChallenges = async () => {\n    try {\n      console.log('Fetching challenges...');\n      const response = await axios.get(`${API_BASE_URL}/handlers/challenge_management.php`);\n      console.log('Challenges response:', response.data);\n      if (response.data.success) {\n        const challengesWithDate = (response.data.challenges || []).sort((a, b) => {\n          // Sort by status priority and then by date\n          const statusPriority = {\n            'Open': 1,\n            'Closed': 2,\n            'Settled': 3,\n            'Expired': 4\n          };\n          if (statusPriority[a.status] !== statusPriority[b.status]) {\n            return statusPriority[a.status] - statusPriority[b.status];\n          }\n          return new Date(b.match_date) - new Date(a.match_date);\n        }).map(challenge => ({\n          ...challenge,\n          end_time: new Date(challenge.end_time)\n        }));\n        setChallenges(challengesWithDate);\n      } else {\n        console.error('Failed to fetch challenges:', response.data.message);\n        throw new Error(response.data.message || 'Failed to fetch challenges');\n      }\n    } catch (error) {\n      console.error('Error fetching challenges:', error);\n      throw error;\n    }\n  };\n  const fetchTeams = async () => {\n    try {\n      console.log('Fetching teams...');\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      console.log('Teams response:', response.data);\n      if (response.data.status === 200) {\n        setTeams(response.data.data || []);\n      } else {\n        console.error('Failed to fetch teams:', response.data.message);\n        throw new Error(response.data.message || 'Failed to fetch teams');\n      }\n    } catch (err) {\n      console.error('Error fetching teams:', err);\n      throw err;\n    }\n  };\n  const getTeamLogo = teamName => {\n    const team = teams.find(team => team.name === teamName);\n    return team ? `${API_BASE_URL}/${team.logo}` : '';\n  };\n  const countdownRenderer = ({\n    days,\n    hours,\n    minutes,\n    seconds,\n    completed\n  }) => {\n    if (completed) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"challenges-countdown-compact\",\n        children: \"Expired\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 20\n      }, this);\n    }\n    let display = '';\n    if (days > 0) display += `${days}d `;\n    if (hours > 0) display += `${hours}h `;\n    if (minutes > 0) display += `${minutes}m `;\n    display += `${seconds}s`;\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"challenges-countdown-compact\",\n      children: display\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 16\n    }, this);\n  };\n  const filteredChallenges = challenges.filter(challenge => {\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      return challenge.team_a.toLowerCase().includes(searchLower) || challenge.team_b.toLowerCase().includes(searchLower);\n    }\n    return true;\n  }).filter(challenge => {\n    const now = new Date();\n    const matchDate = new Date(challenge.match_date);\n    const endTime = new Date(challenge.end_time);\n    switch (filter) {\n      case 'closed':\n        return challenge.status === 'Closed';\n      case 'expired':\n        return now > endTime && challenge.status !== 'Settled';\n      case 'settled':\n        return challenge.status === 'Settled';\n      default:\n        return true;\n    }\n  }).sort((a, b) => {\n    const now = new Date();\n    const aMatchDate = new Date(a.match_date);\n    const bMatchDate = new Date(b.match_date);\n    const aEndTime = new Date(a.end_time);\n    const bEndTime = new Date(b.end_time);\n\n    // Sort based on filter type\n    if (filter === 'closed') {\n      return bMatchDate - aMatchDate; // Most recent matches first\n    } else if (filter === 'expired') {\n      return bEndTime - aEndTime; // Most recently expired first\n    } else if (filter === 'settled') {\n      return bMatchDate - aMatchDate; // Most recent matches first\n    }\n\n    // For 'all' filter, sort by status and then date\n    const statusPriority = {\n      'Open': 1,\n      'Closed': 2,\n      'Expired': 3,\n      'Settled': 4\n    };\n    if (statusPriority[a.status] !== statusPriority[b.status]) {\n      return statusPriority[a.status] - statusPriority[b.status];\n    }\n\n    // If same status, sort by appropriate date\n    if (a.status === 'Expired') {\n      return bEndTime - aEndTime; // Most recently expired first\n    } else {\n      return aMatchDate - bMatchDate; // Upcoming matches first\n    }\n  });\n\n  // Pagination calculations\n  const totalItems = filteredChallenges.length;\n  const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);\n  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;\n  const paginatedChallenges = filteredChallenges.slice(startIndex, startIndex + ITEMS_PER_PAGE);\n  const handlePageChange = pageNumber => {\n    setCurrentPage(pageNumber);\n    window.scrollTo(0, 0);\n  };\n  const renderPagination = () => {\n    const pages = [];\n    const maxVisiblePages = 5;\n    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));\n    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);\n    if (endPage - startPage + 1 < maxVisiblePages) {\n      startPage = Math.max(1, endPage - maxVisiblePages + 1);\n    }\n    if (startPage > 1) {\n      pages.push(/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => handlePageChange(1),\n        className: \"pagination-button\",\n        children: \"1\"\n      }, \"first\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 17\n      }, this));\n      if (startPage > 2) {\n        pages.push(/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"pagination-ellipsis\",\n          children: \"...\"\n        }, \"ellipsis1\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 28\n        }, this));\n      }\n    }\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => handlePageChange(i),\n        className: `pagination-button ${currentPage === i ? 'active' : ''}`,\n        children: i\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 17\n      }, this));\n    }\n    if (endPage < totalPages) {\n      if (endPage < totalPages - 1) {\n        pages.push(/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"pagination-ellipsis\",\n          children: \"...\"\n        }, \"ellipsis2\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 28\n        }, this));\n      }\n      pages.push(/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => handlePageChange(totalPages),\n        className: \"pagination-button\",\n        children: totalPages\n      }, \"last\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 17\n      }, this));\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pagination\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => handlePageChange(currentPage - 1),\n        disabled: currentPage === 1,\n        className: \"pagination-button nav\",\n        children: \"\\u2190\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 17\n      }, this), pages, /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => handlePageChange(currentPage + 1),\n        disabled: currentPage === totalPages,\n        className: \"pagination-button nav\",\n        children: \"\\u2192\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 13\n    }, this);\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"challenges-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 13\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"challenges-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Loading challenges...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"challenges-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"challenges-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"All Challenges\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"challenges-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-box\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search teams...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `filter-button ${filter === 'all' ? 'active' : ''}`,\n            onClick: () => setFilter('all'),\n            children: \"All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `filter-button ${filter === 'closed' ? 'active' : ''}`,\n            onClick: () => setFilter('closed'),\n            children: \"Closed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `filter-button ${filter === 'expired' ? 'active' : ''}`,\n            onClick: () => setFilter('expired'),\n            children: \"Expired\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `filter-button ${filter === 'settled' ? 'active' : ''}`,\n            onClick: () => setFilter('settled'),\n            children: \"Settled\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"challenges-list\",\n      children: filteredChallenges.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-data\",\n        children: \"No challenges found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-container\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"number-column\",\n                  children: \"#\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Teams\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Odds\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Goals\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Time Left\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: paginatedChallenges.map((challenge, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"number-column\",\n                  children: startIndex + index + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"challenges-teams-compact\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"challenges-team-info\",\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: getTeamLogo(challenge.team_a),\n                        alt: challenge.team_a,\n                        className: \"challenges-team-logo-small\",\n                        onError: e => {\n                          e.target.src = '/default-team-logo.png';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 334,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"challenges-team-name-compact\",\n                        children: challenge.team_a\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 342,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"challenges-vs-small\",\n                      children: \"vs\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 344,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"challenges-team-info\",\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: getTeamLogo(challenge.team_b),\n                        alt: challenge.team_b,\n                        className: \"challenges-team-logo-small\",\n                        onError: e => {\n                          e.target.src = '/default-team-logo.png';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 346,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"challenges-team-name-compact\",\n                        children: challenge.team_b\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 354,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"challenges-odds-compact\",\n                  children: [challenge.odds_team_a, \" - \", challenge.odds_team_b]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"challenges-goals-compact\",\n                  children: [\"+\", challenge.team_a_goal_advantage, \" / +\", challenge.team_b_goal_advantage]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"challenges-date-compact\",\n                  children: new Date(challenge.match_date).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(Countdown, {\n                    date: new Date(challenge.end_time),\n                    renderer: countdownRenderer,\n                    onComplete: () => {}\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `status-badge ${challenge.status.toLowerCase()}`,\n                    children: challenge.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: challenge.status === 'Open' && /*#__PURE__*/_jsxDEV(Link, {\n                    to: `/user/join-challenge/${challenge.challenge_id}`,\n                    className: \"challenges-place-bet-button-small\",\n                    children: \"Bet\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 53\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 45\n                }, this)]\n              }, challenge.challenge_id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 25\n        }, this), totalPages > 1 && renderPagination()]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 267,\n    columnNumber: 9\n  }, this);\n}\n_s(Challenges, \"CJyr+xJpOvA8kj4tHJSoK2lgHDk=\", false, function () {\n  return [useNavigate];\n});\n_c = Challenges;\nexport default Challenges;\nvar _c;\n$RefreshReg$(_c, \"Challenges\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Link", "useNavigate", "Countdown", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ITEMS_PER_PAGE", "Challenges", "_s", "navigate", "challenges", "setChallenges", "teams", "setTeams", "error", "setError", "loading", "setLoading", "filter", "setFilter", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "userId", "localStorage", "getItem", "initializePage", "Promise", "all", "fetchChallenges", "fetchTeams", "err", "console", "log", "response", "get", "API_BASE_URL", "data", "success", "challengesWithDate", "sort", "a", "b", "statusPriority", "status", "Date", "match_date", "map", "challenge", "end_time", "message", "Error", "getTeamLogo", "teamName", "team", "find", "name", "logo", "countdownR<PERSON>er", "days", "hours", "minutes", "seconds", "completed", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "filteredChallenges", "searchLower", "toLowerCase", "team_a", "includes", "team_b", "now", "matchDate", "endTime", "aMatchDate", "bMatchDate", "aEndTime", "bEndTime", "totalItems", "length", "totalPages", "Math", "ceil", "startIndex", "paginatedChallenges", "slice", "handlePageChange", "pageNumber", "window", "scrollTo", "renderPagination", "pages", "maxVisiblePages", "startPage", "max", "floor", "endPage", "min", "push", "onClick", "i", "disabled", "type", "placeholder", "value", "onChange", "e", "target", "index", "src", "alt", "onError", "odds_team_a", "odds_team_b", "team_a_goal_advantage", "team_b_goal_advantage", "toLocaleDateString", "date", "renderer", "onComplete", "to", "challenge_id", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/Challenges.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from '../utils/axiosConfig';\nimport { Link, useNavigate } from 'react-router-dom';\nimport Countdown from 'react-countdown';\nimport './Challenges.css';\nconst ITEMS_PER_PAGE = 20;\n\nfunction Challenges() {\n    const navigate = useNavigate();\n    const [challenges, setChallenges] = useState([]);\n    const [teams, setTeams] = useState([]);\n    const [error, setError] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [filter, setFilter] = useState('all'); \n    const [searchTerm, setSearchTerm] = useState('');\n    const [currentPage, setCurrentPage] = useState(1);\n\n    useEffect(() => {\n        const userId = localStorage.getItem('userId');\n        if (!userId) {\n            navigate('/user/login');\n            return;\n        }\n\n        const initializePage = async () => {\n            try {\n                setError(null);\n                await Promise.all([\n                    fetchChallenges(),\n                    fetchTeams()\n                ]);\n            } catch (err) {\n                console.error('Page initialization error:', err);\n                setError('Failed to load challenges. Please try again later.');\n            } finally {\n                setLoading(false);\n            }\n        };\n\n        initializePage();\n    }, [navigate]);\n\n    const fetchChallenges = async () => {\n        try {\n            console.log('Fetching challenges...');\n            const response = await axios.get(`${API_BASE_URL}/handlers/challenge_management.php`);\n            console.log('Challenges response:', response.data);\n            \n            if (response.data.success) {\n                const challengesWithDate = (response.data.challenges || []).sort((a, b) => {\n                    // Sort by status priority and then by date\n                    const statusPriority = { 'Open': 1, 'Closed': 2, 'Settled': 3, 'Expired': 4 };\n                    if (statusPriority[a.status] !== statusPriority[b.status]) {\n                        return statusPriority[a.status] - statusPriority[b.status];\n                    }\n                    return new Date(b.match_date) - new Date(a.match_date);\n                }).map(challenge => ({\n                    ...challenge,\n                    end_time: new Date(challenge.end_time)\n                }));\n                setChallenges(challengesWithDate);\n            } else {\n                console.error('Failed to fetch challenges:', response.data.message);\n                throw new Error(response.data.message || 'Failed to fetch challenges');\n            }\n        } catch (error) {\n            console.error('Error fetching challenges:', error);\n            throw error;\n        }\n    };\n\n    const fetchTeams = async () => {\n        try {\n            console.log('Fetching teams...');\n            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n            console.log('Teams response:', response.data);\n            \n            if (response.data.status === 200) {\n                setTeams(response.data.data || []);\n            } else {\n                console.error('Failed to fetch teams:', response.data.message);\n                throw new Error(response.data.message || 'Failed to fetch teams');\n            }\n        } catch (err) {\n            console.error('Error fetching teams:', err);\n            throw err;\n        }\n    };\n\n    const getTeamLogo = (teamName) => {\n        const team = teams.find(team => team.name === teamName);\n        return team ? `${API_BASE_URL}/${team.logo}` : '';\n    };\n\n    const countdownRenderer = ({ days, hours, minutes, seconds, completed }) => {\n        if (completed) {\n            return <span className=\"challenges-countdown-compact\">Expired</span>;\n        }\n\n        let display = '';\n        if (days > 0) display += `${days}d `;\n        if (hours > 0) display += `${hours}h `;\n        if (minutes > 0) display += `${minutes}m `;\n        display += `${seconds}s`;\n\n        return <span className=\"challenges-countdown-compact\">{display}</span>;\n    };\n\n    const filteredChallenges = challenges\n        .filter(challenge => {\n            if (searchTerm) {\n                const searchLower = searchTerm.toLowerCase();\n                return challenge.team_a.toLowerCase().includes(searchLower) ||\n                       challenge.team_b.toLowerCase().includes(searchLower);\n            }\n            return true;\n        })\n        .filter(challenge => {\n            const now = new Date();\n            const matchDate = new Date(challenge.match_date);\n            const endTime = new Date(challenge.end_time);\n            \n            switch (filter) {\n                case 'closed':\n                    return challenge.status === 'Closed';\n                case 'expired':\n                    return now > endTime && challenge.status !== 'Settled';\n                case 'settled':\n                    return challenge.status === 'Settled';\n                default:\n                    return true;\n            }\n        })\n        .sort((a, b) => {\n            const now = new Date();\n            const aMatchDate = new Date(a.match_date);\n            const bMatchDate = new Date(b.match_date);\n            const aEndTime = new Date(a.end_time);\n            const bEndTime = new Date(b.end_time);\n\n            // Sort based on filter type\n            if (filter === 'closed') {\n                return bMatchDate - aMatchDate; // Most recent matches first\n            } else if (filter === 'expired') {\n                return bEndTime - aEndTime; // Most recently expired first\n            } else if (filter === 'settled') {\n                return bMatchDate - aMatchDate; // Most recent matches first\n            }\n\n            // For 'all' filter, sort by status and then date\n            const statusPriority = {\n                'Open': 1,\n                'Closed': 2,\n                'Expired': 3,\n                'Settled': 4\n            };\n\n            if (statusPriority[a.status] !== statusPriority[b.status]) {\n                return statusPriority[a.status] - statusPriority[b.status];\n            }\n\n            // If same status, sort by appropriate date\n            if (a.status === 'Expired') {\n                return bEndTime - aEndTime; // Most recently expired first\n            } else {\n                return aMatchDate - bMatchDate; // Upcoming matches first\n            }\n        });\n\n    // Pagination calculations\n    const totalItems = filteredChallenges.length;\n    const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);\n    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;\n    const paginatedChallenges = filteredChallenges.slice(startIndex, startIndex + ITEMS_PER_PAGE);\n\n    const handlePageChange = (pageNumber) => {\n        setCurrentPage(pageNumber);\n        window.scrollTo(0, 0);\n    };\n\n    const renderPagination = () => {\n        const pages = [];\n        const maxVisiblePages = 5;\n        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));\n        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);\n\n        if (endPage - startPage + 1 < maxVisiblePages) {\n            startPage = Math.max(1, endPage - maxVisiblePages + 1);\n        }\n\n        if (startPage > 1) {\n            pages.push(\n                <button key=\"first\" onClick={() => handlePageChange(1)} className=\"pagination-button\">\n                    1\n                </button>\n            );\n            if (startPage > 2) {\n                pages.push(<span key=\"ellipsis1\" className=\"pagination-ellipsis\">...</span>);\n            }\n        }\n\n        for (let i = startPage; i <= endPage; i++) {\n            pages.push(\n                <button\n                    key={i}\n                    onClick={() => handlePageChange(i)}\n                    className={`pagination-button ${currentPage === i ? 'active' : ''}`}\n                >\n                    {i}\n                </button>\n            );\n        }\n\n        if (endPage < totalPages) {\n            if (endPage < totalPages - 1) {\n                pages.push(<span key=\"ellipsis2\" className=\"pagination-ellipsis\">...</span>);\n            }\n            pages.push(\n                <button\n                    key=\"last\"\n                    onClick={() => handlePageChange(totalPages)}\n                    className=\"pagination-button\"\n                >\n                    {totalPages}\n                </button>\n            );\n        }\n\n        return (\n            <div className=\"pagination\">\n                <button\n                    onClick={() => handlePageChange(currentPage - 1)}\n                    disabled={currentPage === 1}\n                    className=\"pagination-button nav\"\n                >\n                    ←\n                </button>\n                {pages}\n                <button\n                    onClick={() => handlePageChange(currentPage + 1)}\n                    disabled={currentPage === totalPages}\n                    className=\"pagination-button nav\"\n                >\n                    →\n                </button>\n            </div>\n        );\n    };\n\n    if (error) {\n        return (\n            <div className=\"challenges-page\">\n                <div className=\"error-message\">{error}</div>\n            </div>\n        );\n    }\n\n    if (loading) {\n        return (\n            <div className=\"challenges-page\">\n                <div className=\"loading\">Loading challenges...</div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"challenges-page\">\n            <div className=\"challenges-header\">\n                <h1>All Challenges</h1>\n                <div className=\"challenges-controls\">\n                    <div className=\"search-box\">\n                        <input\n                            type=\"text\"\n                            placeholder=\"Search teams...\"\n                            value={searchTerm}\n                            onChange={(e) => setSearchTerm(e.target.value)}\n                        />\n                    </div>\n                    <div className=\"filter-buttons\">\n                        <button \n                            className={`filter-button ${filter === 'all' ? 'active' : ''}`}\n                            onClick={() => setFilter('all')}\n                        >\n                            All\n                        </button>\n                        <button \n                            className={`filter-button ${filter === 'closed' ? 'active' : ''}`}\n                            onClick={() => setFilter('closed')}\n                        >\n                            Closed\n                        </button>\n                        <button \n                            className={`filter-button ${filter === 'expired' ? 'active' : ''}`}\n                            onClick={() => setFilter('expired')}\n                        >\n                            Expired\n                        </button>\n                        <button \n                            className={`filter-button ${filter === 'settled' ? 'active' : ''}`}\n                            onClick={() => setFilter('settled')}\n                        >\n                            Settled\n                        </button>\n                    </div>\n                </div>\n            </div>\n\n            <div className=\"challenges-list\">\n                {filteredChallenges.length === 0 ? (\n                    <div className=\"no-data\">No challenges found</div>\n                ) : (\n                    <>\n                        <div className=\"table-container\">\n                            <table>\n                                <thead>\n                                    <tr>\n                                        <th className=\"number-column\">#</th>\n                                        <th>Teams</th>\n                                        <th>Odds</th>\n                                        <th>Goals</th>\n                                        <th>Date</th>\n                                        <th>Time Left</th>\n                                        <th>Status</th>\n                                        <th>Action</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {paginatedChallenges.map((challenge, index) => (\n                                        <tr key={challenge.challenge_id}>\n                                            <td className=\"number-column\">{startIndex + index + 1}</td>\n                                            <td>\n                                                <div className=\"challenges-teams-compact\">\n                                                    <div className=\"challenges-team-info\">\n                                                        <img \n                                                            src={getTeamLogo(challenge.team_a)}\n                                                            alt={challenge.team_a}\n                                                            className=\"challenges-team-logo-small\"\n                                                            onError={(e) => {\n                                                                e.target.src = '/default-team-logo.png';\n                                                            }}\n                                                        />\n                                                        <span className=\"challenges-team-name-compact\">{challenge.team_a}</span>\n                                                    </div>\n                                                    <span className=\"challenges-vs-small\">vs</span>\n                                                    <div className=\"challenges-team-info\">\n                                                        <img \n                                                            src={getTeamLogo(challenge.team_b)}\n                                                            alt={challenge.team_b}\n                                                            className=\"challenges-team-logo-small\"\n                                                            onError={(e) => {\n                                                                e.target.src = '/default-team-logo.png';\n                                                            }}\n                                                        />\n                                                        <span className=\"challenges-team-name-compact\">{challenge.team_b}</span>\n                                                    </div>\n                                                </div>\n                                            </td>\n                                            <td className=\"challenges-odds-compact\">{challenge.odds_team_a} - {challenge.odds_team_b}</td>\n                                            <td className=\"challenges-goals-compact\">+{challenge.team_a_goal_advantage} / +{challenge.team_b_goal_advantage}</td>\n                                            <td className=\"challenges-date-compact\">{new Date(challenge.match_date).toLocaleDateString()}</td>\n                                            <td>\n                                                <Countdown \n                                                    date={new Date(challenge.end_time)}\n                                                    renderer={countdownRenderer}\n                                                    onComplete={() => {}}\n                                                />\n                                            </td>\n                                            <td>\n                                                <span className={`status-badge ${challenge.status.toLowerCase()}`}>\n                                                    {challenge.status}\n                                                </span>\n                                            </td>\n                                            <td>\n                                                {challenge.status === 'Open' && (\n                                                    <Link to={`/user/join-challenge/${challenge.challenge_id}`} className=\"challenges-place-bet-button-small\">\n                                                        Bet\n                                                    </Link>\n                                                )}\n                                            </td>\n                                        </tr>\n                                    ))}\n                                </tbody>\n                            </table>\n                        </div>\n                        {totalPages > 1 && renderPagination()}\n                    </>\n                )}\n            </div>\n        </div>\n    );\n}\n\nexport default Challenges;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAC1B,MAAMC,cAAc,GAAG,EAAE;AAEzB,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACZ,MAAM2B,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAC7C,IAAI,CAACF,MAAM,EAAE;MACTf,QAAQ,CAAC,aAAa,CAAC;MACvB;IACJ;IAEA,MAAMkB,cAAc,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACAZ,QAAQ,CAAC,IAAI,CAAC;QACd,MAAMa,OAAO,CAACC,GAAG,CAAC,CACdC,eAAe,CAAC,CAAC,EACjBC,UAAU,CAAC,CAAC,CACf,CAAC;MACN,CAAC,CAAC,OAAOC,GAAG,EAAE;QACVC,OAAO,CAACnB,KAAK,CAAC,4BAA4B,EAAEkB,GAAG,CAAC;QAChDjB,QAAQ,CAAC,oDAAoD,CAAC;MAClE,CAAC,SAAS;QACNE,UAAU,CAAC,KAAK,CAAC;MACrB;IACJ,CAAC;IAEDU,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAAClB,QAAQ,CAAC,CAAC;EAEd,MAAMqB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACAG,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,MAAMC,QAAQ,GAAG,MAAMrC,KAAK,CAACsC,GAAG,CAAC,GAAGC,YAAY,oCAAoC,CAAC;MACrFJ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,QAAQ,CAACG,IAAI,CAAC;MAElD,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;QACvB,MAAMC,kBAAkB,GAAG,CAACL,QAAQ,CAACG,IAAI,CAAC5B,UAAU,IAAI,EAAE,EAAE+B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACvE;UACA,MAAMC,cAAc,GAAG;YAAE,MAAM,EAAE,CAAC;YAAE,QAAQ,EAAE,CAAC;YAAE,SAAS,EAAE,CAAC;YAAE,SAAS,EAAE;UAAE,CAAC;UAC7E,IAAIA,cAAc,CAACF,CAAC,CAACG,MAAM,CAAC,KAAKD,cAAc,CAACD,CAAC,CAACE,MAAM,CAAC,EAAE;YACvD,OAAOD,cAAc,CAACF,CAAC,CAACG,MAAM,CAAC,GAAGD,cAAc,CAACD,CAAC,CAACE,MAAM,CAAC;UAC9D;UACA,OAAO,IAAIC,IAAI,CAACH,CAAC,CAACI,UAAU,CAAC,GAAG,IAAID,IAAI,CAACJ,CAAC,CAACK,UAAU,CAAC;QAC1D,CAAC,CAAC,CAACC,GAAG,CAACC,SAAS,KAAK;UACjB,GAAGA,SAAS;UACZC,QAAQ,EAAE,IAAIJ,IAAI,CAACG,SAAS,CAACC,QAAQ;QACzC,CAAC,CAAC,CAAC;QACHvC,aAAa,CAAC6B,kBAAkB,CAAC;MACrC,CAAC,MAAM;QACHP,OAAO,CAACnB,KAAK,CAAC,6BAA6B,EAAEqB,QAAQ,CAACG,IAAI,CAACa,OAAO,CAAC;QACnE,MAAM,IAAIC,KAAK,CAACjB,QAAQ,CAACG,IAAI,CAACa,OAAO,IAAI,4BAA4B,CAAC;MAC1E;IACJ,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACZmB,OAAO,CAACnB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACf;EACJ,CAAC;EAED,MAAMiB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACAE,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,MAAMC,QAAQ,GAAG,MAAMrC,KAAK,CAACsC,GAAG,CAAC,GAAGC,YAAY,+BAA+B,CAAC;MAChFJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEC,QAAQ,CAACG,IAAI,CAAC;MAE7C,IAAIH,QAAQ,CAACG,IAAI,CAACO,MAAM,KAAK,GAAG,EAAE;QAC9BhC,QAAQ,CAACsB,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACtC,CAAC,MAAM;QACHL,OAAO,CAACnB,KAAK,CAAC,wBAAwB,EAAEqB,QAAQ,CAACG,IAAI,CAACa,OAAO,CAAC;QAC9D,MAAM,IAAIC,KAAK,CAACjB,QAAQ,CAACG,IAAI,CAACa,OAAO,IAAI,uBAAuB,CAAC;MACrE;IACJ,CAAC,CAAC,OAAOnB,GAAG,EAAE;MACVC,OAAO,CAACnB,KAAK,CAAC,uBAAuB,EAAEkB,GAAG,CAAC;MAC3C,MAAMA,GAAG;IACb;EACJ,CAAC;EAED,MAAMqB,WAAW,GAAIC,QAAQ,IAAK;IAC9B,MAAMC,IAAI,GAAG3C,KAAK,CAAC4C,IAAI,CAACD,IAAI,IAAIA,IAAI,CAACE,IAAI,KAAKH,QAAQ,CAAC;IACvD,OAAOC,IAAI,GAAG,GAAGlB,YAAY,IAAIkB,IAAI,CAACG,IAAI,EAAE,GAAG,EAAE;EACrD,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAC;IAAEC,IAAI;IAAEC,KAAK;IAAEC,OAAO;IAAEC,OAAO;IAAEC;EAAU,CAAC,KAAK;IACxE,IAAIA,SAAS,EAAE;MACX,oBAAO7D,OAAA;QAAM8D,SAAS,EAAC,8BAA8B;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACxE;IAEA,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIX,IAAI,GAAG,CAAC,EAAEW,OAAO,IAAI,GAAGX,IAAI,IAAI;IACpC,IAAIC,KAAK,GAAG,CAAC,EAAEU,OAAO,IAAI,GAAGV,KAAK,IAAI;IACtC,IAAIC,OAAO,GAAG,CAAC,EAAES,OAAO,IAAI,GAAGT,OAAO,IAAI;IAC1CS,OAAO,IAAI,GAAGR,OAAO,GAAG;IAExB,oBAAO5D,OAAA;MAAM8D,SAAS,EAAC,8BAA8B;MAAAC,QAAA,EAAEK;IAAO;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAC1E,CAAC;EAED,MAAME,kBAAkB,GAAG9D,UAAU,CAChCQ,MAAM,CAAC+B,SAAS,IAAI;IACjB,IAAI7B,UAAU,EAAE;MACZ,MAAMqD,WAAW,GAAGrD,UAAU,CAACsD,WAAW,CAAC,CAAC;MAC5C,OAAOzB,SAAS,CAAC0B,MAAM,CAACD,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,IACpDxB,SAAS,CAAC4B,MAAM,CAACH,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC;IAC/D;IACA,OAAO,IAAI;EACf,CAAC,CAAC,CACDvD,MAAM,CAAC+B,SAAS,IAAI;IACjB,MAAM6B,GAAG,GAAG,IAAIhC,IAAI,CAAC,CAAC;IACtB,MAAMiC,SAAS,GAAG,IAAIjC,IAAI,CAACG,SAAS,CAACF,UAAU,CAAC;IAChD,MAAMiC,OAAO,GAAG,IAAIlC,IAAI,CAACG,SAAS,CAACC,QAAQ,CAAC;IAE5C,QAAQhC,MAAM;MACV,KAAK,QAAQ;QACT,OAAO+B,SAAS,CAACJ,MAAM,KAAK,QAAQ;MACxC,KAAK,SAAS;QACV,OAAOiC,GAAG,GAAGE,OAAO,IAAI/B,SAAS,CAACJ,MAAM,KAAK,SAAS;MAC1D,KAAK,SAAS;QACV,OAAOI,SAAS,CAACJ,MAAM,KAAK,SAAS;MACzC;QACI,OAAO,IAAI;IACnB;EACJ,CAAC,CAAC,CACDJ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACZ,MAAMmC,GAAG,GAAG,IAAIhC,IAAI,CAAC,CAAC;IACtB,MAAMmC,UAAU,GAAG,IAAInC,IAAI,CAACJ,CAAC,CAACK,UAAU,CAAC;IACzC,MAAMmC,UAAU,GAAG,IAAIpC,IAAI,CAACH,CAAC,CAACI,UAAU,CAAC;IACzC,MAAMoC,QAAQ,GAAG,IAAIrC,IAAI,CAACJ,CAAC,CAACQ,QAAQ,CAAC;IACrC,MAAMkC,QAAQ,GAAG,IAAItC,IAAI,CAACH,CAAC,CAACO,QAAQ,CAAC;;IAErC;IACA,IAAIhC,MAAM,KAAK,QAAQ,EAAE;MACrB,OAAOgE,UAAU,GAAGD,UAAU,CAAC,CAAC;IACpC,CAAC,MAAM,IAAI/D,MAAM,KAAK,SAAS,EAAE;MAC7B,OAAOkE,QAAQ,GAAGD,QAAQ,CAAC,CAAC;IAChC,CAAC,MAAM,IAAIjE,MAAM,KAAK,SAAS,EAAE;MAC7B,OAAOgE,UAAU,GAAGD,UAAU,CAAC,CAAC;IACpC;;IAEA;IACA,MAAMrC,cAAc,GAAG;MACnB,MAAM,EAAE,CAAC;MACT,QAAQ,EAAE,CAAC;MACX,SAAS,EAAE,CAAC;MACZ,SAAS,EAAE;IACf,CAAC;IAED,IAAIA,cAAc,CAACF,CAAC,CAACG,MAAM,CAAC,KAAKD,cAAc,CAACD,CAAC,CAACE,MAAM,CAAC,EAAE;MACvD,OAAOD,cAAc,CAACF,CAAC,CAACG,MAAM,CAAC,GAAGD,cAAc,CAACD,CAAC,CAACE,MAAM,CAAC;IAC9D;;IAEA;IACA,IAAIH,CAAC,CAACG,MAAM,KAAK,SAAS,EAAE;MACxB,OAAOuC,QAAQ,GAAGD,QAAQ,CAAC,CAAC;IAChC,CAAC,MAAM;MACH,OAAOF,UAAU,GAAGC,UAAU,CAAC,CAAC;IACpC;EACJ,CAAC,CAAC;;EAEN;EACA,MAAMG,UAAU,GAAGb,kBAAkB,CAACc,MAAM;EAC5C,MAAMC,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACJ,UAAU,GAAG/E,cAAc,CAAC;EACzD,MAAMoF,UAAU,GAAG,CAACpE,WAAW,GAAG,CAAC,IAAIhB,cAAc;EACrD,MAAMqF,mBAAmB,GAAGnB,kBAAkB,CAACoB,KAAK,CAACF,UAAU,EAAEA,UAAU,GAAGpF,cAAc,CAAC;EAE7F,MAAMuF,gBAAgB,GAAIC,UAAU,IAAK;IACrCvE,cAAc,CAACuE,UAAU,CAAC;IAC1BC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACzB,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,MAAMC,KAAK,GAAG,EAAE;IAChB,MAAMC,eAAe,GAAG,CAAC;IACzB,IAAIC,SAAS,GAAGZ,IAAI,CAACa,GAAG,CAAC,CAAC,EAAE/E,WAAW,GAAGkE,IAAI,CAACc,KAAK,CAACH,eAAe,GAAG,CAAC,CAAC,CAAC;IAC1E,IAAII,OAAO,GAAGf,IAAI,CAACgB,GAAG,CAACjB,UAAU,EAAEa,SAAS,GAAGD,eAAe,GAAG,CAAC,CAAC;IAEnE,IAAII,OAAO,GAAGH,SAAS,GAAG,CAAC,GAAGD,eAAe,EAAE;MAC3CC,SAAS,GAAGZ,IAAI,CAACa,GAAG,CAAC,CAAC,EAAEE,OAAO,GAAGJ,eAAe,GAAG,CAAC,CAAC;IAC1D;IAEA,IAAIC,SAAS,GAAG,CAAC,EAAE;MACfF,KAAK,CAACO,IAAI,cACNtG,OAAA;QAAoBuG,OAAO,EAAEA,CAAA,KAAMb,gBAAgB,CAAC,CAAC,CAAE;QAAC5B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAEtF,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CACZ,CAAC;MACD,IAAI8B,SAAS,GAAG,CAAC,EAAE;QACfF,KAAK,CAACO,IAAI,cAACtG,OAAA;UAAsB8D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAAG,GAA/C,WAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA0C,CAAC,CAAC;MAChF;IACJ;IAEA,KAAK,IAAIqC,CAAC,GAAGP,SAAS,EAAEO,CAAC,IAAIJ,OAAO,EAAEI,CAAC,EAAE,EAAE;MACvCT,KAAK,CAACO,IAAI,cACNtG,OAAA;QAEIuG,OAAO,EAAEA,CAAA,KAAMb,gBAAgB,CAACc,CAAC,CAAE;QACnC1C,SAAS,EAAE,qBAAqB3C,WAAW,KAAKqF,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAzC,QAAA,EAEnEyC;MAAC,GAJGA,CAAC;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKF,CACZ,CAAC;IACL;IAEA,IAAIiC,OAAO,GAAGhB,UAAU,EAAE;MACtB,IAAIgB,OAAO,GAAGhB,UAAU,GAAG,CAAC,EAAE;QAC1BW,KAAK,CAACO,IAAI,cAACtG,OAAA;UAAsB8D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAAG,GAA/C,WAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA0C,CAAC,CAAC;MAChF;MACA4B,KAAK,CAACO,IAAI,cACNtG,OAAA;QAEIuG,OAAO,EAAEA,CAAA,KAAMb,gBAAgB,CAACN,UAAU,CAAE;QAC5CtB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAE5BqB;MAAU,GAJP,MAAM;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKN,CACZ,CAAC;IACL;IAEA,oBACInE,OAAA;MAAK8D,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACvB/D,OAAA;QACIuG,OAAO,EAAEA,CAAA,KAAMb,gBAAgB,CAACvE,WAAW,GAAG,CAAC,CAAE;QACjDsF,QAAQ,EAAEtF,WAAW,KAAK,CAAE;QAC5B2C,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EACpC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACR4B,KAAK,eACN/F,OAAA;QACIuG,OAAO,EAAEA,CAAA,KAAMb,gBAAgB,CAACvE,WAAW,GAAG,CAAC,CAAE;QACjDsF,QAAQ,EAAEtF,WAAW,KAAKiE,UAAW;QACrCtB,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EACpC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEd,CAAC;EAED,IAAIxD,KAAK,EAAE;IACP,oBACIX,OAAA;MAAK8D,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC5B/D,OAAA;QAAK8D,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEpD;MAAK;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC;EAEd;EAEA,IAAItD,OAAO,EAAE;IACT,oBACIb,OAAA;MAAK8D,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC5B/D,OAAA;QAAK8D,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAEd;EAEA,oBACInE,OAAA;IAAK8D,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC5B/D,OAAA;MAAK8D,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9B/D,OAAA;QAAA+D,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvBnE,OAAA;QAAK8D,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChC/D,OAAA;UAAK8D,SAAS,EAAC,YAAY;UAAAC,QAAA,eACvB/D,OAAA;YACI0G,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,iBAAiB;YAC7BC,KAAK,EAAE3F,UAAW;YAClB4F,QAAQ,EAAGC,CAAC,IAAK5F,aAAa,CAAC4F,CAAC,CAACC,MAAM,CAACH,KAAK;UAAE;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNnE,OAAA;UAAK8D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC3B/D,OAAA;YACI8D,SAAS,EAAE,iBAAiB/C,MAAM,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC/DwF,OAAO,EAAEA,CAAA,KAAMvF,SAAS,CAAC,KAAK,CAAE;YAAA+C,QAAA,EACnC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnE,OAAA;YACI8D,SAAS,EAAE,iBAAiB/C,MAAM,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;YAClEwF,OAAO,EAAEA,CAAA,KAAMvF,SAAS,CAAC,QAAQ,CAAE;YAAA+C,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnE,OAAA;YACI8D,SAAS,EAAE,iBAAiB/C,MAAM,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;YACnEwF,OAAO,EAAEA,CAAA,KAAMvF,SAAS,CAAC,SAAS,CAAE;YAAA+C,QAAA,EACvC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnE,OAAA;YACI8D,SAAS,EAAE,iBAAiB/C,MAAM,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;YACnEwF,OAAO,EAAEA,CAAA,KAAMvF,SAAS,CAAC,SAAS,CAAE;YAAA+C,QAAA,EACvC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENnE,OAAA;MAAK8D,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC3BM,kBAAkB,CAACc,MAAM,KAAK,CAAC,gBAC5BnF,OAAA;QAAK8D,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAElDnE,OAAA,CAAAE,SAAA;QAAA6D,QAAA,gBACI/D,OAAA;UAAK8D,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC5B/D,OAAA;YAAA+D,QAAA,gBACI/D,OAAA;cAAA+D,QAAA,eACI/D,OAAA;gBAAA+D,QAAA,gBACI/D,OAAA;kBAAI8D,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpCnE,OAAA;kBAAA+D,QAAA,EAAI;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACdnE,OAAA;kBAAA+D,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACbnE,OAAA;kBAAA+D,QAAA,EAAI;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACdnE,OAAA;kBAAA+D,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACbnE,OAAA;kBAAA+D,QAAA,EAAI;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClBnE,OAAA;kBAAA+D,QAAA,EAAI;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACfnE,OAAA;kBAAA+D,QAAA,EAAI;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACRnE,OAAA;cAAA+D,QAAA,EACKyB,mBAAmB,CAAC3C,GAAG,CAAC,CAACC,SAAS,EAAEkE,KAAK,kBACtChH,OAAA;gBAAA+D,QAAA,gBACI/D,OAAA;kBAAI8D,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEwB,UAAU,GAAGyB,KAAK,GAAG;gBAAC;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3DnE,OAAA;kBAAA+D,QAAA,eACI/D,OAAA;oBAAK8D,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,gBACrC/D,OAAA;sBAAK8D,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACjC/D,OAAA;wBACIiH,GAAG,EAAE/D,WAAW,CAACJ,SAAS,CAAC0B,MAAM,CAAE;wBACnC0C,GAAG,EAAEpE,SAAS,CAAC0B,MAAO;wBACtBV,SAAS,EAAC,4BAA4B;wBACtCqD,OAAO,EAAGL,CAAC,IAAK;0BACZA,CAAC,CAACC,MAAM,CAACE,GAAG,GAAG,wBAAwB;wBAC3C;sBAAE;wBAAAjD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACFnE,OAAA;wBAAM8D,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,EAAEjB,SAAS,CAAC0B;sBAAM;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC,eACNnE,OAAA;sBAAM8D,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/CnE,OAAA;sBAAK8D,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACjC/D,OAAA;wBACIiH,GAAG,EAAE/D,WAAW,CAACJ,SAAS,CAAC4B,MAAM,CAAE;wBACnCwC,GAAG,EAAEpE,SAAS,CAAC4B,MAAO;wBACtBZ,SAAS,EAAC,4BAA4B;wBACtCqD,OAAO,EAAGL,CAAC,IAAK;0BACZA,CAAC,CAACC,MAAM,CAACE,GAAG,GAAG,wBAAwB;wBAC3C;sBAAE;wBAAAjD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACFnE,OAAA;wBAAM8D,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,EAAEjB,SAAS,CAAC4B;sBAAM;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACLnE,OAAA;kBAAI8D,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,GAAEjB,SAAS,CAACsE,WAAW,EAAC,KAAG,EAACtE,SAAS,CAACuE,WAAW;gBAAA;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9FnE,OAAA;kBAAI8D,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,GAAC,GAAC,EAACjB,SAAS,CAACwE,qBAAqB,EAAC,MAAI,EAACxE,SAAS,CAACyE,qBAAqB;gBAAA;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrHnE,OAAA;kBAAI8D,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAE,IAAIpB,IAAI,CAACG,SAAS,CAACF,UAAU,CAAC,CAAC4E,kBAAkB,CAAC;gBAAC;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClGnE,OAAA;kBAAA+D,QAAA,eACI/D,OAAA,CAACF,SAAS;oBACN2H,IAAI,EAAE,IAAI9E,IAAI,CAACG,SAAS,CAACC,QAAQ,CAAE;oBACnC2E,QAAQ,EAAElE,iBAAkB;oBAC5BmE,UAAU,EAAEA,CAAA,KAAM,CAAC;kBAAE;oBAAA3D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACLnE,OAAA;kBAAA+D,QAAA,eACI/D,OAAA;oBAAM8D,SAAS,EAAE,gBAAgBhB,SAAS,CAACJ,MAAM,CAAC6B,WAAW,CAAC,CAAC,EAAG;oBAAAR,QAAA,EAC7DjB,SAAS,CAACJ;kBAAM;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACLnE,OAAA;kBAAA+D,QAAA,EACKjB,SAAS,CAACJ,MAAM,KAAK,MAAM,iBACxB1C,OAAA,CAACJ,IAAI;oBAACgI,EAAE,EAAE,wBAAwB9E,SAAS,CAAC+E,YAAY,EAAG;oBAAC/D,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAE1G;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBACT;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA,GAlDArB,SAAS,CAAC+E,YAAY;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmD3B,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,EACLiB,UAAU,GAAG,CAAC,IAAIU,gBAAgB,CAAC,CAAC;MAAA,eACvC;IACL;MAAA9B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAAC9D,EAAA,CA/XQD,UAAU;EAAA,QACEP,WAAW;AAAA;AAAAiI,EAAA,GADvB1H,UAAU;AAiYnB,eAAeA,UAAU;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}