{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\Messages\\\\Messages.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport axios from '../../utils/axiosConfig';\nimport { FaPaperPlane, FaTimes, FaInbox, FaArrowLeft, FaPaperPlane as FaSent, FaComments } from 'react-icons/fa';\nimport './Messages.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Messages = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('inbox');\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [error, setError] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const messagesEndRef = useRef(null);\n  const currentUserId = localStorage.getItem('userId');\n  const currentUsername = localStorage.getItem('username');\n  useEffect(() => {\n    fetchConversations();\n    const interval = setInterval(fetchConversations, 30000);\n    return () => clearInterval(interval);\n  }, [activeTab]);\n  useEffect(() => {\n    if (selectedConversation) {\n      fetchMessages(selectedConversation.user_id);\n      markMessagesAsRead(selectedConversation.user_id);\n      const interval = setInterval(() => fetchMessages(selectedConversation.user_id), 5000);\n      return () => clearInterval(interval);\n    }\n  }, [selectedConversation]);\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [messages]);\n  const handleDeleteConversation = async () => {\n    try {\n      const response = await axios.post('messages.php', {\n        action: 'delete_conversation',\n        user_id: currentUserId,\n        other_user_id: selectedConversation.user_id\n      });\n      if (response.data.success) {\n        setSelectedConversation(null);\n        fetchConversations();\n        setShowDeleteConfirm(false);\n      }\n    } catch (error) {\n      console.error('Error deleting conversation:', error);\n    }\n  };\n  const fetchConversations = async () => {\n    try {\n      const response = await axios.get('messages.php', {\n        params: {\n          user_id: currentUserId,\n          type: activeTab\n        }\n      });\n      if (response.data.success) {\n        setConversations(response.data.conversations || []);\n      }\n    } catch (error) {\n      console.error('Error fetching conversations:', error);\n    }\n  };\n  const fetchMessages = async otherUserId => {\n    try {\n      const response = await axios.get('messages.php', {\n        params: {\n          user_id: currentUserId,\n          other_user_id: otherUserId,\n          type: 'conversation'\n        }\n      });\n      if (response.data.success) {\n        setMessages(response.data.messages || []);\n      }\n    } catch (error) {\n      console.error('Error fetching messages:', error);\n    }\n  };\n  const markMessagesAsRead = async otherUserId => {\n    try {\n      await axios.post('messages.php', {\n        action: 'mark_read',\n        recipient_id: currentUserId,\n        sender_id: otherUserId\n      });\n    } catch (error) {\n      console.error('Error marking messages as read:', error);\n    }\n  };\n  const handleSendMessage = async e => {\n    e.preventDefault();\n    if (!newMessage.trim() || !selectedConversation) return;\n    setIsLoading(true);\n    try {\n      const response = await axios.post('messages.php', {\n        sender_id: currentUserId,\n        recipient_id: selectedConversation.user_id,\n        content: newMessage.trim()\n      });\n      if (response.data.success) {\n        setNewMessage('');\n        fetchMessages(selectedConversation.user_id);\n      } else {\n        setError(response.data.message || 'Failed to send message');\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n      setError('Failed to send message. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleConversationSelect = conv => {\n    setSelectedConversation(conv);\n    markMessagesAsRead(conv.user_id);\n  };\n  const formatMessageDate = date => {\n    const messageDate = new Date(date);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (messageDate.toDateString() === today.toDateString()) {\n      return 'Today';\n    } else if (messageDate.toDateString() === yesterday.toDateString()) {\n      return 'Yesterday';\n    } else {\n      return messageDate.toLocaleDateString();\n    }\n  };\n  const formatMessageTime = date => {\n    return new Date(date).toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit',\n      hour12: true\n    });\n  };\n  const groupMessagesByDateAndUser = messages => {\n    const groups = {};\n    let currentDate = null;\n    let currentGroup = [];\n    let lastSenderId = null;\n    messages.forEach(message => {\n      const messageDate = formatMessageDate(message.created_at);\n\n      // Start new group if date changes or sender changes\n      if (messageDate !== currentDate || message.sender_id !== lastSenderId) {\n        if (currentGroup.length > 0) {\n          if (!groups[currentDate]) {\n            groups[currentDate] = [];\n          }\n          groups[currentDate].push(currentGroup);\n        }\n        currentGroup = [message];\n        currentDate = messageDate;\n      } else {\n        currentGroup.push(message);\n      }\n      lastSenderId = message.sender_id;\n    });\n\n    // Add the last group\n    if (currentGroup.length > 0 && currentDate) {\n      if (!groups[currentDate]) {\n        groups[currentDate] = [];\n      }\n      groups[currentDate].push(currentGroup);\n    }\n    return groups;\n  };\n  const renderMessages = () => {\n    const groupedMessages = groupMessagesByDateAndUser(messages);\n    return Object.entries(groupedMessages).map(([date, dateGroups]) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-date-divider\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"message-date\",\n          children: date\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 17\n      }, this), dateGroups.map((group, groupIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-group\",\n        children: group.map((message, messageIndex) => {\n          const isSent = message.sender_id === currentUserId;\n          const username = isSent ? currentUsername : message.sender_username;\n          const showUsername = messageIndex === 0;\n          const isUnread = !message.is_read && !isSent;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `message ${message.message_type} ${isUnread ? 'unread' : ''}`,\n            children: [showUsername && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-user-info\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-username\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"username-text\",\n                  children: username.toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-bubble\",\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-time\",\n                children: formatMessageTime(message.created_at)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 37\n            }, this)]\n          }, message.id || messageIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 33\n          }, this);\n        })\n      }, `${date}-${groupIndex}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 21\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 17\n      }, this)]\n    }, date, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 13\n    }, this));\n  };\n  const renderConversationsList = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"conversations-list\",\n    children: conversations.map(conv => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `conversation-item ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.user_id) === conv.user_id ? 'active' : ''} ${!conv.is_read ? 'unread' : ''}`,\n      onClick: () => handleConversationSelect(conv),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"avatar\",\n        children: conv.username[0].toUpperCase()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"conversation-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"conversation-name\",\n          children: [conv.username, !conv.is_read && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"unread-indicator\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 47\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"conversation-preview\",\n          children: conv.last_message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"conversation-time\",\n          children: formatMessageTime(conv.last_message_time)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 21\n      }, this)]\n    }, conv.user_id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 243,\n    columnNumber: 9\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"messages-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `messages-sidebar ${!selectedConversation ? 'active' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"messages-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Messages\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"messages-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `tab ${activeTab === 'inbox' ? 'active' : ''}`,\n          onClick: () => setActiveTab('inbox'),\n          children: [/*#__PURE__*/_jsxDEV(FaInbox, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 25\n          }, this), \" Inbox\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `tab ${activeTab === 'sent' ? 'active' : ''}`,\n          onClick: () => setActiveTab('sent'),\n          children: [/*#__PURE__*/_jsxDEV(FaSent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 25\n          }, this), \" Sent\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"conversations-list\",\n        children: renderConversationsList()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-area\",\n      children: selectedConversation ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chat-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"back-button\",\n            onClick: () => setSelectedConversation(null),\n            children: /*#__PURE__*/_jsxDEV(FaArrowLeft, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chat-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"avatar\",\n              children: selectedConversation.username[0].toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Chat with \", selectedConversation.username.charAt(0).toUpperCase() + selectedConversation.username.slice(1).toLowerCase()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"delete-button\",\n            onClick: () => setShowDeleteConfirm(true),\n            children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 25\n        }, this), showDeleteConfirm && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Are you sure you want to delete this conversation?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"delete-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleDeleteConversation,\n              children: \"Yes, Delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowDeleteConfirm(false),\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"messages-content\",\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 39\n          }, this), renderMessages(), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSendMessage,\n          className: \"message-input-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"message-input\",\n            value: newMessage,\n            onChange: e => setNewMessage(e.target.value),\n            placeholder: \"Type a message...\",\n            onKeyDown: e => {\n              if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage(e);\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"send-button\",\n            disabled: isLoading || !newMessage.trim(),\n            children: /*#__PURE__*/_jsxDEV(FaPaperPlane, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-conversation\",\n        children: [/*#__PURE__*/_jsxDEV(FaComments, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Select a conversation to start messaging\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 271,\n    columnNumber: 9\n  }, this);\n};\n_s(Messages, \"6OC/Sm5BhkUww/kw5ffaI5cXblU=\");\n_c = Messages;\nexport default Messages;\nvar _c;\n$RefreshReg$(_c, \"Messages\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "axios", "FaPaperPlane", "FaTimes", "FaInbox", "FaArrowLeft", "FaSent", "FaComments", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Messages", "_s", "activeTab", "setActiveTab", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "newMessage", "setNewMessage", "error", "setError", "isLoading", "setIsLoading", "showDeleteConfirm", "setShowDeleteConfirm", "messagesEndRef", "currentUserId", "localStorage", "getItem", "currentUsername", "fetchConversations", "interval", "setInterval", "clearInterval", "fetchMessages", "user_id", "markMessagesAsRead", "current", "scrollIntoView", "behavior", "handleDeleteConversation", "response", "post", "action", "other_user_id", "data", "success", "console", "get", "params", "type", "otherUserId", "recipient_id", "sender_id", "handleSendMessage", "e", "preventDefault", "trim", "content", "message", "handleConversationSelect", "conv", "formatMessageDate", "date", "messageDate", "Date", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "formatMessageTime", "toLocaleTimeString", "hour", "minute", "hour12", "groupMessagesByDateAndUser", "groups", "currentDate", "currentGroup", "lastSenderId", "for<PERSON>ach", "created_at", "length", "push", "renderMessages", "groupedMessages", "Object", "entries", "map", "dateGroups", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "group", "groupIndex", "messageIndex", "isSent", "username", "sender_username", "showUsername", "isUnread", "is_read", "message_type", "toUpperCase", "id", "ref", "renderConversationsList", "onClick", "last_message", "last_message_time", "char<PERSON>t", "slice", "toLowerCase", "onSubmit", "value", "onChange", "target", "placeholder", "onKeyDown", "key", "shift<PERSON>ey", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/Messages/Messages.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport axios from '../../utils/axiosConfig';\nimport { FaPaperPlane, FaTimes, FaInbox, FaArrowLeft, FaPaperPlane as FaSent, FaComments } from 'react-icons/fa';\nimport './Messages.css';\n\nconst Messages = () => {\n    const [activeTab, setActiveTab] = useState('inbox');\n    const [conversations, setConversations] = useState([]);\n    const [selectedConversation, setSelectedConversation] = useState(null);\n    const [messages, setMessages] = useState([]);\n    const [newMessage, setNewMessage] = useState('');\n    const [error, setError] = useState(null);\n    const [isLoading, setIsLoading] = useState(false);\n    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n    const messagesEndRef = useRef(null);\n    const currentUserId = localStorage.getItem('userId');\n    const currentUsername = localStorage.getItem('username');\n\n    useEffect(() => {\n        fetchConversations();\n        const interval = setInterval(fetchConversations, 30000);\n        return () => clearInterval(interval);\n    }, [activeTab]);\n\n    useEffect(() => {\n        if (selectedConversation) {\n            fetchMessages(selectedConversation.user_id);\n            markMessagesAsRead(selectedConversation.user_id);\n            const interval = setInterval(() => fetchMessages(selectedConversation.user_id), 5000);\n            return () => clearInterval(interval);\n        }\n    }, [selectedConversation]);\n\n    useEffect(() => {\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n        }\n    }, [messages]);\n\n    const handleDeleteConversation = async () => {\n        try {\n            const response = await axios.post('messages.php', {\n                action: 'delete_conversation',\n                user_id: currentUserId,\n                other_user_id: selectedConversation.user_id\n            });\n            \n            if (response.data.success) {\n                setSelectedConversation(null);\n                fetchConversations();\n                setShowDeleteConfirm(false);\n            }\n        } catch (error) {\n            console.error('Error deleting conversation:', error);\n        }\n    };\n\n    const fetchConversations = async () => {\n        try {\n            const response = await axios.get('messages.php', {\n                params: {\n                    user_id: currentUserId,\n                    type: activeTab\n                }\n            });\n            if (response.data.success) {\n                setConversations(response.data.conversations || []);\n            }\n        } catch (error) {\n            console.error('Error fetching conversations:', error);\n        }\n    };\n\n    const fetchMessages = async (otherUserId) => {\n        try {\n            const response = await axios.get('messages.php', {\n                params: {\n                    user_id: currentUserId,\n                    other_user_id: otherUserId,\n                    type: 'conversation'\n                }\n            });\n            if (response.data.success) {\n                setMessages(response.data.messages || []);\n            }\n        } catch (error) {\n            console.error('Error fetching messages:', error);\n        }\n    };\n\n    const markMessagesAsRead = async (otherUserId) => {\n        try {\n            await axios.post('messages.php', {\n                action: 'mark_read',\n                recipient_id: currentUserId,\n                sender_id: otherUserId\n            });\n        } catch (error) {\n            console.error('Error marking messages as read:', error);\n        }\n    };\n\n    const handleSendMessage = async (e) => {\n        e.preventDefault();\n        if (!newMessage.trim() || !selectedConversation) return;\n\n        setIsLoading(true);\n        try {\n            const response = await axios.post('messages.php', {\n                sender_id: currentUserId,\n                recipient_id: selectedConversation.user_id,\n                content: newMessage.trim()\n            });\n\n            if (response.data.success) {\n                setNewMessage('');\n                fetchMessages(selectedConversation.user_id);\n            } else {\n                setError(response.data.message || 'Failed to send message');\n            }\n        } catch (error) {\n            console.error('Error sending message:', error);\n            setError('Failed to send message. Please try again.');\n        } finally {\n            setIsLoading(false);\n        }\n    };\n\n    const handleConversationSelect = (conv) => {\n        setSelectedConversation(conv);\n        markMessagesAsRead(conv.user_id);\n    };\n\n    const formatMessageDate = (date) => {\n        const messageDate = new Date(date);\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n\n        if (messageDate.toDateString() === today.toDateString()) {\n            return 'Today';\n        } else if (messageDate.toDateString() === yesterday.toDateString()) {\n            return 'Yesterday';\n        } else {\n            return messageDate.toLocaleDateString();\n        }\n    };\n\n    const formatMessageTime = (date) => {\n        return new Date(date).toLocaleTimeString([], { \n            hour: '2-digit', \n            minute: '2-digit',\n            hour12: true \n        });\n    };\n\n    const groupMessagesByDateAndUser = (messages) => {\n        const groups = {};\n        let currentDate = null;\n        let currentGroup = [];\n        let lastSenderId = null;\n\n        messages.forEach((message) => {\n            const messageDate = formatMessageDate(message.created_at);\n            \n            // Start new group if date changes or sender changes\n            if (messageDate !== currentDate || message.sender_id !== lastSenderId) {\n                if (currentGroup.length > 0) {\n                    if (!groups[currentDate]) {\n                        groups[currentDate] = [];\n                    }\n                    groups[currentDate].push(currentGroup);\n                }\n                currentGroup = [message];\n                currentDate = messageDate;\n            } else {\n                currentGroup.push(message);\n            }\n            \n            lastSenderId = message.sender_id;\n        });\n\n        // Add the last group\n        if (currentGroup.length > 0 && currentDate) {\n            if (!groups[currentDate]) {\n                groups[currentDate] = [];\n            }\n            groups[currentDate].push(currentGroup);\n        }\n\n        return groups;\n    };\n\n    const renderMessages = () => {\n        const groupedMessages = groupMessagesByDateAndUser(messages);\n        \n        return Object.entries(groupedMessages).map(([date, dateGroups]) => (\n            <React.Fragment key={date}>\n                <div className=\"message-date-divider\">\n                    <span className=\"message-date\">{date}</span>\n                </div>\n                {dateGroups.map((group, groupIndex) => (\n                    <div key={`${date}-${groupIndex}`} className=\"message-group\">\n                        {group.map((message, messageIndex) => {\n                            const isSent = message.sender_id === currentUserId;\n                            const username = isSent ? currentUsername : message.sender_username;\n                            const showUsername = messageIndex === 0;\n                            const isUnread = !message.is_read && !isSent;\n\n                            return (\n                                <div \n                                    key={message.id || messageIndex} \n                                    className={`message ${message.message_type} ${isUnread ? 'unread' : ''}`}\n                                >\n                                    {showUsername && (\n                                        <div className=\"message-user-info\">\n                                            <div className=\"message-username\">\n                                                <span className=\"username-text\">\n                                                    {username.toUpperCase()}\n                                                </span>\n                                            </div>\n                                        </div>\n                                    )}\n                                    <div className=\"message-content\">\n                                        <div className=\"message-bubble\">\n                                            {message.content}\n                                        </div>\n                                        <div className=\"message-time\">\n                                            {formatMessageTime(message.created_at)}\n                                        </div>\n                                    </div>\n                                </div>\n                            );\n                        })}\n                    </div>\n                ))}\n                <div ref={messagesEndRef} />\n            </React.Fragment>\n        ));\n    };\n\n    const renderConversationsList = () => (\n        <div className=\"conversations-list\">\n            {conversations.map((conv) => (\n                <div\n                    key={conv.user_id}\n                    className={`conversation-item ${selectedConversation?.user_id === conv.user_id ? 'active' : ''} ${!conv.is_read ? 'unread' : ''}`}\n                    onClick={() => handleConversationSelect(conv)}\n                >\n                    <div className=\"avatar\">\n                        {conv.username[0].toUpperCase()}\n                    </div>\n                    <div className=\"conversation-info\">\n                        <div className=\"conversation-name\">\n                            {conv.username}\n                            {!conv.is_read && <span className=\"unread-indicator\" />}\n                        </div>\n                        <div className=\"conversation-preview\">\n                            {conv.last_message}\n                        </div>\n                        <div className=\"conversation-time\">\n                            {formatMessageTime(conv.last_message_time)}\n                        </div>\n                    </div>\n                </div>\n            ))}\n        </div>\n    );\n\n    return (\n        <div className=\"messages-container\">\n            <div className={`messages-sidebar ${!selectedConversation ? 'active' : ''}`}>\n                <div className=\"messages-header\">\n                    <h2>Messages</h2>\n                </div>\n                <div className=\"messages-tabs\">\n                    <div \n                        className={`tab ${activeTab === 'inbox' ? 'active' : ''}`}\n                        onClick={() => setActiveTab('inbox')}\n                    >\n                        <FaInbox /> Inbox\n                    </div>\n                    <div \n                        className={`tab ${activeTab === 'sent' ? 'active' : ''}`}\n                        onClick={() => setActiveTab('sent')}\n                    >\n                        <FaSent /> Sent\n                    </div>\n                </div>\n                <div className=\"conversations-list\">\n                    {renderConversationsList()}\n                </div>\n            </div>\n\n            <div className=\"chat-area\">\n                {selectedConversation ? (\n                    <>\n                        <div className=\"chat-header\">\n                            <button \n                                className=\"back-button\"\n                                onClick={() => setSelectedConversation(null)}\n                            >\n                                <FaArrowLeft />\n                            </button>\n                            <div className=\"chat-title\">\n                                <div className=\"avatar\">\n                                    {selectedConversation.username[0].toUpperCase()}\n                                </div>\n                                <h3>Chat with {selectedConversation.username.charAt(0).toUpperCase() + selectedConversation.username.slice(1).toLowerCase()}</h3>\n                            </div>\n                            <button \n                                className=\"delete-button\"\n                                onClick={() => setShowDeleteConfirm(true)}\n                            >\n                                <FaTimes />\n                            </button>\n                        </div>\n\n                        {showDeleteConfirm && (\n                            <div className=\"delete-confirm\">\n                                <p>Are you sure you want to delete this conversation?</p>\n                                <div className=\"delete-actions\">\n                                    <button onClick={handleDeleteConversation}>Yes, Delete</button>\n                                    <button onClick={() => setShowDeleteConfirm(false)}>Cancel</button>\n                                </div>\n                            </div>\n                        )}\n\n                        <div className=\"messages-content\">\n                            {error && <div className=\"error-message\">{error}</div>}\n                            {renderMessages()}\n                            <div ref={messagesEndRef} />\n                        </div>\n\n                        <form onSubmit={handleSendMessage} className=\"message-input-container\">\n                            <textarea\n                                className=\"message-input\"\n                                value={newMessage}\n                                onChange={(e) => setNewMessage(e.target.value)}\n                                placeholder=\"Type a message...\"\n                                onKeyDown={(e) => {\n                                    if (e.key === 'Enter' && !e.shiftKey) {\n                                        e.preventDefault();\n                                        handleSendMessage(e);\n                                    }\n                                }}\n                            />\n                            <button \n                                type=\"submit\" \n                                className=\"send-button\"\n                                disabled={isLoading || !newMessage.trim()}\n                            >\n                                <FaPaperPlane />\n                            </button>\n                        </form>\n                    </>\n                ) : (\n                    <div className=\"no-conversation\">\n                        <FaComments />\n                        <p>Select a conversation to start messaging</p>\n                    </div>\n                )}\n            </div>\n        </div>\n    );\n};\n\nexport default Messages;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,SAASC,YAAY,EAAEC,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEH,YAAY,IAAII,MAAM,EAAEC,UAAU,QAAQ,gBAAgB;AAChH,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAMgC,cAAc,GAAG9B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM+B,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EACpD,MAAMC,eAAe,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;EAExDlC,SAAS,CAAC,MAAM;IACZoC,kBAAkB,CAAC,CAAC;IACpB,MAAMC,QAAQ,GAAGC,WAAW,CAACF,kBAAkB,EAAE,KAAK,CAAC;IACvD,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACxC,CAAC,EAAE,CAACtB,SAAS,CAAC,CAAC;EAEff,SAAS,CAAC,MAAM;IACZ,IAAImB,oBAAoB,EAAE;MACtBqB,aAAa,CAACrB,oBAAoB,CAACsB,OAAO,CAAC;MAC3CC,kBAAkB,CAACvB,oBAAoB,CAACsB,OAAO,CAAC;MAChD,MAAMJ,QAAQ,GAAGC,WAAW,CAAC,MAAME,aAAa,CAACrB,oBAAoB,CAACsB,OAAO,CAAC,EAAE,IAAI,CAAC;MACrF,OAAO,MAAMF,aAAa,CAACF,QAAQ,CAAC;IACxC;EACJ,CAAC,EAAE,CAAClB,oBAAoB,CAAC,CAAC;EAE1BnB,SAAS,CAAC,MAAM;IACZ,IAAI+B,cAAc,CAACY,OAAO,EAAE;MACxBZ,cAAc,CAACY,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IACjE;EACJ,CAAC,EAAE,CAACxB,QAAQ,CAAC,CAAC;EAEd,MAAMyB,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAM7C,KAAK,CAAC8C,IAAI,CAAC,cAAc,EAAE;QAC9CC,MAAM,EAAE,qBAAqB;QAC7BR,OAAO,EAAET,aAAa;QACtBkB,aAAa,EAAE/B,oBAAoB,CAACsB;MACxC,CAAC,CAAC;MAEF,IAAIM,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvBhC,uBAAuB,CAAC,IAAI,CAAC;QAC7BgB,kBAAkB,CAAC,CAAC;QACpBN,oBAAoB,CAAC,KAAK,CAAC;MAC/B;IACJ,CAAC,CAAC,OAAOL,KAAK,EAAE;MACZ4B,OAAO,CAAC5B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACxD;EACJ,CAAC;EAED,MAAMW,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACA,MAAMW,QAAQ,GAAG,MAAM7C,KAAK,CAACoD,GAAG,CAAC,cAAc,EAAE;QAC7CC,MAAM,EAAE;UACJd,OAAO,EAAET,aAAa;UACtBwB,IAAI,EAAEzC;QACV;MACJ,CAAC,CAAC;MACF,IAAIgC,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvBlC,gBAAgB,CAAC6B,QAAQ,CAACI,IAAI,CAAClC,aAAa,IAAI,EAAE,CAAC;MACvD;IACJ,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACZ4B,OAAO,CAAC5B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACzD;EACJ,CAAC;EAED,MAAMe,aAAa,GAAG,MAAOiB,WAAW,IAAK;IACzC,IAAI;MACA,MAAMV,QAAQ,GAAG,MAAM7C,KAAK,CAACoD,GAAG,CAAC,cAAc,EAAE;QAC7CC,MAAM,EAAE;UACJd,OAAO,EAAET,aAAa;UACtBkB,aAAa,EAAEO,WAAW;UAC1BD,IAAI,EAAE;QACV;MACJ,CAAC,CAAC;MACF,IAAIT,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvB9B,WAAW,CAACyB,QAAQ,CAACI,IAAI,CAAC9B,QAAQ,IAAI,EAAE,CAAC;MAC7C;IACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;MACZ4B,OAAO,CAAC5B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD;EACJ,CAAC;EAED,MAAMiB,kBAAkB,GAAG,MAAOe,WAAW,IAAK;IAC9C,IAAI;MACA,MAAMvD,KAAK,CAAC8C,IAAI,CAAC,cAAc,EAAE;QAC7BC,MAAM,EAAE,WAAW;QACnBS,YAAY,EAAE1B,aAAa;QAC3B2B,SAAS,EAAEF;MACf,CAAC,CAAC;IACN,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACZ4B,OAAO,CAAC5B,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IAC3D;EACJ,CAAC;EAED,MAAMmC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;IACnCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACvC,UAAU,CAACwC,IAAI,CAAC,CAAC,IAAI,CAAC5C,oBAAoB,EAAE;IAEjDS,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACA,MAAMmB,QAAQ,GAAG,MAAM7C,KAAK,CAAC8C,IAAI,CAAC,cAAc,EAAE;QAC9CW,SAAS,EAAE3B,aAAa;QACxB0B,YAAY,EAAEvC,oBAAoB,CAACsB,OAAO;QAC1CuB,OAAO,EAAEzC,UAAU,CAACwC,IAAI,CAAC;MAC7B,CAAC,CAAC;MAEF,IAAIhB,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvB5B,aAAa,CAAC,EAAE,CAAC;QACjBgB,aAAa,CAACrB,oBAAoB,CAACsB,OAAO,CAAC;MAC/C,CAAC,MAAM;QACHf,QAAQ,CAACqB,QAAQ,CAACI,IAAI,CAACc,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IACJ,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACZ4B,OAAO,CAAC5B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,QAAQ,CAAC,2CAA2C,CAAC;IACzD,CAAC,SAAS;MACNE,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAMsC,wBAAwB,GAAIC,IAAI,IAAK;IACvC/C,uBAAuB,CAAC+C,IAAI,CAAC;IAC7BzB,kBAAkB,CAACyB,IAAI,CAAC1B,OAAO,CAAC;EACpC,CAAC;EAED,MAAM2B,iBAAiB,GAAIC,IAAI,IAAK;IAChC,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;IAClC,MAAMG,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;IACxB,MAAME,SAAS,GAAG,IAAIF,IAAI,CAACC,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1C,IAAIL,WAAW,CAACM,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,EAAE;MACrD,OAAO,OAAO;IAClB,CAAC,MAAM,IAAIN,WAAW,CAACM,YAAY,CAAC,CAAC,KAAKH,SAAS,CAACG,YAAY,CAAC,CAAC,EAAE;MAChE,OAAO,WAAW;IACtB,CAAC,MAAM;MACH,OAAON,WAAW,CAACO,kBAAkB,CAAC,CAAC;IAC3C;EACJ,CAAC;EAED,MAAMC,iBAAiB,GAAIT,IAAI,IAAK;IAChC,OAAO,IAAIE,IAAI,CAACF,IAAI,CAAC,CAACU,kBAAkB,CAAC,EAAE,EAAE;MACzCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,0BAA0B,GAAI9D,QAAQ,IAAK;IAC7C,MAAM+D,MAAM,GAAG,CAAC,CAAC;IACjB,IAAIC,WAAW,GAAG,IAAI;IACtB,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAIC,YAAY,GAAG,IAAI;IAEvBlE,QAAQ,CAACmE,OAAO,CAAEvB,OAAO,IAAK;MAC1B,MAAMK,WAAW,GAAGF,iBAAiB,CAACH,OAAO,CAACwB,UAAU,CAAC;;MAEzD;MACA,IAAInB,WAAW,KAAKe,WAAW,IAAIpB,OAAO,CAACN,SAAS,KAAK4B,YAAY,EAAE;QACnE,IAAID,YAAY,CAACI,MAAM,GAAG,CAAC,EAAE;UACzB,IAAI,CAACN,MAAM,CAACC,WAAW,CAAC,EAAE;YACtBD,MAAM,CAACC,WAAW,CAAC,GAAG,EAAE;UAC5B;UACAD,MAAM,CAACC,WAAW,CAAC,CAACM,IAAI,CAACL,YAAY,CAAC;QAC1C;QACAA,YAAY,GAAG,CAACrB,OAAO,CAAC;QACxBoB,WAAW,GAAGf,WAAW;MAC7B,CAAC,MAAM;QACHgB,YAAY,CAACK,IAAI,CAAC1B,OAAO,CAAC;MAC9B;MAEAsB,YAAY,GAAGtB,OAAO,CAACN,SAAS;IACpC,CAAC,CAAC;;IAEF;IACA,IAAI2B,YAAY,CAACI,MAAM,GAAG,CAAC,IAAIL,WAAW,EAAE;MACxC,IAAI,CAACD,MAAM,CAACC,WAAW,CAAC,EAAE;QACtBD,MAAM,CAACC,WAAW,CAAC,GAAG,EAAE;MAC5B;MACAD,MAAM,CAACC,WAAW,CAAC,CAACM,IAAI,CAACL,YAAY,CAAC;IAC1C;IAEA,OAAOF,MAAM;EACjB,CAAC;EAED,MAAMQ,cAAc,GAAGA,CAAA,KAAM;IACzB,MAAMC,eAAe,GAAGV,0BAA0B,CAAC9D,QAAQ,CAAC;IAE5D,OAAOyE,MAAM,CAACC,OAAO,CAACF,eAAe,CAAC,CAACG,GAAG,CAAC,CAAC,CAAC3B,IAAI,EAAE4B,UAAU,CAAC,kBAC1DvF,OAAA,CAACZ,KAAK,CAACa,QAAQ;MAAAuF,QAAA,gBACXxF,OAAA;QAAKyF,SAAS,EAAC,sBAAsB;QAAAD,QAAA,eACjCxF,OAAA;UAAMyF,SAAS,EAAC,cAAc;UAAAD,QAAA,EAAE7B;QAAI;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,EACLN,UAAU,CAACD,GAAG,CAAC,CAACQ,KAAK,EAAEC,UAAU,kBAC9B/F,OAAA;QAAmCyF,SAAS,EAAC,eAAe;QAAAD,QAAA,EACvDM,KAAK,CAACR,GAAG,CAAC,CAAC/B,OAAO,EAAEyC,YAAY,KAAK;UAClC,MAAMC,MAAM,GAAG1C,OAAO,CAACN,SAAS,KAAK3B,aAAa;UAClD,MAAM4E,QAAQ,GAAGD,MAAM,GAAGxE,eAAe,GAAG8B,OAAO,CAAC4C,eAAe;UACnE,MAAMC,YAAY,GAAGJ,YAAY,KAAK,CAAC;UACvC,MAAMK,QAAQ,GAAG,CAAC9C,OAAO,CAAC+C,OAAO,IAAI,CAACL,MAAM;UAE5C,oBACIjG,OAAA;YAEIyF,SAAS,EAAE,WAAWlC,OAAO,CAACgD,YAAY,IAAIF,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAAb,QAAA,GAExEY,YAAY,iBACTpG,OAAA;cAAKyF,SAAS,EAAC,mBAAmB;cAAAD,QAAA,eAC9BxF,OAAA;gBAAKyF,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,eAC7BxF,OAAA;kBAAMyF,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAC1BU,QAAQ,CAACM,WAAW,CAAC;gBAAC;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACR,eACD7F,OAAA;cAAKyF,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC5BxF,OAAA;gBAAKyF,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,EAC1BjC,OAAO,CAACD;cAAO;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACN7F,OAAA;gBAAKyF,SAAS,EAAC,cAAc;gBAAAD,QAAA,EACxBpB,iBAAiB,CAACb,OAAO,CAACwB,UAAU;cAAC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA,GAnBDtC,OAAO,CAACkD,EAAE,IAAIT,YAAY;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoB9B,CAAC;QAEd,CAAC;MAAC,GA/BI,GAAGlC,IAAI,IAAIoC,UAAU,EAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgC5B,CACR,CAAC,eACF7F,OAAA;QAAK0G,GAAG,EAAErF;MAAe;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA,GAvCXlC,IAAI;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAwCT,CACnB,CAAC;EACN,CAAC;EAED,MAAMc,uBAAuB,GAAGA,CAAA,kBAC5B3G,OAAA;IAAKyF,SAAS,EAAC,oBAAoB;IAAAD,QAAA,EAC9BjF,aAAa,CAAC+E,GAAG,CAAE7B,IAAI,iBACpBzD,OAAA;MAEIyF,SAAS,EAAE,qBAAqB,CAAAhF,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEsB,OAAO,MAAK0B,IAAI,CAAC1B,OAAO,GAAG,QAAQ,GAAG,EAAE,IAAI,CAAC0B,IAAI,CAAC6C,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;MAClIM,OAAO,EAAEA,CAAA,KAAMpD,wBAAwB,CAACC,IAAI,CAAE;MAAA+B,QAAA,gBAE9CxF,OAAA;QAAKyF,SAAS,EAAC,QAAQ;QAAAD,QAAA,EAClB/B,IAAI,CAACyC,QAAQ,CAAC,CAAC,CAAC,CAACM,WAAW,CAAC;MAAC;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACN7F,OAAA;QAAKyF,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAC9BxF,OAAA;UAAKyF,SAAS,EAAC,mBAAmB;UAAAD,QAAA,GAC7B/B,IAAI,CAACyC,QAAQ,EACb,CAACzC,IAAI,CAAC6C,OAAO,iBAAItG,OAAA;YAAMyF,SAAS,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACN7F,OAAA;UAAKyF,SAAS,EAAC,sBAAsB;UAAAD,QAAA,EAChC/B,IAAI,CAACoD;QAAY;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACN7F,OAAA;UAAKyF,SAAS,EAAC,mBAAmB;UAAAD,QAAA,EAC7BpB,iBAAiB,CAACX,IAAI,CAACqD,iBAAiB;QAAC;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,GAlBDpC,IAAI,CAAC1B,OAAO;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAmBhB,CACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CACR;EAED,oBACI7F,OAAA;IAAKyF,SAAS,EAAC,oBAAoB;IAAAD,QAAA,gBAC/BxF,OAAA;MAAKyF,SAAS,EAAE,oBAAoB,CAAChF,oBAAoB,GAAG,QAAQ,GAAG,EAAE,EAAG;MAAA+E,QAAA,gBACxExF,OAAA;QAAKyF,SAAS,EAAC,iBAAiB;QAAAD,QAAA,eAC5BxF,OAAA;UAAAwF,QAAA,EAAI;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACN7F,OAAA;QAAKyF,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAC1BxF,OAAA;UACIyF,SAAS,EAAE,OAAOpF,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC1DuG,OAAO,EAAEA,CAAA,KAAMtG,YAAY,CAAC,OAAO,CAAE;UAAAkF,QAAA,gBAErCxF,OAAA,CAACL,OAAO;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UACf;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN7F,OAAA;UACIyF,SAAS,EAAE,OAAOpF,SAAS,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;UACzDuG,OAAO,EAAEA,CAAA,KAAMtG,YAAY,CAAC,MAAM,CAAE;UAAAkF,QAAA,gBAEpCxF,OAAA,CAACH,MAAM;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SACd;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN7F,OAAA;QAAKyF,SAAS,EAAC,oBAAoB;QAAAD,QAAA,EAC9BmB,uBAAuB,CAAC;MAAC;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN7F,OAAA;MAAKyF,SAAS,EAAC,WAAW;MAAAD,QAAA,EACrB/E,oBAAoB,gBACjBT,OAAA,CAAAE,SAAA;QAAAsF,QAAA,gBACIxF,OAAA;UAAKyF,SAAS,EAAC,aAAa;UAAAD,QAAA,gBACxBxF,OAAA;YACIyF,SAAS,EAAC,aAAa;YACvBmB,OAAO,EAAEA,CAAA,KAAMlG,uBAAuB,CAAC,IAAI,CAAE;YAAA8E,QAAA,eAE7CxF,OAAA,CAACJ,WAAW;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACT7F,OAAA;YAAKyF,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACvBxF,OAAA;cAAKyF,SAAS,EAAC,QAAQ;cAAAD,QAAA,EAClB/E,oBAAoB,CAACyF,QAAQ,CAAC,CAAC,CAAC,CAACM,WAAW,CAAC;YAAC;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACN7F,OAAA;cAAAwF,QAAA,GAAI,YAAU,EAAC/E,oBAAoB,CAACyF,QAAQ,CAACa,MAAM,CAAC,CAAC,CAAC,CAACP,WAAW,CAAC,CAAC,GAAG/F,oBAAoB,CAACyF,QAAQ,CAACc,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAAA;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChI,CAAC,eACN7F,OAAA;YACIyF,SAAS,EAAC,eAAe;YACzBmB,OAAO,EAAEA,CAAA,KAAMxF,oBAAoB,CAAC,IAAI,CAAE;YAAAoE,QAAA,eAE1CxF,OAAA,CAACN,OAAO;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAEL1E,iBAAiB,iBACdnB,OAAA;UAAKyF,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC3BxF,OAAA;YAAAwF,QAAA,EAAG;UAAkD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzD7F,OAAA;YAAKyF,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3BxF,OAAA;cAAQ4G,OAAO,EAAExE,wBAAyB;cAAAoD,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/D7F,OAAA;cAAQ4G,OAAO,EAAEA,CAAA,KAAMxF,oBAAoB,CAAC,KAAK,CAAE;cAAAoE,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,eAED7F,OAAA;UAAKyF,SAAS,EAAC,kBAAkB;UAAAD,QAAA,GAC5BzE,KAAK,iBAAIf,OAAA;YAAKyF,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAEzE;UAAK;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACrDX,cAAc,CAAC,CAAC,eACjBlF,OAAA;YAAK0G,GAAG,EAAErF;UAAe;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEN7F,OAAA;UAAMkH,QAAQ,EAAEhE,iBAAkB;UAACuC,SAAS,EAAC,yBAAyB;UAAAD,QAAA,gBAClExF,OAAA;YACIyF,SAAS,EAAC,eAAe;YACzB0B,KAAK,EAAEtG,UAAW;YAClBuG,QAAQ,EAAGjE,CAAC,IAAKrC,aAAa,CAACqC,CAAC,CAACkE,MAAM,CAACF,KAAK,CAAE;YAC/CG,WAAW,EAAC,mBAAmB;YAC/BC,SAAS,EAAGpE,CAAC,IAAK;cACd,IAAIA,CAAC,CAACqE,GAAG,KAAK,OAAO,IAAI,CAACrE,CAAC,CAACsE,QAAQ,EAAE;gBAClCtE,CAAC,CAACC,cAAc,CAAC,CAAC;gBAClBF,iBAAiB,CAACC,CAAC,CAAC;cACxB;YACJ;UAAE;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACF7F,OAAA;YACI8C,IAAI,EAAC,QAAQ;YACb2C,SAAS,EAAC,aAAa;YACvBiC,QAAQ,EAAEzG,SAAS,IAAI,CAACJ,UAAU,CAACwC,IAAI,CAAC,CAAE;YAAAmC,QAAA,eAE1CxF,OAAA,CAACP,YAAY;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA,eACT,CAAC,gBAEH7F,OAAA;QAAKyF,SAAS,EAAC,iBAAiB;QAAAD,QAAA,gBAC5BxF,OAAA,CAACF,UAAU;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACd7F,OAAA;UAAAwF,QAAA,EAAG;QAAwC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACzF,EAAA,CAxWID,QAAQ;AAAAwH,EAAA,GAARxH,QAAQ;AA0Wd,eAAeA,QAAQ;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}