{"ast": null, "code": "import axios from 'axios';\nimport { API_BASE_URL } from '../config';\n\n// Create axios instance with consistent configuration\nconst axiosInstance = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  // 30 second timeout\n  withCredentials: true,\n  headers: {\n    'Content-Type': 'application/json',\n    'X-Requested-With': 'XMLHttpRequest',\n    'Accept': 'application/json'\n  }\n});\n\n// Request interceptor for authentication and URL normalization\naxiosInstance.interceptors.request.use(config => {\n  // Normalize URL to prevent duplicate handlers\n  if (config.url && config.url.includes('/handlers/handlers/')) {\n    config.url = config.url.replace('/handlers/handlers/', '/handlers/');\n  }\n\n  // Ensure handlers prefix for API endpoints - but only if baseURL doesn't already include backend path\n  if (config.url && !config.url.startsWith('/handlers/') && !config.url.startsWith('http') && !config.baseURL.includes('/backend')) {\n    config.url = `/handlers/${config.url}`;\n  }\n\n  // Add authentication token\n  if (!config.headers.Authorization) {\n    const token = localStorage.getItem('userToken');\n    const adminId = localStorage.getItem('adminId');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    } else if (adminId) {\n      config.headers['X-Admin-ID'] = adminId;\n    }\n  }\n\n  // Log request in development\n  if (process.env.NODE_ENV === 'development') {\n    var _config$method;\n    console.log(`🔄 ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.baseURL}${config.url}`);\n  }\n  return config;\n}, error => {\n  console.error('❌ Request interceptor error:', error);\n  return Promise.reject(error);\n});\n\n// Response interceptor for consistent error handling\naxiosInstance.interceptors.response.use(response => {\n  // Log successful responses in development\n  if (process.env.NODE_ENV === 'development') {\n    var _response$config$meth;\n    console.log(`✅ ${(_response$config$meth = response.config.method) === null || _response$config$meth === void 0 ? void 0 : _response$config$meth.toUpperCase()} ${response.config.url} - ${response.status}`);\n  }\n  return response;\n}, error => {\n  var _config$method2, _config$url;\n  const {\n    response,\n    config\n  } = error;\n\n  // Log error details\n  console.error(`❌ ${config === null || config === void 0 ? void 0 : (_config$method2 = config.method) === null || _config$method2 === void 0 ? void 0 : _config$method2.toUpperCase()} ${config === null || config === void 0 ? void 0 : config.url} - ${(response === null || response === void 0 ? void 0 : response.status) || 'Network Error'}`);\n\n  // Handle authentication errors\n  if ((response === null || response === void 0 ? void 0 : response.status) === 401 && !(config !== null && config !== void 0 && (_config$url = config.url) !== null && _config$url !== void 0 && _config$url.includes('join_league.php'))) {\n    handleAuthError();\n  }\n\n  // Handle network errors\n  if (!response) {\n    error.message = 'Network error - please check your connection';\n  }\n  return Promise.reject(error);\n});\n\n// Authentication error handler\nconst handleAuthError = () => {\n  const adminId = localStorage.getItem('adminId');\n  const userToken = localStorage.getItem('userToken');\n  if (adminId) {\n    // Admin session expired\n    localStorage.removeItem('adminId');\n    localStorage.removeItem('adminUsername');\n    localStorage.removeItem('adminRole');\n    window.location.href = '/admin/login';\n  } else if (userToken) {\n    // User session expired\n    localStorage.removeItem('userToken');\n    localStorage.removeItem('userId');\n    window.location.href = '/login';\n  }\n};\nexport default axiosInstance;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "axiosInstance", "create", "baseURL", "timeout", "withCredentials", "headers", "interceptors", "request", "use", "config", "url", "includes", "replace", "startsWith", "Authorization", "token", "localStorage", "getItem", "adminId", "process", "env", "NODE_ENV", "_config$method", "console", "log", "method", "toUpperCase", "error", "Promise", "reject", "response", "_response$config$meth", "status", "_config$method2", "_config$url", "handleAuthError", "message", "userToken", "removeItem", "window", "location", "href"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/utils/axiosConfig.js"], "sourcesContent": ["import axios from 'axios';\nimport { API_BASE_URL } from '../config';\n\n// Create axios instance with consistent configuration\nconst axiosInstance = axios.create({\n    baseURL: API_BASE_URL,\n    timeout: 30000, // 30 second timeout\n    withCredentials: true,\n    headers: {\n        'Content-Type': 'application/json',\n        'X-Requested-With': 'XMLHttpRequest',\n        'Accept': 'application/json'\n    }\n});\n\n// Request interceptor for authentication and URL normalization\naxiosInstance.interceptors.request.use(\n    (config) => {\n        // Normalize URL to prevent duplicate handlers\n        if (config.url && config.url.includes('/handlers/handlers/')) {\n            config.url = config.url.replace('/handlers/handlers/', '/handlers/');\n        }\n\n        // Ensure handlers prefix for API endpoints - but only if baseURL doesn't already include backend path\n        if (config.url && !config.url.startsWith('/handlers/') && !config.url.startsWith('http') && !config.baseURL.includes('/backend')) {\n            config.url = `/handlers/${config.url}`;\n        }\n\n        // Add authentication token\n        if (!config.headers.Authorization) {\n            const token = localStorage.getItem('userToken');\n            const adminId = localStorage.getItem('adminId');\n\n            if (token) {\n                config.headers.Authorization = `Bearer ${token}`;\n            } else if (adminId) {\n                config.headers['X-Admin-ID'] = adminId;\n            }\n        }\n\n        // Log request in development\n        if (process.env.NODE_ENV === 'development') {\n            console.log(`🔄 ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`);\n        }\n\n        return config;\n    },\n    (error) => {\n        console.error('❌ Request interceptor error:', error);\n        return Promise.reject(error);\n    }\n);\n\n// Response interceptor for consistent error handling\naxiosInstance.interceptors.response.use(\n    (response) => {\n        // Log successful responses in development\n        if (process.env.NODE_ENV === 'development') {\n            console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`);\n        }\n        return response;\n    },\n    (error) => {\n        const { response, config } = error;\n\n        // Log error details\n        console.error(`❌ ${config?.method?.toUpperCase()} ${config?.url} - ${response?.status || 'Network Error'}`);\n\n        // Handle authentication errors\n        if (response?.status === 401 && !config?.url?.includes('join_league.php')) {\n            handleAuthError();\n        }\n\n        // Handle network errors\n        if (!response) {\n            error.message = 'Network error - please check your connection';\n        }\n\n        return Promise.reject(error);\n    }\n);\n\n// Authentication error handler\nconst handleAuthError = () => {\n    const adminId = localStorage.getItem('adminId');\n    const userToken = localStorage.getItem('userToken');\n\n    if (adminId) {\n        // Admin session expired\n        localStorage.removeItem('adminId');\n        localStorage.removeItem('adminUsername');\n        localStorage.removeItem('adminRole');\n        window.location.href = '/admin/login';\n    } else if (userToken) {\n        // User session expired\n        localStorage.removeItem('userToken');\n        localStorage.removeItem('userId');\n        window.location.href = '/login';\n    }\n};\n\nexport default axiosInstance;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,WAAW;;AAExC;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EAC/BC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE,KAAK;EAAE;EAChBC,eAAe,EAAE,IAAI;EACrBC,OAAO,EAAE;IACL,cAAc,EAAE,kBAAkB;IAClC,kBAAkB,EAAE,gBAAgB;IACpC,QAAQ,EAAE;EACd;AACJ,CAAC,CAAC;;AAEF;AACAL,aAAa,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACjCC,MAAM,IAAK;EACR;EACA,IAAIA,MAAM,CAACC,GAAG,IAAID,MAAM,CAACC,GAAG,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EAAE;IAC1DF,MAAM,CAACC,GAAG,GAAGD,MAAM,CAACC,GAAG,CAACE,OAAO,CAAC,qBAAqB,EAAE,YAAY,CAAC;EACxE;;EAEA;EACA,IAAIH,MAAM,CAACC,GAAG,IAAI,CAACD,MAAM,CAACC,GAAG,CAACG,UAAU,CAAC,YAAY,CAAC,IAAI,CAACJ,MAAM,CAACC,GAAG,CAACG,UAAU,CAAC,MAAM,CAAC,IAAI,CAACJ,MAAM,CAACP,OAAO,CAACS,QAAQ,CAAC,UAAU,CAAC,EAAE;IAC9HF,MAAM,CAACC,GAAG,GAAG,aAAaD,MAAM,CAACC,GAAG,EAAE;EAC1C;;EAEA;EACA,IAAI,CAACD,MAAM,CAACJ,OAAO,CAACS,aAAa,EAAE;IAC/B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAE/C,IAAIF,KAAK,EAAE;MACPN,MAAM,CAACJ,OAAO,CAACS,aAAa,GAAG,UAAUC,KAAK,EAAE;IACpD,CAAC,MAAM,IAAIG,OAAO,EAAE;MAChBT,MAAM,CAACJ,OAAO,CAAC,YAAY,CAAC,GAAGa,OAAO;IAC1C;EACJ;;EAEA;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAAA,IAAAC,cAAA;IACxCC,OAAO,CAACC,GAAG,CAAC,OAAAF,cAAA,GAAMb,MAAM,CAACgB,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,IAAIjB,MAAM,CAACP,OAAO,GAAGO,MAAM,CAACC,GAAG,EAAE,CAAC;EACpF;EAEA,OAAOD,MAAM;AACjB,CAAC,EACAkB,KAAK,IAAK;EACPJ,OAAO,CAACI,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;EACpD,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CACJ,CAAC;;AAED;AACA3B,aAAa,CAACM,YAAY,CAACwB,QAAQ,CAACtB,GAAG,CAClCsB,QAAQ,IAAK;EACV;EACA,IAAIX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAAA,IAAAU,qBAAA;IACxCR,OAAO,CAACC,GAAG,CAAC,MAAAO,qBAAA,GAAKD,QAAQ,CAACrB,MAAM,CAACgB,MAAM,cAAAM,qBAAA,uBAAtBA,qBAAA,CAAwBL,WAAW,CAAC,CAAC,IAAII,QAAQ,CAACrB,MAAM,CAACC,GAAG,MAAMoB,QAAQ,CAACE,MAAM,EAAE,CAAC;EACzG;EACA,OAAOF,QAAQ;AACnB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAM,eAAA,EAAAC,WAAA;EACP,MAAM;IAAEJ,QAAQ;IAAErB;EAAO,CAAC,GAAGkB,KAAK;;EAElC;EACAJ,OAAO,CAACI,KAAK,CAAC,KAAKlB,MAAM,aAANA,MAAM,wBAAAwB,eAAA,GAANxB,MAAM,CAAEgB,MAAM,cAAAQ,eAAA,uBAAdA,eAAA,CAAgBP,WAAW,CAAC,CAAC,IAAIjB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,GAAG,MAAM,CAAAoB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,KAAI,eAAe,EAAE,CAAC;;EAE3G;EACA,IAAI,CAAAF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,IAAI,EAACvB,MAAM,aAANA,MAAM,gBAAAyB,WAAA,GAANzB,MAAM,CAAEC,GAAG,cAAAwB,WAAA,eAAXA,WAAA,CAAavB,QAAQ,CAAC,iBAAiB,CAAC,GAAE;IACvEwB,eAAe,CAAC,CAAC;EACrB;;EAEA;EACA,IAAI,CAACL,QAAQ,EAAE;IACXH,KAAK,CAACS,OAAO,GAAG,8CAA8C;EAClE;EAEA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CACJ,CAAC;;AAED;AACA,MAAMQ,eAAe,GAAGA,CAAA,KAAM;EAC1B,MAAMjB,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMoB,SAAS,GAAGrB,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAEnD,IAAIC,OAAO,EAAE;IACT;IACAF,YAAY,CAACsB,UAAU,CAAC,SAAS,CAAC;IAClCtB,YAAY,CAACsB,UAAU,CAAC,eAAe,CAAC;IACxCtB,YAAY,CAACsB,UAAU,CAAC,WAAW,CAAC;IACpCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;EACzC,CAAC,MAAM,IAAIJ,SAAS,EAAE;IAClB;IACArB,YAAY,CAACsB,UAAU,CAAC,WAAW,CAAC;IACpCtB,YAAY,CAACsB,UAAU,CAAC,QAAQ,CAAC;IACjCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACnC;AACJ,CAAC;AAED,eAAezC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}