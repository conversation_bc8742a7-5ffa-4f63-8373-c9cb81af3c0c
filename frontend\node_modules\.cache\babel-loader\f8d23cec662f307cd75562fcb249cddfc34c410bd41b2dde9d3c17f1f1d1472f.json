{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\LeagueHome.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport { API_BASE_URL } from '../config';\nimport '../styles/league-home.css';\nimport { FaCrown, FaStar, FaTrophy, FaMedal, FaInfoCircle, FaChartLine, FaHistory, FaAward, FaBolt, FaFire, FaUsers, FaCoins, FaRocket, FaChartBar } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LeagueHome = () => {\n  _s();\n  const navigate = useNavigate();\n  const [leagues, setLeagues] = useState([]);\n  const [userStats, setUserStats] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showSuccessMessage, setShowSuccessMessage] = useState('');\n  const [showWelcomeGuide, setShowWelcomeGuide] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentSeason, setCurrentSeason] = useState(null);\n  const [joinAmount, setJoinAmount] = useState('');\n  const [showJoinModal, setShowJoinModal] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [userBalance, setUserBalance] = useState(0);\n  const leaguesPerPage = 12;\n\n  // Add fetchUserBalance function\n  const fetchUserBalance = async () => {\n    try {\n      const response = await axios.get('get_user_balance.php');\n      if (response.data.status === 200) {\n        setUserBalance(response.data.data.balance);\n      }\n    } catch (error) {\n      console.error('Error fetching user balance:', error);\n      setError('Failed to fetch user balance');\n    }\n  };\n\n  // Fetch leagues and user stats\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n        setError('');\n        const response = await axios.get('league_home.php');\n        console.log('League response:', response.data); // Debug log\n\n        if (response.data.status === 200) {\n          const {\n            leagues,\n            user_stats,\n            current_season\n          } = response.data.data;\n          if (leagues && Array.isArray(leagues)) {\n            setLeagues(leagues);\n            console.log('Leagues loaded:', leagues.length); // Debug log\n          } else {\n            setLeagues([]);\n            console.log('No leagues found or invalid data'); // Debug log\n          }\n          setUserStats(user_stats || null);\n          setCurrentSeason(current_season || null);\n          if (!localStorage.getItem('leagueGuideShown')) {\n            setShowWelcomeGuide(true);\n            localStorage.setItem('leagueGuideShown', 'true');\n          }\n        } else {\n          setError(response.data.message || 'Failed to load leagues');\n        }\n      } catch (err) {\n        var _err$response;\n        console.error('Error fetching data:', err);\n        if (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status) === 401) {\n          navigate('/login');\n        } else {\n          setError('Failed to load leagues. Please try again.');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n    fetchUserBalance(); // Also fetch initial balance\n  }, [navigate]);\n\n  // Handle league join\n  const handleJoinLeague = async league => {\n    try {\n      // First check if user is already in any active league\n      const userId = localStorage.getItem('userId');\n      if (!userId) {\n        setError('Please log in to join a league');\n        navigate('/login');\n        return;\n      }\n      const response = await axios.get(`${API_BASE_URL}/handlers/user_leagues.php?user_id=${userId}`);\n      console.log('User leagues response:', response.data); // Debug log\n\n      if (response.data.status === 200) {\n        const activeLeagues = response.data.data.filter(l => l.status === 'active');\n        if (activeLeagues.length > 0) {\n          setError(`You are already a member of an active league: ${activeLeagues[0].name}. You can only join one league at a time.`);\n          // Close modal if it's open\n          setShowJoinModal(false);\n          setSelectedLeague(null);\n          // Clear the error after 5 seconds\n          setTimeout(() => {\n            setError('');\n          }, 5000);\n          return;\n        }\n      } else {\n        setError(response.data.message || 'Failed to verify league membership');\n        setShowJoinModal(false);\n        setSelectedLeague(null);\n        return;\n      }\n\n      // If we get here, user can join the league\n      setSelectedLeague(league);\n      setShowJoinModal(true);\n      setJoinAmount(league.min_bet_amount.toString());\n\n      // Refresh user balance before showing modal\n      await fetchUserBalance();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error checking user leagues:', error);\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || 'Failed to verify league membership status';\n      setError(`Error: ${errorMessage}`);\n      // Close modal if it's open\n      setShowJoinModal(false);\n      setSelectedLeague(null);\n      // Clear the error after 5 seconds\n      setTimeout(() => {\n        setError('');\n      }, 5000);\n    }\n  };\n  const confirmJoinLeague = async () => {\n    try {\n      setError('');\n      setShowSuccessMessage('');\n      const amount = parseFloat(joinAmount);\n      if (isNaN(amount)) {\n        setError('Please enter a valid amount');\n        return;\n      }\n      if (amount < selectedLeague.min_bet_amount || amount > selectedLeague.max_bet_amount) {\n        setError(`Amount must be between ${selectedLeague.min_bet_amount} and ${selectedLeague.max_bet_amount} FC`);\n        return;\n      }\n      if (amount > userBalance) {\n        setError(`Insufficient balance. Your current balance is ${userBalance} FC`);\n        return;\n      }\n      const userId = localStorage.getItem('userId');\n      if (!userId) {\n        setError('User ID not found. Please log in again.');\n        navigate('/login');\n        return;\n      }\n      const response = await axios.post(`${API_BASE_URL}/handlers/join_league.php`, {\n        league_id: selectedLeague.league_id,\n        user_id: userId,\n        amount: amount\n      });\n      if (response.data.status === 200) {\n        setLeagues(prevLeagues => prevLeagues.map(league => {\n          if (league.league_id === selectedLeague.league_id) {\n            return {\n              ...league,\n              ...response.data.data.league,\n              is_member: true\n            };\n          }\n          return league;\n        }));\n        setShowSuccessMessage('Successfully joined the league!');\n        // Close modal after success\n        setTimeout(() => {\n          setShowJoinModal(false);\n          setJoinAmount('');\n          setSelectedLeague(null);\n          setShowSuccessMessage('');\n          // Refresh user balance\n          fetchUserBalance();\n        }, 2000);\n      }\n    } catch (err) {\n      var _err$response2, _err$response2$data, _err$response3;\n      console.error('Error joining league:', err);\n      const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || 'Failed to join league';\n      setError(errorMessage);\n      // Close modal on error\n      setTimeout(() => {\n        setShowJoinModal(false);\n        setJoinAmount('');\n        setSelectedLeague(null);\n        setError('');\n      }, 3000);\n      if (((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401) {\n        navigate('/login');\n      }\n    }\n  };\n\n  // Filter and paginate leagues\n  const filteredLeagues = leagues.filter(league => league.name.toLowerCase().includes(searchTerm.toLowerCase()) || league.description && league.description.toLowerCase().includes(searchTerm.toLowerCase()));\n  const indexOfLastLeague = currentPage * leaguesPerPage;\n  const indexOfFirstLeague = indexOfLastLeague - leaguesPerPage;\n  const currentLeagues = filteredLeagues.slice(indexOfFirstLeague, indexOfLastLeague);\n  const totalPages = Math.ceil(filteredLeagues.length / leaguesPerPage);\n\n  // Navigation links\n  const navLinks = [{\n    to: '/user/leagues',\n    label: 'Leagues',\n    icon: /*#__PURE__*/_jsxDEV(FaTrophy, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 56\n    }, this)\n  }, {\n    to: '/user/leagues/leaderboard',\n    label: 'Leaderboard',\n    icon: /*#__PURE__*/_jsxDEV(FaChartLine, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 72\n    }, this)\n  }, {\n    to: '/user/leagues/achievements',\n    label: 'Achievements',\n    icon: /*#__PURE__*/_jsxDEV(FaAward, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 74\n    }, this)\n  }, {\n    to: '/user/leagues/seasons',\n    label: 'Season History',\n    icon: /*#__PURE__*/_jsxDEV(FaHistory, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 71\n    }, this)\n  }];\n\n  // Season Info Component\n  const SeasonInfo = ({\n    season\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"season-info\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"season-header\",\n      children: [/*#__PURE__*/_jsxDEV(FaChartLine, {\n        className: \"season-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"season-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: season.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"season-dates\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Started: \", new Date(season.start_date).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Ends: \", new Date(season.end_date).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 13\n    }, this), season.days_remaining > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"season-countdown\",\n      children: [/*#__PURE__*/_jsxDEV(FaRocket, {\n        className: \"countdown-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [season.days_remaining, \" days remaining\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 238,\n    columnNumber: 9\n  }, this);\n\n  // User Stats Bar Component\n  const UserStatsBar = ({\n    stats\n  }) => {\n    var _stats$total_points;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-stats-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-stats-bar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(FaTrophy, {\n            className: \"stat-icon\",\n            style: {\n              color: '#228B22'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Total Points\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: (stats === null || stats === void 0 ? void 0 : (_stats$total_points = stats.total_points) === null || _stats$total_points === void 0 ? void 0 : _stats$total_points.toLocaleString()) || '0'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(FaBolt, {\n            className: \"stat-icon\",\n            style: {\n              color: '#228B22'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Current Streak\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: (stats === null || stats === void 0 ? void 0 : stats.current_streak) || '0'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(FaCoins, {\n            className: \"stat-icon\",\n            style: {\n              color: '#228B22'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Balance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [parseFloat((stats === null || stats === void 0 ? void 0 : stats.balance) || 0).toLocaleString(), \" FC\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(FaChartBar, {\n            className: \"stat-icon\",\n            style: {\n              color: '#228B22'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Leagues Joined\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: (stats === null || stats === void 0 ? void 0 : stats.leagues_joined) || '0'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Progress to Next Level\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [50 - (stats === null || stats === void 0 ? void 0 : stats.points) % 50, \" points needed\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-bar\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-fill\",\n            style: {\n              width: `${Math.min((stats === null || stats === void 0 ? void 0 : stats.points) % 50 / 50 * 100, 100)}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 9\n    }, this);\n  };\n  const renderNavigation = () => /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"league-nav\",\n    children: navLinks.map(link => /*#__PURE__*/_jsxDEV(Link, {\n      to: link.to,\n      className: `nav-item ${window.location.pathname === link.to ? 'active' : ''}`,\n      children: [link.icon, link.label]\n    }, link.to, true, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 307,\n    columnNumber: 9\n  }, this);\n\n  // League Card Component\n  const LeagueCard = ({\n    league,\n    userStats,\n    onJoin,\n    navigate\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"league-list-item\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"league-main-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"league-icon\",\n        children: league.league_icon ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: league.league_icon,\n          alt: `${league.name} icon`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(FaTrophy, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"league-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"league-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"league-title\",\n            children: league.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"league-members\",\n            children: [/*#__PURE__*/_jsxDEV(FaUsers, {\n              className: \"info-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"info-value\",\n              children: [league.member_count, \" Members\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"league-description\",\n          children: league.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"league-betting\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"betting-limits\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"limit-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"limit-label\",\n            children: \"Min\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"limit-value\",\n            children: [league.min_bet_formatted, \" FC\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"limit-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"limit-label\",\n            children: \"Max\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"limit-value\",\n            children: [league.max_bet_formatted, \" FC\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"league-actions\",\n      children: league.is_member ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-badge active\",\n          style: {\n            backgroundColor: '#228B22'\n          },\n          children: \"Active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"view-league-btn\",\n          onClick: () => navigate(`/user/leagues/${league.league_id}`),\n          children: [/*#__PURE__*/_jsxDEV(FaChartLine, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 29\n          }, this), \" View\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true) : league.has_active_membership ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-badge inactive\",\n        style: {\n          backgroundColor: '#228B22'\n        },\n        children: \"In League\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onJoin(league),\n        className: \"join-league-btn\",\n        disabled: (userStats === null || userStats === void 0 ? void 0 : userStats.balance) < league.min_bet_amount,\n        children: (userStats === null || userStats === void 0 ? void 0 : userStats.balance) < league.min_bet_amount ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(FaCoins, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 33\n          }, this), \" Low Balance\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(FaRocket, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 33\n          }, this), \" Join\"]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 323,\n    columnNumber: 9\n  }, this);\n\n  // Empty League List Component\n  const EmptyLeagueList = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"empty-leagues\",\n    children: [/*#__PURE__*/_jsxDEV(FaTrophy, {\n      className: \"empty-state-icon\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"No leagues available at the moment\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"Check back soon for new leagues!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 393,\n    columnNumber: 9\n  }, this);\n\n  // Welcome Guide Component\n  const WelcomeGuide = ({\n    onClose\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"welcome-guide\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"guide-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: [/*#__PURE__*/_jsxDEV(FaCrown, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 21\n        }, this), \" Welcome to 247 League!\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"guide-features\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-item\",\n          children: [/*#__PURE__*/_jsxDEV(FaTrophy, {\n            className: \"feature-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Compete in Leagues\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Join leagues that match your betting range and compete with others\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-item\",\n          children: [/*#__PURE__*/_jsxDEV(FaChartLine, {\n            className: \"feature-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Earn Points\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Win: 3 points | Draw: 1 point | Climb the leaderboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-item\",\n          children: [/*#__PURE__*/_jsxDEV(FaMedal, {\n            className: \"feature-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Win Rewards\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Unlock badges and earn rewards as you progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-item\",\n          children: [/*#__PURE__*/_jsxDEV(FaCoins, {\n            className: \"feature-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Secure Investment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Get your minimum deposit back after the season (90 days)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"start-button\",\n        onClick: onClose,\n        children: [\"Get Started \", /*#__PURE__*/_jsxDEV(FaRocket, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 33\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 402,\n    columnNumber: 9\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"league-home-container\",\n    children: [userStats && /*#__PURE__*/_jsxDEV(UserStatsBar, {\n      stats: userStats\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 445,\n      columnNumber: 27\n    }, this), currentSeason && /*#__PURE__*/_jsxDEV(SeasonInfo, {\n      season: currentSeason\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 31\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      style: {\n        position: 'fixed',\n        top: '20px',\n        right: '20px',\n        background: '#ff4444',\n        color: 'white',\n        padding: '15px 25px',\n        borderRadius: '5px',\n        zIndex: 1000,\n        animation: 'slideIn 0.3s ease-out'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 23\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"league-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"Search leagues...\",\n        value: searchTerm,\n        onChange: e => setSearchTerm(e.target.value),\n        className: \"league-search\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/user/my-leagues\",\n        className: \"view-my-leagues-btn\",\n        children: [/*#__PURE__*/_jsxDEV(FaUsers, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 21\n        }, this), \" My Leagues\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"leagues-list\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Loading leagues...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 21\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 21\n      }, this) : currentLeagues.length === 0 ? /*#__PURE__*/_jsxDEV(EmptyLeagueList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 21\n      }, this) : currentLeagues.map(league => /*#__PURE__*/_jsxDEV(LeagueCard, {\n        league: league,\n        userStats: userStats,\n        onJoin: handleJoinLeague,\n        navigate: navigate\n      }, league.league_id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 25\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pagination\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentPage(prev => Math.max(prev - 1, 1)),\n        disabled: currentPage === 1,\n        children: [/*#__PURE__*/_jsxDEV(FaChartLine, {\n          className: \"rotate-180\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 21\n        }, this), \" Previous\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Page \", currentPage, \" of \", Math.max(totalPages, 1)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentPage(prev => Math.min(prev + 1, Math.max(totalPages, 1))),\n        disabled: currentPage === Math.max(totalPages, 1),\n        children: [\"Next \", /*#__PURE__*/_jsxDEV(FaChartLine, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 26\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 13\n    }, this), showWelcomeGuide && /*#__PURE__*/_jsxDEV(WelcomeGuide, {\n      onClose: () => setShowWelcomeGuide(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 34\n    }, this), showSuccessMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-message\",\n      children: showSuccessMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 17\n    }, this), showJoinModal && selectedLeague && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"Join \", selectedLeague.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Your Balance: \", userBalance.toLocaleString(), \" FC\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Min Bet: \", selectedLeague.min_bet_amount.toLocaleString(), \" FC\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Max Bet: \", selectedLeague.max_bet_amount.toLocaleString(), \" FC\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Enter Amount (FC):\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            value: joinAmount,\n            onChange: e => setJoinAmount(e.target.value),\n            min: selectedLeague.min_bet_amount,\n            max: Math.min(selectedLeague.max_bet_amount, userBalance)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 25\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 35\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: confirmJoinLeague,\n            disabled: parseFloat(joinAmount) > userBalance,\n            className: \"primary-button\",\n            children: \"Join League\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowJoinModal(false);\n              setError('');\n              setJoinAmount('');\n            },\n            className: \"secondary-button\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 443,\n    columnNumber: 9\n  }, this);\n};\n_s(LeagueHome, \"26dYeJo1MtzgoFTBSPj5j8qYyOM=\", false, function () {\n  return [useNavigate];\n});\n_c = LeagueHome;\nexport default LeagueHome;\nvar _c;\n$RefreshReg$(_c, \"LeagueHome\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "axios", "API_BASE_URL", "FaCrown", "FaStar", "FaTrophy", "FaMedal", "FaInfoCircle", "FaChartLine", "FaHistory", "FaAward", "FaBolt", "FaFire", "FaUsers", "FaCoins", "FaRocket", "FaChartBar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LeagueHome", "_s", "navigate", "leagues", "<PERSON><PERSON><PERSON><PERSON>", "userStats", "setUserStats", "loading", "setLoading", "error", "setError", "showSuccessMessage", "setShowSuccessMessage", "showWelcomeGuide", "setShowWelcomeGuide", "currentPage", "setCurrentPage", "searchTerm", "setSearchTerm", "currentSeason", "setCurrentSeason", "joinAmount", "setJoinAmount", "showJoinModal", "setShowJoinModal", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedLeague", "userBalance", "setUserBalance", "leaguesPerPage", "fetchUserBalance", "response", "get", "data", "status", "balance", "console", "fetchData", "log", "user_stats", "current_season", "Array", "isArray", "length", "localStorage", "getItem", "setItem", "message", "err", "_err$response", "handleJoinLeague", "league", "userId", "activeLeagues", "filter", "l", "name", "setTimeout", "min_bet_amount", "toString", "_error$response", "_error$response$data", "errorMessage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amount", "parseFloat", "isNaN", "max_bet_amount", "post", "league_id", "user_id", "prevLeagues", "map", "is_member", "_err$response2", "_err$response2$data", "_err$response3", "filteredLeagues", "toLowerCase", "includes", "description", "indexOfLastLeague", "indexOfFirstLeague", "currentLeagues", "slice", "totalPages", "Math", "ceil", "navLinks", "to", "label", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "SeasonInfo", "season", "className", "children", "Date", "start_date", "toLocaleDateString", "end_date", "days_remaining", "UserStatsBar", "stats", "_stats$total_points", "style", "color", "total_points", "toLocaleString", "current_streak", "leagues_joined", "points", "width", "min", "renderNavigation", "link", "window", "location", "pathname", "LeagueCard", "onJoin", "league_icon", "src", "alt", "member_count", "min_bet_formatted", "max_bet_formatted", "backgroundColor", "onClick", "has_active_membership", "disabled", "EmptyLeagueList", "WelcomeGuide", "onClose", "position", "top", "right", "background", "padding", "borderRadius", "zIndex", "animation", "type", "placeholder", "value", "onChange", "e", "target", "prev", "max", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/LeagueHome.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport { API_BASE_URL } from '../config';\nimport '../styles/league-home.css';\nimport {\n    FaCrown, FaStar, FaTrophy, FaMedal, FaInfoCircle,\n    FaChartLine, FaHistory, FaAward, FaBolt, FaFire, FaUsers,\n    FaCoins, FaRocket, FaChartBar\n} from 'react-icons/fa';\n\nconst LeagueHome = () => {\n    const navigate = useNavigate();\n    const [leagues, setLeagues] = useState([]);\n    const [userStats, setUserStats] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [showSuccessMessage, setShowSuccessMessage] = useState('');\n    const [showWelcomeGuide, setShowWelcomeGuide] = useState(false);\n    const [currentPage, setCurrentPage] = useState(1);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [currentSeason, setCurrentSeason] = useState(null);\n    const [joinAmount, setJoinAmount] = useState('');\n    const [showJoinModal, setShowJoinModal] = useState(false);\n    const [selectedLeague, setSelectedLeague] = useState(null);\n    const [userBalance, setUserBalance] = useState(0);\n    const leaguesPerPage = 12;\n\n    // Add fetchUserBalance function\n    const fetchUserBalance = async () => {\n        try {\n            const response = await axios.get('get_user_balance.php');\n            if (response.data.status === 200) {\n                setUserBalance(response.data.data.balance);\n            }\n        } catch (error) {\n            console.error('Error fetching user balance:', error);\n            setError('Failed to fetch user balance');\n        }\n    };\n\n    // Fetch leagues and user stats\n    useEffect(() => {\n        const fetchData = async () => {\n            try {\n                setLoading(true);\n                setError('');\n\n                const response = await axios.get('league_home.php');\n                console.log('League response:', response.data); // Debug log\n                \n                if (response.data.status === 200) {\n                    const { leagues, user_stats, current_season } = response.data.data;\n                    if (leagues && Array.isArray(leagues)) {\n                        setLeagues(leagues);\n                        console.log('Leagues loaded:', leagues.length); // Debug log\n                    } else {\n                        setLeagues([]);\n                        console.log('No leagues found or invalid data'); // Debug log\n                    }\n                    setUserStats(user_stats || null);\n                    setCurrentSeason(current_season || null);\n                    \n                    if (!localStorage.getItem('leagueGuideShown')) {\n                        setShowWelcomeGuide(true);\n                        localStorage.setItem('leagueGuideShown', 'true');\n                    }\n                } else {\n                    setError(response.data.message || 'Failed to load leagues');\n                }\n            } catch (err) {\n                console.error('Error fetching data:', err);\n                if (err.response?.status === 401) {\n                    navigate('/login');\n                } else {\n                    setError('Failed to load leagues. Please try again.');\n                }\n            } finally {\n                setLoading(false);\n            }\n        };\n\n        fetchData();\n        fetchUserBalance(); // Also fetch initial balance\n    }, [navigate]);\n\n    // Handle league join\n    const handleJoinLeague = async (league) => {\n        try {\n            // First check if user is already in any active league\n            const userId = localStorage.getItem('userId');\n            if (!userId) {\n                setError('Please log in to join a league');\n                navigate('/login');\n                return;\n            }\n\n            const response = await axios.get(`${API_BASE_URL}/handlers/user_leagues.php?user_id=${userId}`);\n            console.log('User leagues response:', response.data); // Debug log\n            \n            if (response.data.status === 200) {\n                const activeLeagues = response.data.data.filter(l => l.status === 'active');\n                if (activeLeagues.length > 0) {\n                    setError(`You are already a member of an active league: ${activeLeagues[0].name}. You can only join one league at a time.`);\n                    // Close modal if it's open\n                    setShowJoinModal(false);\n                    setSelectedLeague(null);\n                    // Clear the error after 5 seconds\n                    setTimeout(() => {\n                        setError('');\n                    }, 5000);\n                    return;\n                }\n            } else {\n                setError(response.data.message || 'Failed to verify league membership');\n                setShowJoinModal(false);\n                setSelectedLeague(null);\n                return;\n            }\n\n            // If we get here, user can join the league\n            setSelectedLeague(league);\n            setShowJoinModal(true);\n            setJoinAmount(league.min_bet_amount.toString());\n            \n            // Refresh user balance before showing modal\n            await fetchUserBalance();\n        } catch (error) {\n            console.error('Error checking user leagues:', error);\n            const errorMessage = error.response?.data?.message || error.message || 'Failed to verify league membership status';\n            setError(`Error: ${errorMessage}`);\n            // Close modal if it's open\n            setShowJoinModal(false);\n            setSelectedLeague(null);\n            // Clear the error after 5 seconds\n            setTimeout(() => {\n                setError('');\n            }, 5000);\n        }\n    };\n\n    const confirmJoinLeague = async () => {\n        try {\n            setError('');\n            setShowSuccessMessage('');\n\n            const amount = parseFloat(joinAmount);\n            if (isNaN(amount)) {\n                setError('Please enter a valid amount');\n                return;\n            }\n\n            if (amount < selectedLeague.min_bet_amount || amount > selectedLeague.max_bet_amount) {\n                setError(`Amount must be between ${selectedLeague.min_bet_amount} and ${selectedLeague.max_bet_amount} FC`);\n                return;\n            }\n\n            if (amount > userBalance) {\n                setError(`Insufficient balance. Your current balance is ${userBalance} FC`);\n                return;\n            }\n\n            const userId = localStorage.getItem('userId');\n            if (!userId) {\n                setError('User ID not found. Please log in again.');\n                navigate('/login');\n                return;\n            }\n\n            const response = await axios.post(`${API_BASE_URL}/handlers/join_league.php`,\n                { \n                    league_id: selectedLeague.league_id,\n                    user_id: userId,\n                    amount: amount\n                }\n            );\n\n            if (response.data.status === 200) {\n                setLeagues(prevLeagues => prevLeagues.map(league => {\n                    if (league.league_id === selectedLeague.league_id) {\n                        return {\n                            ...league,\n                            ...response.data.data.league,\n                            is_member: true\n                        };\n                    }\n                    return league;\n                }));\n                setShowSuccessMessage('Successfully joined the league!');\n                // Close modal after success\n                setTimeout(() => {\n                    setShowJoinModal(false);\n                    setJoinAmount('');\n                    setSelectedLeague(null);\n                    setShowSuccessMessage('');\n                    // Refresh user balance\n                    fetchUserBalance();\n                }, 2000);\n            }\n        } catch (err) {\n            console.error('Error joining league:', err);\n            const errorMessage = err.response?.data?.message || 'Failed to join league';\n            setError(errorMessage);\n            // Close modal on error\n            setTimeout(() => {\n                setShowJoinModal(false);\n                setJoinAmount('');\n                setSelectedLeague(null);\n                setError('');\n            }, 3000);\n            if (err.response?.status === 401) {\n                navigate('/login');\n            }\n        }\n    };\n\n    // Filter and paginate leagues\n    const filteredLeagues = leagues.filter(league => \n        league.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        (league.description && league.description.toLowerCase().includes(searchTerm.toLowerCase()))\n    );\n\n    const indexOfLastLeague = currentPage * leaguesPerPage;\n    const indexOfFirstLeague = indexOfLastLeague - leaguesPerPage;\n    const currentLeagues = filteredLeagues.slice(indexOfFirstLeague, indexOfLastLeague);\n    const totalPages = Math.ceil(filteredLeagues.length / leaguesPerPage);\n\n    // Navigation links\n    const navLinks = [\n        { to: '/user/leagues', label: 'Leagues', icon: <FaTrophy /> },\n        { to: '/user/leagues/leaderboard', label: 'Leaderboard', icon: <FaChartLine /> },\n        { to: '/user/leagues/achievements', label: 'Achievements', icon: <FaAward /> },\n        { to: '/user/leagues/seasons', label: 'Season History', icon: <FaHistory /> }\n    ];\n\n    // Season Info Component\n    const SeasonInfo = ({ season }) => (\n        <div className=\"season-info\">\n            <div className=\"season-header\">\n                <FaChartLine className=\"season-icon\" />\n                <div className=\"season-title\">\n                    <h3>{season.name}</h3>\n                    <div className=\"season-dates\">\n                        <span>Started: {new Date(season.start_date).toLocaleDateString()}</span>\n                        <span>Ends: {new Date(season.end_date).toLocaleDateString()}</span>\n                    </div>\n                </div>\n            </div>\n            {season.days_remaining > 0 && (\n                <div className=\"season-countdown\">\n                    <FaRocket className=\"countdown-icon\" />\n                    <span>{season.days_remaining} days remaining</span>\n                </div>\n            )}\n        </div>\n    );\n\n    // User Stats Bar Component\n    const UserStatsBar = ({ stats }) => (\n        <div className=\"user-stats-container\">\n            <div className=\"user-stats-bar\">\n                <div className=\"stat-item\">\n                    <FaTrophy className=\"stat-icon\" style={{ color: '#228B22' }} />\n                    <div className=\"stat-info\">\n                        <label>Total Points</label>\n                        <span>{stats?.total_points?.toLocaleString() || '0'}</span>\n                    </div>\n                </div>\n                <div className=\"stat-item\">\n                    <FaBolt className=\"stat-icon\" style={{ color: '#228B22' }} />\n                    <div className=\"stat-info\">\n                        <label>Current Streak</label>\n                        <span>{stats?.current_streak || '0'}</span>\n                    </div>\n                </div>\n                <div className=\"stat-item\">\n                    <FaCoins className=\"stat-icon\" style={{ color: '#228B22' }} />\n                    <div className=\"stat-info\">\n                        <label>Balance</label>\n                        <span>{parseFloat(stats?.balance || 0).toLocaleString()} FC</span>\n                    </div>\n                </div>\n                <div className=\"stat-item\">\n                    <FaChartBar className=\"stat-icon\" style={{ color: '#228B22' }} />\n                    <div className=\"stat-info\">\n                        <label>Leagues Joined</label>\n                        <span>{stats?.leagues_joined || '0'}</span>\n                    </div>\n                </div>\n            </div>\n            <div className=\"progress-container\">\n                <div className=\"progress-info\">\n                    <span>Progress to Next Level</span>\n                    <span>{50 - (stats?.points % 50)} points needed</span>\n                </div>\n                <div className=\"progress-bar\">\n                    <div \n                        className=\"progress-fill\" \n                        style={{ width: `${Math.min((stats?.points % 50) / 50 * 100, 100)}%` }}\n                    />\n                </div>\n            </div>\n        </div>\n    );\n\n    const renderNavigation = () => (\n        <nav className=\"league-nav\">\n            {navLinks.map((link) => (\n                <Link\n                    key={link.to}\n                    to={link.to}\n                    className={`nav-item ${window.location.pathname === link.to ? 'active' : ''}`}\n                >\n                    {link.icon}\n                    {link.label}\n                </Link>\n            ))}\n        </nav>\n    );\n\n    // League Card Component\n    const LeagueCard = ({ league, userStats, onJoin, navigate }) => (\n        <div className=\"league-list-item\">\n            <div className=\"league-main-info\">\n                <div className=\"league-icon\">\n                    {league.league_icon ? (\n                        <img src={league.league_icon} alt={`${league.name} icon`} />\n                    ) : (\n                        <FaTrophy />\n                    )}\n                </div>\n                <div className=\"league-details\">\n                    <div className=\"league-header\">\n                        <h3 className=\"league-title\">{league.name}</h3>\n                        <div className=\"league-members\">\n                            <FaUsers className=\"info-icon\" />\n                            <span className=\"info-value\">{league.member_count} Members</span>\n                        </div>\n                    </div>\n                    <p className=\"league-description\">{league.description}</p>\n                </div>\n            </div>\n\n            <div className=\"league-betting\">\n                <div className=\"betting-limits\">\n                    <div className=\"limit-item\">\n                        <span className=\"limit-label\">Min</span>\n                        <span className=\"limit-value\">{league.min_bet_formatted} FC</span>\n                    </div>\n                    <div className=\"limit-item\">\n                        <span className=\"limit-label\">Max</span>\n                        <span className=\"limit-value\">{league.max_bet_formatted} FC</span>\n                    </div>\n                </div>\n            </div>\n\n            <div className=\"league-actions\">\n                {league.is_member ? (\n                    <>\n                        <div className=\"status-badge active\" style={{ backgroundColor: '#228B22' }}>Active</div>\n                        <button \n                            className=\"view-league-btn\"\n                            onClick={() => navigate(`/user/leagues/${league.league_id}`)}\n                        >\n                            <FaChartLine /> View\n                        </button>\n                    </>\n                ) : league.has_active_membership ? (\n                    <div className=\"status-badge inactive\" style={{ backgroundColor: '#228B22' }}>In League</div>\n                ) : (\n                    <button \n                        onClick={() => onJoin(league)}\n                        className=\"join-league-btn\"\n                        disabled={userStats?.balance < league.min_bet_amount}\n                    >\n                        {userStats?.balance < league.min_bet_amount ? (\n                            <>\n                                <FaCoins /> Low Balance\n                            </>\n                        ) : (\n                            <>\n                                <FaRocket /> Join\n                            </>\n                        )}\n                    </button>\n                )}\n            </div>\n        </div>\n    );\n\n    // Empty League List Component\n    const EmptyLeagueList = () => (\n        <div className=\"empty-leagues\">\n            <FaTrophy className=\"empty-state-icon\" />\n            <p>No leagues available at the moment</p>\n            <span>Check back soon for new leagues!</span>\n        </div>\n    );\n\n    // Welcome Guide Component\n    const WelcomeGuide = ({ onClose }) => (\n        <div className=\"welcome-guide\">\n            <div className=\"guide-content\">\n                <h2><FaCrown /> Welcome to 247 League!</h2>\n                <div className=\"guide-features\">\n                    <div className=\"feature-item\">\n                        <FaTrophy className=\"feature-icon\" />\n                        <div className=\"feature-text\">\n                            <h3>Compete in Leagues</h3>\n                            <p>Join leagues that match your betting range and compete with others</p>\n                        </div>\n                    </div>\n                    <div className=\"feature-item\">\n                        <FaChartLine className=\"feature-icon\" />\n                        <div className=\"feature-text\">\n                            <h3>Earn Points</h3>\n                            <p>Win: 3 points | Draw: 1 point | Climb the leaderboard</p>\n                        </div>\n                    </div>\n                    <div className=\"feature-item\">\n                        <FaMedal className=\"feature-icon\" />\n                        <div className=\"feature-text\">\n                            <h3>Win Rewards</h3>\n                            <p>Unlock badges and earn rewards as you progress</p>\n                        </div>\n                    </div>\n                    <div className=\"feature-item\">\n                        <FaCoins className=\"feature-icon\" />\n                        <div className=\"feature-text\">\n                            <h3>Secure Investment</h3>\n                            <p>Get your minimum deposit back after the season (90 days)</p>\n                        </div>\n                    </div>\n                </div>\n                <button className=\"start-button\" onClick={onClose}>\n                    Get Started <FaRocket />\n                </button>\n            </div>\n        </div>\n    );\n\n    return (\n        <div className=\"league-home-container\">\n            {/* User Stats Bar */}\n            {userStats && <UserStatsBar stats={userStats} />}\n            \n            {/* Season Information */}\n            {currentSeason && <SeasonInfo season={currentSeason} />}\n            \n            {/* Error Message */}\n            {error && <div className=\"error-message\" style={{\n                position: 'fixed',\n                top: '20px',\n                right: '20px',\n                background: '#ff4444',\n                color: 'white',\n                padding: '15px 25px',\n                borderRadius: '5px',\n                zIndex: 1000,\n                animation: 'slideIn 0.3s ease-out'\n            }}>{error}</div>}\n            \n            {/* Search and Controls */}\n            <div className=\"league-controls\">\n                <input\n                    type=\"text\"\n                    placeholder=\"Search leagues...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"league-search\"\n                />\n                <Link to=\"/user/my-leagues\" className=\"view-my-leagues-btn\">\n                    <FaUsers /> My Leagues\n                </Link>\n            </div>\n\n            {/* Leagues List */}\n            <div className=\"leagues-list\">\n                {loading ? (\n                    <div className=\"loading\">Loading leagues...</div>\n                ) : error ? (\n                    <div className=\"error-message\">{error}</div>\n                ) : currentLeagues.length === 0 ? (\n                    <EmptyLeagueList />\n                ) : (\n                    currentLeagues.map(league => (\n                        <LeagueCard \n                            key={league.league_id} \n                            league={league} \n                            userStats={userStats} \n                            onJoin={handleJoinLeague} \n                            navigate={navigate} \n                        />\n                    ))\n                )}\n            </div>\n\n            {/* Pagination - Always Visible */}\n            <div className=\"pagination\">\n                <button \n                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}\n                    disabled={currentPage === 1}\n                >\n                    <FaChartLine className=\"rotate-180\" /> Previous\n                </button>\n                <span>Page {currentPage} of {Math.max(totalPages, 1)}</span>\n                <button \n                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, Math.max(totalPages, 1)))}\n                    disabled={currentPage === Math.max(totalPages, 1)}\n                >\n                    Next <FaChartLine />\n                </button>\n            </div>\n\n            {/* Welcome Guide */}\n            {showWelcomeGuide && <WelcomeGuide onClose={() => setShowWelcomeGuide(false)} />}\n            \n            {/* Success Message */}\n            {showSuccessMessage && (\n                <div className=\"success-message\">{showSuccessMessage}</div>\n            )}\n            \n            {/* Join League Modal */}\n            {showJoinModal && selectedLeague && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <h2>Join {selectedLeague.name}</h2>\n                        <p>Your Balance: {userBalance.toLocaleString()} FC</p>\n                        <p>Min Bet: {selectedLeague.min_bet_amount.toLocaleString()} FC</p>\n                        <p>Max Bet: {selectedLeague.max_bet_amount.toLocaleString()} FC</p>\n                        <div className=\"form-group\">\n                            <label>Enter Amount (FC):</label>\n                            <input\n                                type=\"number\"\n                                value={joinAmount}\n                                onChange={(e) => setJoinAmount(e.target.value)}\n                                min={selectedLeague.min_bet_amount}\n                                max={Math.min(selectedLeague.max_bet_amount, userBalance)}\n                            />\n                        </div>\n                        {error && <div className=\"error\">{error}</div>}\n                        <div className=\"modal-buttons\">\n                            <button onClick={confirmJoinLeague} \n                                    disabled={parseFloat(joinAmount) > userBalance}\n                                    className=\"primary-button\">\n                                Join League\n                            </button>\n                            <button onClick={() => {\n                                setShowJoinModal(false);\n                                setError('');\n                                setJoinAmount('');\n                            }} className=\"secondary-button\">\n                                Cancel\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default LeagueHome;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,2BAA2B;AAClC,SACIC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,YAAY,EAChDC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EACxDC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,QAC1B,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACmC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2C,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAMqD,cAAc,GAAG,EAAE;;EAEzB;EACA,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMnD,KAAK,CAACoD,GAAG,CAAC,sBAAsB,CAAC;MACxD,IAAID,QAAQ,CAACE,IAAI,CAACC,MAAM,KAAK,GAAG,EAAE;QAC9BN,cAAc,CAACG,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACE,OAAO,CAAC;MAC9C;IACJ,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACZ2B,OAAO,CAAC3B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDC,QAAQ,CAAC,8BAA8B,CAAC;IAC5C;EACJ,CAAC;;EAED;EACAjC,SAAS,CAAC,MAAM;IACZ,MAAM4D,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC1B,IAAI;QACA7B,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,EAAE,CAAC;QAEZ,MAAMqB,QAAQ,GAAG,MAAMnD,KAAK,CAACoD,GAAG,CAAC,iBAAiB,CAAC;QACnDI,OAAO,CAACE,GAAG,CAAC,kBAAkB,EAAEP,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;;QAEhD,IAAIF,QAAQ,CAACE,IAAI,CAACC,MAAM,KAAK,GAAG,EAAE;UAC9B,MAAM;YAAE/B,OAAO;YAAEoC,UAAU;YAAEC;UAAe,CAAC,GAAGT,QAAQ,CAACE,IAAI,CAACA,IAAI;UAClE,IAAI9B,OAAO,IAAIsC,KAAK,CAACC,OAAO,CAACvC,OAAO,CAAC,EAAE;YACnCC,UAAU,CAACD,OAAO,CAAC;YACnBiC,OAAO,CAACE,GAAG,CAAC,iBAAiB,EAAEnC,OAAO,CAACwC,MAAM,CAAC,CAAC,CAAC;UACpD,CAAC,MAAM;YACHvC,UAAU,CAAC,EAAE,CAAC;YACdgC,OAAO,CAACE,GAAG,CAAC,kCAAkC,CAAC,CAAC,CAAC;UACrD;UACAhC,YAAY,CAACiC,UAAU,IAAI,IAAI,CAAC;UAChCnB,gBAAgB,CAACoB,cAAc,IAAI,IAAI,CAAC;UAExC,IAAI,CAACI,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,EAAE;YAC3C/B,mBAAmB,CAAC,IAAI,CAAC;YACzB8B,YAAY,CAACE,OAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC;UACpD;QACJ,CAAC,MAAM;UACHpC,QAAQ,CAACqB,QAAQ,CAACE,IAAI,CAACc,OAAO,IAAI,wBAAwB,CAAC;QAC/D;MACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAC,aAAA;QACVb,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,EAAEuC,GAAG,CAAC;QAC1C,IAAI,EAAAC,aAAA,GAAAD,GAAG,CAACjB,QAAQ,cAAAkB,aAAA,uBAAZA,aAAA,CAAcf,MAAM,MAAK,GAAG,EAAE;UAC9BhC,QAAQ,CAAC,QAAQ,CAAC;QACtB,CAAC,MAAM;UACHQ,QAAQ,CAAC,2CAA2C,CAAC;QACzD;MACJ,CAAC,SAAS;QACNF,UAAU,CAAC,KAAK,CAAC;MACrB;IACJ,CAAC;IAED6B,SAAS,CAAC,CAAC;IACXP,gBAAgB,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,EAAE,CAAC5B,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMgD,gBAAgB,GAAG,MAAOC,MAAM,IAAK;IACvC,IAAI;MACA;MACA,MAAMC,MAAM,GAAGR,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;MAC7C,IAAI,CAACO,MAAM,EAAE;QACT1C,QAAQ,CAAC,gCAAgC,CAAC;QAC1CR,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACJ;MAEA,MAAM6B,QAAQ,GAAG,MAAMnD,KAAK,CAACoD,GAAG,CAAC,GAAGnD,YAAY,sCAAsCuE,MAAM,EAAE,CAAC;MAC/FhB,OAAO,CAACE,GAAG,CAAC,wBAAwB,EAAEP,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;;MAEtD,IAAIF,QAAQ,CAACE,IAAI,CAACC,MAAM,KAAK,GAAG,EAAE;QAC9B,MAAMmB,aAAa,GAAGtB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACqB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrB,MAAM,KAAK,QAAQ,CAAC;QAC3E,IAAImB,aAAa,CAACV,MAAM,GAAG,CAAC,EAAE;UAC1BjC,QAAQ,CAAC,iDAAiD2C,aAAa,CAAC,CAAC,CAAC,CAACG,IAAI,2CAA2C,CAAC;UAC3H;UACAhC,gBAAgB,CAAC,KAAK,CAAC;UACvBE,iBAAiB,CAAC,IAAI,CAAC;UACvB;UACA+B,UAAU,CAAC,MAAM;YACb/C,QAAQ,CAAC,EAAE,CAAC;UAChB,CAAC,EAAE,IAAI,CAAC;UACR;QACJ;MACJ,CAAC,MAAM;QACHA,QAAQ,CAACqB,QAAQ,CAACE,IAAI,CAACc,OAAO,IAAI,oCAAoC,CAAC;QACvEvB,gBAAgB,CAAC,KAAK,CAAC;QACvBE,iBAAiB,CAAC,IAAI,CAAC;QACvB;MACJ;;MAEA;MACAA,iBAAiB,CAACyB,MAAM,CAAC;MACzB3B,gBAAgB,CAAC,IAAI,CAAC;MACtBF,aAAa,CAAC6B,MAAM,CAACO,cAAc,CAACC,QAAQ,CAAC,CAAC,CAAC;;MAE/C;MACA,MAAM7B,gBAAgB,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOrB,KAAK,EAAE;MAAA,IAAAmD,eAAA,EAAAC,oBAAA;MACZzB,OAAO,CAAC3B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMqD,YAAY,GAAG,EAAAF,eAAA,GAAAnD,KAAK,CAACsB,QAAQ,cAAA6B,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB3B,IAAI,cAAA4B,oBAAA,uBAApBA,oBAAA,CAAsBd,OAAO,KAAItC,KAAK,CAACsC,OAAO,IAAI,2CAA2C;MAClHrC,QAAQ,CAAC,UAAUoD,YAAY,EAAE,CAAC;MAClC;MACAtC,gBAAgB,CAAC,KAAK,CAAC;MACvBE,iBAAiB,CAAC,IAAI,CAAC;MACvB;MACA+B,UAAU,CAAC,MAAM;QACb/C,QAAQ,CAAC,EAAE,CAAC;MAChB,CAAC,EAAE,IAAI,CAAC;IACZ;EACJ,CAAC;EAED,MAAMqD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACArD,QAAQ,CAAC,EAAE,CAAC;MACZE,qBAAqB,CAAC,EAAE,CAAC;MAEzB,MAAMoD,MAAM,GAAGC,UAAU,CAAC5C,UAAU,CAAC;MACrC,IAAI6C,KAAK,CAACF,MAAM,CAAC,EAAE;QACftD,QAAQ,CAAC,6BAA6B,CAAC;QACvC;MACJ;MAEA,IAAIsD,MAAM,GAAGvC,cAAc,CAACiC,cAAc,IAAIM,MAAM,GAAGvC,cAAc,CAAC0C,cAAc,EAAE;QAClFzD,QAAQ,CAAC,0BAA0Be,cAAc,CAACiC,cAAc,QAAQjC,cAAc,CAAC0C,cAAc,KAAK,CAAC;QAC3G;MACJ;MAEA,IAAIH,MAAM,GAAGrC,WAAW,EAAE;QACtBjB,QAAQ,CAAC,iDAAiDiB,WAAW,KAAK,CAAC;QAC3E;MACJ;MAEA,MAAMyB,MAAM,GAAGR,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;MAC7C,IAAI,CAACO,MAAM,EAAE;QACT1C,QAAQ,CAAC,yCAAyC,CAAC;QACnDR,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACJ;MAEA,MAAM6B,QAAQ,GAAG,MAAMnD,KAAK,CAACwF,IAAI,CAAC,GAAGvF,YAAY,2BAA2B,EACxE;QACIwF,SAAS,EAAE5C,cAAc,CAAC4C,SAAS;QACnCC,OAAO,EAAElB,MAAM;QACfY,MAAM,EAAEA;MACZ,CACJ,CAAC;MAED,IAAIjC,QAAQ,CAACE,IAAI,CAACC,MAAM,KAAK,GAAG,EAAE;QAC9B9B,UAAU,CAACmE,WAAW,IAAIA,WAAW,CAACC,GAAG,CAACrB,MAAM,IAAI;UAChD,IAAIA,MAAM,CAACkB,SAAS,KAAK5C,cAAc,CAAC4C,SAAS,EAAE;YAC/C,OAAO;cACH,GAAGlB,MAAM;cACT,GAAGpB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACkB,MAAM;cAC5BsB,SAAS,EAAE;YACf,CAAC;UACL;UACA,OAAOtB,MAAM;QACjB,CAAC,CAAC,CAAC;QACHvC,qBAAqB,CAAC,iCAAiC,CAAC;QACxD;QACA6C,UAAU,CAAC,MAAM;UACbjC,gBAAgB,CAAC,KAAK,CAAC;UACvBF,aAAa,CAAC,EAAE,CAAC;UACjBI,iBAAiB,CAAC,IAAI,CAAC;UACvBd,qBAAqB,CAAC,EAAE,CAAC;UACzB;UACAkB,gBAAgB,CAAC,CAAC;QACtB,CAAC,EAAE,IAAI,CAAC;MACZ;IACJ,CAAC,CAAC,OAAOkB,GAAG,EAAE;MAAA,IAAA0B,cAAA,EAAAC,mBAAA,EAAAC,cAAA;MACVxC,OAAO,CAAC3B,KAAK,CAAC,uBAAuB,EAAEuC,GAAG,CAAC;MAC3C,MAAMc,YAAY,GAAG,EAAAY,cAAA,GAAA1B,GAAG,CAACjB,QAAQ,cAAA2C,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAczC,IAAI,cAAA0C,mBAAA,uBAAlBA,mBAAA,CAAoB5B,OAAO,KAAI,uBAAuB;MAC3ErC,QAAQ,CAACoD,YAAY,CAAC;MACtB;MACAL,UAAU,CAAC,MAAM;QACbjC,gBAAgB,CAAC,KAAK,CAAC;QACvBF,aAAa,CAAC,EAAE,CAAC;QACjBI,iBAAiB,CAAC,IAAI,CAAC;QACvBhB,QAAQ,CAAC,EAAE,CAAC;MAChB,CAAC,EAAE,IAAI,CAAC;MACR,IAAI,EAAAkE,cAAA,GAAA5B,GAAG,CAACjB,QAAQ,cAAA6C,cAAA,uBAAZA,cAAA,CAAc1C,MAAM,MAAK,GAAG,EAAE;QAC9BhC,QAAQ,CAAC,QAAQ,CAAC;MACtB;IACJ;EACJ,CAAC;;EAED;EACA,MAAM2E,eAAe,GAAG1E,OAAO,CAACmD,MAAM,CAACH,MAAM,IACzCA,MAAM,CAACK,IAAI,CAACsB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9D,UAAU,CAAC6D,WAAW,CAAC,CAAC,CAAC,IAC3D3B,MAAM,CAAC6B,WAAW,IAAI7B,MAAM,CAAC6B,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9D,UAAU,CAAC6D,WAAW,CAAC,CAAC,CAC7F,CAAC;EAED,MAAMG,iBAAiB,GAAGlE,WAAW,GAAGc,cAAc;EACtD,MAAMqD,kBAAkB,GAAGD,iBAAiB,GAAGpD,cAAc;EAC7D,MAAMsD,cAAc,GAAGN,eAAe,CAACO,KAAK,CAACF,kBAAkB,EAAED,iBAAiB,CAAC;EACnF,MAAMI,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACV,eAAe,CAAClC,MAAM,GAAGd,cAAc,CAAC;;EAErE;EACA,MAAM2D,QAAQ,GAAG,CACb;IAAEC,EAAE,EAAE,eAAe;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,eAAE9F,OAAA,CAACb,QAAQ;MAAA4G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC7D;IAAEN,EAAE,EAAE,2BAA2B;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,eAAE9F,OAAA,CAACV,WAAW;MAAAyG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAChF;IAAEN,EAAE,EAAE,4BAA4B;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,eAAE9F,OAAA,CAACR,OAAO;MAAAuG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC9E;IAAEN,EAAE,EAAE,uBAAuB;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,eAAE9F,OAAA,CAACT,SAAS;MAAAwG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CAChF;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAC;IAAEC;EAAO,CAAC,kBAC1BpG,OAAA;IAAKqG,SAAS,EAAC,aAAa;IAAAC,QAAA,gBACxBtG,OAAA;MAAKqG,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1BtG,OAAA,CAACV,WAAW;QAAC+G,SAAS,EAAC;MAAa;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvClG,OAAA;QAAKqG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzBtG,OAAA;UAAAsG,QAAA,EAAKF,MAAM,CAACzC;QAAI;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtBlG,OAAA;UAAKqG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBtG,OAAA;YAAAsG,QAAA,GAAM,WAAS,EAAC,IAAIC,IAAI,CAACH,MAAM,CAACI,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxElG,OAAA;YAAAsG,QAAA,GAAM,QAAM,EAAC,IAAIC,IAAI,CAACH,MAAM,CAACM,QAAQ,CAAC,CAACD,kBAAkB,CAAC,CAAC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACLE,MAAM,CAACO,cAAc,GAAG,CAAC,iBACtB3G,OAAA;MAAKqG,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BtG,OAAA,CAACH,QAAQ;QAACwG,SAAS,EAAC;MAAgB;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvClG,OAAA;QAAAsG,QAAA,GAAOF,MAAM,CAACO,cAAc,EAAC,iBAAe;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CACR;;EAED;EACA,MAAMU,YAAY,GAAGA,CAAC;IAAEC;EAAM,CAAC;IAAA,IAAAC,mBAAA;IAAA,oBAC3B9G,OAAA;MAAKqG,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACjCtG,OAAA;QAAKqG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BtG,OAAA;UAAKqG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBtG,OAAA,CAACb,QAAQ;YAACkH,SAAS,EAAC,WAAW;YAACU,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DlG,OAAA;YAAKqG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBtG,OAAA;cAAAsG,QAAA,EAAO;YAAY;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3BlG,OAAA;cAAAsG,QAAA,EAAO,CAAAO,KAAK,aAALA,KAAK,wBAAAC,mBAAA,GAALD,KAAK,CAAEI,YAAY,cAAAH,mBAAA,uBAAnBA,mBAAA,CAAqBI,cAAc,CAAC,CAAC,KAAI;YAAG;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNlG,OAAA;UAAKqG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBtG,OAAA,CAACP,MAAM;YAAC4G,SAAS,EAAC,WAAW;YAACU,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DlG,OAAA;YAAKqG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBtG,OAAA;cAAAsG,QAAA,EAAO;YAAc;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7BlG,OAAA;cAAAsG,QAAA,EAAO,CAAAO,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,cAAc,KAAI;YAAG;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNlG,OAAA;UAAKqG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBtG,OAAA,CAACJ,OAAO;YAACyG,SAAS,EAAC,WAAW;YAACU,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DlG,OAAA;YAAKqG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBtG,OAAA;cAAAsG,QAAA,EAAO;YAAO;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtBlG,OAAA;cAAAsG,QAAA,GAAOlC,UAAU,CAAC,CAAAyC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEvE,OAAO,KAAI,CAAC,CAAC,CAAC4E,cAAc,CAAC,CAAC,EAAC,KAAG;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNlG,OAAA;UAAKqG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBtG,OAAA,CAACF,UAAU;YAACuG,SAAS,EAAC,WAAW;YAACU,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjElG,OAAA;YAAKqG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBtG,OAAA;cAAAsG,QAAA,EAAO;YAAc;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7BlG,OAAA;cAAAsG,QAAA,EAAO,CAAAO,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEO,cAAc,KAAI;YAAG;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNlG,OAAA;QAAKqG,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAC/BtG,OAAA;UAAKqG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1BtG,OAAA;YAAAsG,QAAA,EAAM;UAAsB;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnClG,OAAA;YAAAsG,QAAA,GAAO,EAAE,GAAI,CAAAO,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,MAAM,IAAG,EAAG,EAAC,gBAAc;UAAA;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACNlG,OAAA;UAAKqG,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBtG,OAAA;YACIqG,SAAS,EAAC,eAAe;YACzBU,KAAK,EAAE;cAAEO,KAAK,EAAE,GAAG7B,IAAI,CAAC8B,GAAG,CAAE,CAAAV,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,MAAM,IAAG,EAAE,GAAI,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;YAAI;UAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,CACT;EAED,MAAMsB,gBAAgB,GAAGA,CAAA,kBACrBxH,OAAA;IAAKqG,SAAS,EAAC,YAAY;IAAAC,QAAA,EACtBX,QAAQ,CAAChB,GAAG,CAAE8C,IAAI,iBACfzH,OAAA,CAACnB,IAAI;MAED+G,EAAE,EAAE6B,IAAI,CAAC7B,EAAG;MACZS,SAAS,EAAE,YAAYqB,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKH,IAAI,CAAC7B,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;MAAAU,QAAA,GAE7EmB,IAAI,CAAC3B,IAAI,EACT2B,IAAI,CAAC5B,KAAK;IAAA,GALN4B,IAAI,CAAC7B,EAAE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMV,CACT;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CACR;;EAED;EACA,MAAM2B,UAAU,GAAGA,CAAC;IAAEvE,MAAM;IAAE9C,SAAS;IAAEsH,MAAM;IAAEzH;EAAS,CAAC,kBACvDL,OAAA;IAAKqG,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC7BtG,OAAA;MAAKqG,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BtG,OAAA;QAAKqG,SAAS,EAAC,aAAa;QAAAC,QAAA,EACvBhD,MAAM,CAACyE,WAAW,gBACf/H,OAAA;UAAKgI,GAAG,EAAE1E,MAAM,CAACyE,WAAY;UAACE,GAAG,EAAE,GAAG3E,MAAM,CAACK,IAAI;QAAQ;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE5DlG,OAAA,CAACb,QAAQ;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MACd;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACNlG,OAAA;QAAKqG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BtG,OAAA;UAAKqG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1BtG,OAAA;YAAIqG,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEhD,MAAM,CAACK;UAAI;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/ClG,OAAA;YAAKqG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BtG,OAAA,CAACL,OAAO;cAAC0G,SAAS,EAAC;YAAW;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjClG,OAAA;cAAMqG,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAEhD,MAAM,CAAC4E,YAAY,EAAC,UAAQ;YAAA;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNlG,OAAA;UAAGqG,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEhD,MAAM,CAAC6B;QAAW;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENlG,OAAA;MAAKqG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC3BtG,OAAA;QAAKqG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BtG,OAAA;UAAKqG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBtG,OAAA;YAAMqG,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxClG,OAAA;YAAMqG,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAEhD,MAAM,CAAC6E,iBAAiB,EAAC,KAAG;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eACNlG,OAAA;UAAKqG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBtG,OAAA;YAAMqG,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxClG,OAAA;YAAMqG,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAEhD,MAAM,CAAC8E,iBAAiB,EAAC,KAAG;UAAA;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENlG,OAAA;MAAKqG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC1BhD,MAAM,CAACsB,SAAS,gBACb5E,OAAA,CAAAE,SAAA;QAAAoG,QAAA,gBACItG,OAAA;UAAKqG,SAAS,EAAC,qBAAqB;UAACU,KAAK,EAAE;YAAEsB,eAAe,EAAE;UAAU,CAAE;UAAA/B,QAAA,EAAC;QAAM;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxFlG,OAAA;UACIqG,SAAS,EAAC,iBAAiB;UAC3BiC,OAAO,EAAEA,CAAA,KAAMjI,QAAQ,CAAC,iBAAiBiD,MAAM,CAACkB,SAAS,EAAE,CAAE;UAAA8B,QAAA,gBAE7DtG,OAAA,CAACV,WAAW;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SACnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACX,CAAC,GACH5C,MAAM,CAACiF,qBAAqB,gBAC5BvI,OAAA;QAAKqG,SAAS,EAAC,uBAAuB;QAACU,KAAK,EAAE;UAAEsB,eAAe,EAAE;QAAU,CAAE;QAAA/B,QAAA,EAAC;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAE7FlG,OAAA;QACIsI,OAAO,EAAEA,CAAA,KAAMR,MAAM,CAACxE,MAAM,CAAE;QAC9B+C,SAAS,EAAC,iBAAiB;QAC3BmC,QAAQ,EAAE,CAAAhI,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE8B,OAAO,IAAGgB,MAAM,CAACO,cAAe;QAAAyC,QAAA,EAEpD,CAAA9F,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE8B,OAAO,IAAGgB,MAAM,CAACO,cAAc,gBACvC7D,OAAA,CAAAE,SAAA;UAAAoG,QAAA,gBACItG,OAAA,CAACJ,OAAO;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBACf;QAAA,eAAE,CAAC,gBAEHlG,OAAA,CAAAE,SAAA;UAAAoG,QAAA,gBACItG,OAAA,CAACH,QAAQ;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAChB;QAAA,eAAE;MACL;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IACX;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;;EAED;EACA,MAAMuC,eAAe,GAAGA,CAAA,kBACpBzI,OAAA;IAAKqG,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC1BtG,OAAA,CAACb,QAAQ;MAACkH,SAAS,EAAC;IAAkB;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzClG,OAAA;MAAAsG,QAAA,EAAG;IAAkC;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACzClG,OAAA;MAAAsG,QAAA,EAAM;IAAgC;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5C,CACR;;EAED;EACA,MAAMwC,YAAY,GAAGA,CAAC;IAAEC;EAAQ,CAAC,kBAC7B3I,OAAA;IAAKqG,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC1BtG,OAAA;MAAKqG,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1BtG,OAAA;QAAAsG,QAAA,gBAAItG,OAAA,CAACf,OAAO;UAAA8G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,2BAAuB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3ClG,OAAA;QAAKqG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BtG,OAAA;UAAKqG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBtG,OAAA,CAACb,QAAQ;YAACkH,SAAS,EAAC;UAAc;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrClG,OAAA;YAAKqG,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBtG,OAAA;cAAAsG,QAAA,EAAI;YAAkB;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3BlG,OAAA;cAAAsG,QAAA,EAAG;YAAkE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNlG,OAAA;UAAKqG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBtG,OAAA,CAACV,WAAW;YAAC+G,SAAS,EAAC;UAAc;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxClG,OAAA;YAAKqG,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBtG,OAAA;cAAAsG,QAAA,EAAI;YAAW;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBlG,OAAA;cAAAsG,QAAA,EAAG;YAAqD;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNlG,OAAA;UAAKqG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBtG,OAAA,CAACZ,OAAO;YAACiH,SAAS,EAAC;UAAc;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpClG,OAAA;YAAKqG,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBtG,OAAA;cAAAsG,QAAA,EAAI;YAAW;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBlG,OAAA;cAAAsG,QAAA,EAAG;YAA8C;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNlG,OAAA;UAAKqG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBtG,OAAA,CAACJ,OAAO;YAACyG,SAAS,EAAC;UAAc;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpClG,OAAA;YAAKqG,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBtG,OAAA;cAAAsG,QAAA,EAAI;YAAiB;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BlG,OAAA;cAAAsG,QAAA,EAAG;YAAwD;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNlG,OAAA;QAAQqG,SAAS,EAAC,cAAc;QAACiC,OAAO,EAAEK,OAAQ;QAAArC,QAAA,GAAC,cACnC,eAAAtG,OAAA,CAACH,QAAQ;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;EAED,oBACIlG,OAAA;IAAKqG,SAAS,EAAC,uBAAuB;IAAAC,QAAA,GAEjC9F,SAAS,iBAAIR,OAAA,CAAC4G,YAAY;MAACC,KAAK,EAAErG;IAAU;MAAAuF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAG/C5E,aAAa,iBAAItB,OAAA,CAACmG,UAAU;MAACC,MAAM,EAAE9E;IAAc;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGtDtF,KAAK,iBAAIZ,OAAA;MAAKqG,SAAS,EAAC,eAAe;MAACU,KAAK,EAAE;QAC5C6B,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE,SAAS;QACrB/B,KAAK,EAAE,OAAO;QACdgC,OAAO,EAAE,WAAW;QACpBC,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE;MACf,CAAE;MAAA7C,QAAA,EAAE1F;IAAK;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAGhBlG,OAAA;MAAKqG,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BtG,OAAA;QACIoJ,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,mBAAmB;QAC/BC,KAAK,EAAElI,UAAW;QAClBmI,QAAQ,EAAGC,CAAC,IAAKnI,aAAa,CAACmI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC/CjD,SAAS,EAAC;MAAe;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACFlG,OAAA,CAACnB,IAAI;QAAC+G,EAAE,EAAC,kBAAkB;QAACS,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACvDtG,OAAA,CAACL,OAAO;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACf;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNlG,OAAA;MAAKqG,SAAS,EAAC,cAAc;MAAAC,QAAA,EACxB5F,OAAO,gBACJV,OAAA;QAAKqG,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAkB;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,GACjDtF,KAAK,gBACLZ,OAAA;QAAKqG,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAE1F;MAAK;QAAAmF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,GAC5CZ,cAAc,CAACxC,MAAM,KAAK,CAAC,gBAC3B9C,OAAA,CAACyI,eAAe;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAEnBZ,cAAc,CAACX,GAAG,CAACrB,MAAM,iBACrBtD,OAAA,CAAC6H,UAAU;QAEPvE,MAAM,EAAEA,MAAO;QACf9C,SAAS,EAAEA,SAAU;QACrBsH,MAAM,EAAEzE,gBAAiB;QACzBhD,QAAQ,EAAEA;MAAS,GAJdiD,MAAM,CAACkB,SAAS;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKxB,CACJ;IACJ;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGNlG,OAAA;MAAKqG,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACvBtG,OAAA;QACIsI,OAAO,EAAEA,CAAA,KAAMnH,cAAc,CAACuI,IAAI,IAAIjE,IAAI,CAACkE,GAAG,CAACD,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAE;QAC7DlB,QAAQ,EAAEtH,WAAW,KAAK,CAAE;QAAAoF,QAAA,gBAE5BtG,OAAA,CAACV,WAAW;UAAC+G,SAAS,EAAC;QAAY;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAC1C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlG,OAAA;QAAAsG,QAAA,GAAM,OAAK,EAACpF,WAAW,EAAC,MAAI,EAACuE,IAAI,CAACkE,GAAG,CAACnE,UAAU,EAAE,CAAC,CAAC;MAAA;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5DlG,OAAA;QACIsI,OAAO,EAAEA,CAAA,KAAMnH,cAAc,CAACuI,IAAI,IAAIjE,IAAI,CAAC8B,GAAG,CAACmC,IAAI,GAAG,CAAC,EAAEjE,IAAI,CAACkE,GAAG,CAACnE,UAAU,EAAE,CAAC,CAAC,CAAC,CAAE;QACnFgD,QAAQ,EAAEtH,WAAW,KAAKuE,IAAI,CAACkE,GAAG,CAACnE,UAAU,EAAE,CAAC,CAAE;QAAAc,QAAA,GACrD,OACQ,eAAAtG,OAAA,CAACV,WAAW;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAGLlF,gBAAgB,iBAAIhB,OAAA,CAAC0I,YAAY;MAACC,OAAO,EAAEA,CAAA,KAAM1H,mBAAmB,CAAC,KAAK;IAAE;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAG/EpF,kBAAkB,iBACfd,OAAA;MAAKqG,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAAExF;IAAkB;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAC7D,EAGAxE,aAAa,IAAIE,cAAc,iBAC5B5B,OAAA;MAAKqG,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC1BtG,OAAA;QAAKqG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BtG,OAAA;UAAAsG,QAAA,GAAI,OAAK,EAAC1E,cAAc,CAAC+B,IAAI;QAAA;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnClG,OAAA;UAAAsG,QAAA,GAAG,gBAAc,EAACxE,WAAW,CAACoF,cAAc,CAAC,CAAC,EAAC,KAAG;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtDlG,OAAA;UAAAsG,QAAA,GAAG,WAAS,EAAC1E,cAAc,CAACiC,cAAc,CAACqD,cAAc,CAAC,CAAC,EAAC,KAAG;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnElG,OAAA;UAAAsG,QAAA,GAAG,WAAS,EAAC1E,cAAc,CAAC0C,cAAc,CAAC4C,cAAc,CAAC,CAAC,EAAC,KAAG;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnElG,OAAA;UAAKqG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBtG,OAAA;YAAAsG,QAAA,EAAO;UAAkB;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjClG,OAAA;YACIoJ,IAAI,EAAC,QAAQ;YACbE,KAAK,EAAE9H,UAAW;YAClB+H,QAAQ,EAAGC,CAAC,IAAK/H,aAAa,CAAC+H,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/C/B,GAAG,EAAE3F,cAAc,CAACiC,cAAe;YACnC8F,GAAG,EAAElE,IAAI,CAAC8B,GAAG,CAAC3F,cAAc,CAAC0C,cAAc,EAAExC,WAAW;UAAE;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLtF,KAAK,iBAAIZ,OAAA;UAAKqG,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE1F;QAAK;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9ClG,OAAA;UAAKqG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1BtG,OAAA;YAAQsI,OAAO,EAAEpE,iBAAkB;YAC3BsE,QAAQ,EAAEpE,UAAU,CAAC5C,UAAU,CAAC,GAAGM,WAAY;YAC/CuE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAEnC;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlG,OAAA;YAAQsI,OAAO,EAAEA,CAAA,KAAM;cACnB3G,gBAAgB,CAAC,KAAK,CAAC;cACvBd,QAAQ,CAAC,EAAE,CAAC;cACZY,aAAa,CAAC,EAAE,CAAC;YACrB,CAAE;YAAC4E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAEhC;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC9F,EAAA,CAriBID,UAAU;EAAA,QACKrB,WAAW;AAAA;AAAA8K,EAAA,GAD1BzJ,UAAU;AAuiBhB,eAAeA,UAAU;AAAC,IAAAyJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}