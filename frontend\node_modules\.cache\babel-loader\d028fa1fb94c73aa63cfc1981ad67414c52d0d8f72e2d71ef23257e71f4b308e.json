{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\UserLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Link, useLocation, useNavigate, Outlet } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport './UserLayoutSidebar.css';\nimport './UserLayout.css';\nimport { FaEnvelope, FaBars, FaChevronLeft, FaTimes } from 'react-icons/fa';\nimport { useUser } from '../context/UserContext';\n// import { CurrencyBalance, CurrencyQuickSelector } from '../components/Currency'; // DISABLED: Currency system being recoded\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction UserLayout() {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    userData,\n    setUserData\n  } = useUser();\n  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\n  const [isMobileView, setIsMobileView] = useState(window.innerWidth <= 768);\n  const [unreadMessages, setUnreadMessages] = useState(0);\n  const [pendingRequests, setPendingRequests] = useState(0);\n  const [prevUnreadCount, setPrevUnreadCount] = useState(0);\n  const [notification, setNotification] = useState(null);\n  const [sidebarLogo, setSidebarLogo] = useState(null);\n  const userId = localStorage.getItem('userId');\n  const username = localStorage.getItem('username');\n  const token = localStorage.getItem('userToken');\n\n  // List of public routes that don't need auth\n  const publicRoutes = ['/login', '/register', '/', '/about'];\n\n  // Handle window resize\n  useEffect(() => {\n    const handleResize = () => {\n      const mobile = window.innerWidth <= 768;\n      setIsMobileView(mobile);\n      if (!mobile) {\n        setIsMobileSidebarOpen(false);\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Close mobile sidebar when route changes\n  useEffect(() => {\n    setIsMobileSidebarOpen(false);\n  }, [location.pathname]);\n  const fetchUserData = useCallback(async () => {\n    if (!token || !userId) return;\n    try {\n      const response = await axios.get('/backend/handlers/user_data.php', {\n        params: {\n          userId\n        },\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success) {\n        setUserData({\n          balance: response.data.balance,\n          points: response.data.points,\n          username: response.data.username\n        });\n      } else {\n        console.error('Failed to fetch user data:', response.data.message);\n      }\n    } catch (error) {\n      var _error$response;\n      console.error('Error fetching user data:', error);\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        navigate('/login', {\n          state: {\n            from: location.pathname\n          }\n        });\n      }\n    }\n  }, [token, userId, navigate, location.pathname]);\n  const fetchNotifications = useCallback(async () => {\n    if (!token || !userId) return;\n    try {\n      const response = await axios.get('/backend/handlers/messages.php', {\n        params: {\n          user_id: userId,\n          type: 'unread_count'\n        }\n      });\n      if (response.data.success) {\n        setUnreadMessages(response.data.unread_count);\n      }\n      setPendingRequests(0);\n    } catch (error) {\n      var _error$response2;\n      console.error('Error fetching notifications:', error);\n      if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 401 && !publicRoutes.some(route => location.pathname.startsWith(route))) {\n        navigate('/login', {\n          state: {\n            from: location.pathname\n          }\n        });\n      }\n    }\n  }, [token, userId, navigate, location.pathname, publicRoutes]);\n  useEffect(() => {\n    const isPublicRoute = publicRoutes.some(route => location.pathname.startsWith(route));\n    if (!isPublicRoute && (!token || !userId)) {\n      navigate('/login', {\n        state: {\n          from: location.pathname\n        }\n      });\n      return;\n    }\n    if (!isPublicRoute && token && userId) {\n      fetchUserData();\n      fetchNotifications();\n      const userDataInterval = setInterval(fetchUserData, 30000);\n      const notificationInterval = setInterval(fetchNotifications, 60000);\n      return () => {\n        clearInterval(userDataInterval);\n        clearInterval(notificationInterval);\n      };\n    }\n  }, [token, userId, navigate, location.pathname, fetchUserData, fetchNotifications]);\n  useEffect(() => {\n    if (unreadMessages > prevUnreadCount) {\n      setNotification({\n        message: `You have ${unreadMessages} new message${unreadMessages > 1 ? 's' : ''}`,\n        type: 'info'\n      });\n      setTimeout(() => setNotification(null), 5000);\n    }\n    setPrevUnreadCount(unreadMessages);\n  }, [unreadMessages, prevUnreadCount]);\n  const handleLogout = () => {\n    localStorage.removeItem('userId');\n    localStorage.removeItem('userToken');\n    navigate('/login');\n  };\n  const getPageTitle = () => {\n    const path = location.pathname;\n    if (path === '/user/dashboard') return 'Dashboard';\n    if (path === '/user/profile') return 'Profile';\n    if (path === '/user/bets/outgoing') return 'Outgoing Bets';\n    if (path === '/user/bets/incoming') return 'Incoming Bets';\n    if (path === '/user/bets/accepted') return 'Accepted Bets';\n    if (path === '/user/friends') return 'Friends';\n    if (path === '/user/messages') return 'Messages';\n    if (path === '/user/transfer') return 'Transfer';\n    if (path === '/user/leaderboard') return 'Leaderboard';\n    if (path === '/user/league') return '247 League';\n    if (path === '/user/my-leagues') return 'My Leagues';\n    if (path === '/user/wallet') return 'Wallet';\n    if (path === '/user/settings') return 'Settings';\n    if (path === '/user/friend-requests') return 'Friend Requests';\n    if (path === '/user/challenges') return 'Challenges';\n    if (path === '/user/recent-bets') return 'Recent Bets';\n    return 'Dashboard';\n  };\n  const toggleSidebar = () => {\n    if (isMobileView) {\n      setIsMobileSidebarOpen(!isMobileSidebarOpen);\n    } else {\n      setIsSidebarCollapsed(!isSidebarCollapsed);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `user-layout ${isMobileView ? 'mobile-view' : ''} ${isMobileSidebarOpen ? 'mobile-sidebar-open' : ''}`,\n    children: [isMobileView && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mobile-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"mobile-menu-toggle\",\n        onClick: toggleSidebar,\n        \"aria-label\": \"Toggle menu\",\n        \"aria-expanded\": isMobileSidebarOpen,\n        children: isMobileSidebarOpen ? /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 36\n        }, this) : /*#__PURE__*/_jsxDEV(FaBars, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 50\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mobile-logo\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: \"FanBet247\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mobile-user-info\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mobile-balance\",\n          children: [\"$\", (userData.points || 0).toFixed(2), \" FC\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 9\n    }, this), isMobileView && isMobileSidebarOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-overlay\",\n      onClick: () => setIsMobileSidebarOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n      className: `user-dashboard-sidebar ${isSidebarCollapsed ? 'collapsed' : ''} ${isMobileView ? 'mobile' : ''} ${isMobileSidebarOpen ? 'open' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-dashboard-logo\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            children: sidebarLogo ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: sidebarLogo,\n              alt: \"FanBet247\",\n              className: \"sidebar-logo-img\",\n              style: {\n                maxHeight: isSidebarCollapsed && !isMobileView ? '32px' : '40px',\n                maxWidth: '100%',\n                objectFit: 'contain'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this) : isSidebarCollapsed && !isMobileView ? 'FB' : 'FanBet247'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), !isMobileView && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"sidebar-toggle\",\n          onClick: toggleSidebar,\n          children: isSidebarCollapsed ? /*#__PURE__*/_jsxDEV(FaBars, {\n            className: \"toggle-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(FaChevronLeft, {\n            className: \"toggle-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-dashboard-profile\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-section\",\n          title: username,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            role: \"img\",\n            \"aria-label\": \"User profile\",\n            className: \"profile-icon\",\n            children: \"\\uD83D\\uDC64\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"profile-name\",\n            children: username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 55\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-balance\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"balance-item\",\n            title: \"FanCoin Balance\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              role: \"img\",\n              \"aria-label\": \"Coin\",\n              className: \"balance-icon\",\n              children: \"\\uD83E\\uDE99\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sidebar-balance\",\n              children: [\"$\", (userData.points || 0).toFixed(2), \" FC\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"currency-selector-wrapper\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sidebar-currency-selector\",\n              children: \"USD ($)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"user-dashboard-nav\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"dashboard-nav-list\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/dashboard' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/dashboard\",\n              title: \"Dashboard\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Home\",\n                className: \"nav-icon\",\n                children: \"\\uD83C\\uDFE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 59\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/profile' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/profile\",\n              title: \"Profile\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"User\",\n                className: \"nav-icon\",\n                children: \"\\uD83D\\uDC64\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 59\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/bets/outgoing' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/bets/outgoing\",\n              title: \"Outgoing Bets\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Outbox\",\n                className: \"nav-icon\",\n                children: \"\\uD83D\\uDCE4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Outgoing Bets\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 59\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/challenges' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/challenges\",\n              title: \"Challenges\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Target\",\n                className: \"nav-icon\",\n                children: \"\\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Challenges\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 59\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/recent-bets' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/recent-bets\",\n              title: \"Recent Bets\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Chart\",\n                className: \"nav-icon\",\n                children: \"\\uD83D\\uDCCA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Recent Bets\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 59\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/bets/incoming' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/bets/incoming\",\n              title: \"Incoming Bets\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Inbox\",\n                className: \"nav-icon\",\n                children: \"\\uD83D\\uDCE5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Incoming Bets\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 59\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/bets/accepted' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/bets/accepted\",\n              title: \"Accepted Bets\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Check mark\",\n                className: \"nav-icon\",\n                children: \"\\u2705\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Accepted Bets\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 59\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/friends' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/friends\",\n              title: \"Friends\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"People\",\n                className: \"nav-icon\",\n                children: \"\\uD83D\\uDC65\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Friends\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 59\n              }, this), pendingRequests > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"notification-badge\",\n                children: pendingRequests\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/friend-requests' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/friend-requests\",\n              title: \"Friend Requests\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Bell\",\n                className: \"nav-icon\",\n                children: \"\\uD83D\\uDD14\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Friend Requests\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 59\n              }, this), pendingRequests > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"notification-badge\",\n                children: pendingRequests\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/messages' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/messages\",\n              title: \"Messages\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Speech bubble\",\n                className: \"nav-icon\",\n                children: \"\\uD83D\\uDCAC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"menu-text\",\n                children: \"Messages\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 59\n              }, this), unreadMessages > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"menu-notification\",\n                children: unreadMessages\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/transfer' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/transfer\",\n              title: \"Transfer\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Transfer arrows\",\n                className: \"nav-icon\",\n                children: \"\\u2194\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Transfer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 59\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/leaderboard' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/leaderboard\",\n              title: \"Leaderboard\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Trophy\",\n                className: \"nav-icon\",\n                children: \"\\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Leaderboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 59\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/league' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/league\",\n              title: \"247 League\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Soccer ball\",\n                className: \"nav-icon\",\n                children: \"\\u26BD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"247 League\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 59\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/my-leagues' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/my-leagues\",\n              title: \"My Leagues\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Trophy\",\n                className: \"nav-icon\",\n                children: \"\\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"My Leagues\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 59\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/wallet' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/wallet\",\n              title: \"Credit Wallet\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Wallet\",\n                className: \"nav-icon\",\n                children: \"\\uD83D\\uDC5B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Credit Wallet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 59\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/credit-history' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/credit-history\",\n              title: \"Credit History\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Scroll\",\n                className: \"nav-icon\",\n                children: \"\\uD83D\\uDCDC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Credit History\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 59\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: location.pathname === '/user/settings' ? 'active' : '',\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/settings\",\n              title: \"Settings\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"Gear\",\n                className: \"nav-icon\",\n                children: \"\\u2699\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this), (!isSidebarCollapsed || isMobileView) && /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 59\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleLogout,\n        className: \"sidebar-logout-btn\",\n        title: \"Logout\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          role: \"img\",\n          \"aria-label\": \"Door\",\n          className: \"logout-icon\",\n          children: \"\\uD83D\\uDEAA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), (!isSidebarCollapsed || isMobileView) && 'Logout']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `main-container ${isSidebarCollapsed && !isMobileView ? 'expanded' : ''}`,\n      children: [notification && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `notification-banner ${notification.type}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notification-content\",\n          children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n            className: \"notification-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: notification.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 11\n      }, this), !isMobileView && /*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"dashboard-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-nav\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-left\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"page-title\",\n              children: getPageTitle()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-balances\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"balance-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"balance-label\",\n                  children: \"FanCoin:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"header-balance\",\n                  children: [\"$\", (userData.points || 0).toFixed(2), \" FC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"currency-selector-item\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"header-currency-selector\",\n                  children: \"USD ($)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogout,\n              className: \"nav-logout-btn\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"nav-icon\",\n                children: \"\\uD83D\\uDEAA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this), \"Logout\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-content\",\n        children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n}\n_s(UserLayout, \"/btBtmVpwI755ieQaUMpr+4fSCo=\", false, function () {\n  return [useLocation, useNavigate, useUser];\n});\n_c = UserLayout;\nexport default UserLayout;\nvar _c;\n$RefreshReg$(_c, \"UserLayout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Link", "useLocation", "useNavigate", "Outlet", "axios", "FaEnvelope", "FaBars", "FaChevronLeft", "FaTimes", "useUser", "jsxDEV", "_jsxDEV", "UserLayout", "_s", "location", "navigate", "userData", "setUserData", "isSidebarCollapsed", "setIsSidebarCollapsed", "isMobileSidebarOpen", "setIsMobileSidebarOpen", "isMobile<PERSON>iew", "setIsMobileView", "window", "innerWidth", "unreadMessages", "setUnreadMessages", "pendingRequests", "setPendingRequests", "prevUnreadCount", "setPrevUnreadCount", "notification", "setNotification", "sidebarLogo", "setSidebarLogo", "userId", "localStorage", "getItem", "username", "token", "publicRoutes", "handleResize", "mobile", "addEventListener", "removeEventListener", "pathname", "fetchUserData", "response", "get", "params", "headers", "data", "success", "balance", "points", "console", "error", "message", "_error$response", "status", "state", "from", "fetchNotifications", "user_id", "type", "unread_count", "_error$response2", "some", "route", "startsWith", "isPublicRoute", "userDataInterval", "setInterval", "notificationInterval", "clearInterval", "setTimeout", "handleLogout", "removeItem", "getPageTitle", "path", "toggleSidebar", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "toFixed", "src", "alt", "style", "maxHeight", "max<PERSON><PERSON><PERSON>", "objectFit", "title", "role", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/UserLayout.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { Link, useLocation, useNavigate, Outlet } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport './UserLayoutSidebar.css';\nimport './UserLayout.css';\nimport { FaEnvelope, FaBars, FaChevronLeft, FaTimes } from 'react-icons/fa';\nimport { useUser } from '../context/UserContext';\n// import { CurrencyBalance, CurrencyQuickSelector } from '../components/Currency'; // DISABLED: Currency system being recoded\n\nfunction UserLayout() {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { userData, setUserData } = useUser();\n  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\n  const [isMobileView, setIsMobileView] = useState(window.innerWidth <= 768);\n  const [unreadMessages, setUnreadMessages] = useState(0);\n  const [pendingRequests, setPendingRequests] = useState(0);\n  const [prevUnreadCount, setPrevUnreadCount] = useState(0);\n  const [notification, setNotification] = useState(null);\n  const [sidebarLogo, setSidebarLogo] = useState(null);\n  const userId = localStorage.getItem('userId');\n  const username = localStorage.getItem('username');\n  const token = localStorage.getItem('userToken');\n\n  // List of public routes that don't need auth\n  const publicRoutes = ['/login', '/register', '/', '/about'];\n\n  // Handle window resize\n  useEffect(() => {\n    const handleResize = () => {\n      const mobile = window.innerWidth <= 768;\n      setIsMobileView(mobile);\n      if (!mobile) {\n        setIsMobileSidebarOpen(false);\n      }\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Close mobile sidebar when route changes\n  useEffect(() => {\n    setIsMobileSidebarOpen(false);\n  }, [location.pathname]);\n\n  const fetchUserData = useCallback(async () => {\n    if (!token || !userId) return;\n\n    try {\n      const response = await axios.get('/backend/handlers/user_data.php', {\n        params: { userId },\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      \n      if (response.data.success) {\n        setUserData({\n          balance: response.data.balance,\n          points: response.data.points,\n          username: response.data.username\n        });\n      } else {\n        console.error('Failed to fetch user data:', response.data.message);\n      }\n    } catch (error) {\n      console.error('Error fetching user data:', error);\n      if (error.response?.status === 401) {\n        navigate('/login', { state: { from: location.pathname } });\n      }\n    }\n  }, [token, userId, navigate, location.pathname]);\n\n  const fetchNotifications = useCallback(async () => {\n    if (!token || !userId) return;\n\n    try {\n      const response = await axios.get('/backend/handlers/messages.php', {\n        params: {\n          user_id: userId,\n          type: 'unread_count'\n        }\n      });\n      \n      if (response.data.success) {\n        setUnreadMessages(response.data.unread_count);\n      }\n      \n      setPendingRequests(0);\n    } catch (error) {\n      console.error('Error fetching notifications:', error);\n      if (error.response?.status === 401 && !publicRoutes.some(route => location.pathname.startsWith(route))) {\n        navigate('/login', { state: { from: location.pathname } });\n      }\n    }\n  }, [token, userId, navigate, location.pathname, publicRoutes]);\n\n  useEffect(() => {\n    const isPublicRoute = publicRoutes.some(route => location.pathname.startsWith(route));\n    \n    if (!isPublicRoute && (!token || !userId)) {\n      navigate('/login', { state: { from: location.pathname } });\n      return;\n    }\n\n    if (!isPublicRoute && token && userId) {\n      fetchUserData();\n      fetchNotifications();\n      \n      const userDataInterval = setInterval(fetchUserData, 30000);\n      const notificationInterval = setInterval(fetchNotifications, 60000);\n\n      return () => {\n        clearInterval(userDataInterval);\n        clearInterval(notificationInterval);\n      };\n    }\n  }, [token, userId, navigate, location.pathname, fetchUserData, fetchNotifications]);\n\n  useEffect(() => {\n    if (unreadMessages > prevUnreadCount) {\n      setNotification({\n        message: `You have ${unreadMessages} new message${unreadMessages > 1 ? 's' : ''}`,\n        type: 'info'\n      });\n      setTimeout(() => setNotification(null), 5000);\n    }\n    setPrevUnreadCount(unreadMessages);\n  }, [unreadMessages, prevUnreadCount]);\n\n  const handleLogout = () => {\n    localStorage.removeItem('userId');\n    localStorage.removeItem('userToken');\n    navigate('/login');\n  };\n\n  const getPageTitle = () => {\n    const path = location.pathname;\n    if (path === '/user/dashboard') return 'Dashboard';\n    if (path === '/user/profile') return 'Profile';\n    if (path === '/user/bets/outgoing') return 'Outgoing Bets';\n    if (path === '/user/bets/incoming') return 'Incoming Bets';\n    if (path === '/user/bets/accepted') return 'Accepted Bets';\n    if (path === '/user/friends') return 'Friends';\n    if (path === '/user/messages') return 'Messages';\n    if (path === '/user/transfer') return 'Transfer';\n    if (path === '/user/leaderboard') return 'Leaderboard';\n    if (path === '/user/league') return '247 League';\n    if (path === '/user/my-leagues') return 'My Leagues';\n    if (path === '/user/wallet') return 'Wallet';\n    if (path === '/user/settings') return 'Settings';\n    if (path === '/user/friend-requests') return 'Friend Requests';\n    if (path === '/user/challenges') return 'Challenges';\n    if (path === '/user/recent-bets') return 'Recent Bets';\n    return 'Dashboard';\n  };\n\n  const toggleSidebar = () => {\n    if (isMobileView) {\n      setIsMobileSidebarOpen(!isMobileSidebarOpen);\n    } else {\n      setIsSidebarCollapsed(!isSidebarCollapsed);\n    }\n  };\n\n  return (\n    <div className={`user-layout ${isMobileView ? 'mobile-view' : ''} ${isMobileSidebarOpen ? 'mobile-sidebar-open' : ''}`}>\n      {/* Mobile Header */}\n      {isMobileView && (\n        <div className=\"mobile-header\">\n          <button \n            className=\"mobile-menu-toggle\"\n            onClick={toggleSidebar}\n            aria-label=\"Toggle menu\"\n            aria-expanded={isMobileSidebarOpen}\n          >\n            {isMobileSidebarOpen ? <FaTimes /> : <FaBars />}\n          </button>\n          <div className=\"mobile-logo\">\n            <Link to=\"/\">FanBet247</Link>\n          </div>\n          <div className=\"mobile-user-info\">\n            <span className=\"mobile-balance\">${(userData.points || 0).toFixed(2)} FC</span>\n          </div>\n        </div>\n      )}\n\n      {/* Sidebar Overlay for Mobile */}\n      {isMobileView && isMobileSidebarOpen && (\n        <div className=\"sidebar-overlay\" onClick={() => setIsMobileSidebarOpen(false)} />\n      )}\n\n      <aside className={`user-dashboard-sidebar ${isSidebarCollapsed ? 'collapsed' : ''} ${isMobileView ? 'mobile' : ''} ${isMobileSidebarOpen ? 'open' : ''}`}>\n        <div className=\"sidebar-header\">\n          <div className=\"user-dashboard-logo\">\n            <Link to=\"/\">\n              {sidebarLogo ? (\n                <img\n                  src={sidebarLogo}\n                  alt=\"FanBet247\"\n                  className=\"sidebar-logo-img\"\n                  style={{\n                    maxHeight: isSidebarCollapsed && !isMobileView ? '32px' : '40px',\n                    maxWidth: '100%',\n                    objectFit: 'contain'\n                  }}\n                />\n              ) : (\n                isSidebarCollapsed && !isMobileView ? 'FB' : 'FanBet247'\n              )}\n            </Link>\n          </div>\n          {!isMobileView && (\n            <button className=\"sidebar-toggle\" onClick={toggleSidebar}>\n              {isSidebarCollapsed ? (\n                <FaBars className=\"toggle-icon\" />\n              ) : (\n                <FaChevronLeft className=\"toggle-icon\" />\n              )}\n            </button>\n          )}\n        </div>\n        \n        <div className=\"user-dashboard-profile\">\n          <div className=\"profile-section\" title={username}>\n            <span role=\"img\" aria-label=\"User profile\" className=\"profile-icon\">👤</span>\n            {(!isSidebarCollapsed || isMobileView) && <span className=\"profile-name\">{username}</span>}\n          </div>\n          <div className=\"profile-balance\">\n            <div className=\"balance-item\" title=\"FanCoin Balance\">\n              <span role=\"img\" aria-label=\"Coin\" className=\"balance-icon\">🪙</span>\n              {(!isSidebarCollapsed || isMobileView) && (\n                <span className=\"sidebar-balance\">${(userData.points || 0).toFixed(2)} FC</span>\n              )}\n            </div>\n            {(!isSidebarCollapsed || isMobileView) && (\n              <div className=\"currency-selector-wrapper\">\n                <span className=\"sidebar-currency-selector\">USD ($)</span>\n              </div>\n            )}\n          </div>\n        </div>\n\n        <nav className=\"user-dashboard-nav\">\n          <ul className=\"dashboard-nav-list\">\n            <li className={location.pathname === '/user/dashboard' ? 'active' : ''}>\n              <Link to=\"/user/dashboard\" title=\"Dashboard\">\n                <span role=\"img\" aria-label=\"Home\" className=\"nav-icon\">🏠</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>Dashboard</span>}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/profile' ? 'active' : ''}>\n              <Link to=\"/user/profile\" title=\"Profile\">\n                <span role=\"img\" aria-label=\"User\" className=\"nav-icon\">👤</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>Profile</span>}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/bets/outgoing' ? 'active' : ''}>\n              <Link to=\"/user/bets/outgoing\" title=\"Outgoing Bets\">\n                <span role=\"img\" aria-label=\"Outbox\" className=\"nav-icon\">📤</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>Outgoing Bets</span>}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/challenges' ? 'active' : ''}>\n              <Link to=\"/user/challenges\" title=\"Challenges\">\n                <span role=\"img\" aria-label=\"Target\" className=\"nav-icon\">🎯</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>Challenges</span>}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/recent-bets' ? 'active' : ''}>\n              <Link to=\"/user/recent-bets\" title=\"Recent Bets\">\n                <span role=\"img\" aria-label=\"Chart\" className=\"nav-icon\">📊</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>Recent Bets</span>}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/bets/incoming' ? 'active' : ''}>\n              <Link to=\"/user/bets/incoming\" title=\"Incoming Bets\">\n                <span role=\"img\" aria-label=\"Inbox\" className=\"nav-icon\">📥</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>Incoming Bets</span>}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/bets/accepted' ? 'active' : ''}>\n              <Link to=\"/user/bets/accepted\" title=\"Accepted Bets\">\n                <span role=\"img\" aria-label=\"Check mark\" className=\"nav-icon\">✅</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>Accepted Bets</span>}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/friends' ? 'active' : ''}>\n              <Link to=\"/user/friends\" title=\"Friends\">\n                <span role=\"img\" aria-label=\"People\" className=\"nav-icon\">👥</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>Friends</span>}\n                {pendingRequests > 0 && <span className=\"notification-badge\">{pendingRequests}</span>}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/friend-requests' ? 'active' : ''}>\n              <Link to=\"/user/friend-requests\" title=\"Friend Requests\">\n                <span role=\"img\" aria-label=\"Bell\" className=\"nav-icon\">🔔</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>Friend Requests</span>}\n                {pendingRequests > 0 && (\n                  <span className=\"notification-badge\">{pendingRequests}</span>\n                )}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/messages' ? 'active' : ''}>\n              <Link to=\"/user/messages\" title=\"Messages\">\n                <span role=\"img\" aria-label=\"Speech bubble\" className=\"nav-icon\">💬</span>\n                {(!isSidebarCollapsed || isMobileView) && <span className=\"menu-text\">Messages</span>}\n                {unreadMessages > 0 && (\n                  <span className=\"menu-notification\">{unreadMessages}</span>\n                )}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/transfer' ? 'active' : ''}>\n              <Link to=\"/user/transfer\" title=\"Transfer\">\n                <span role=\"img\" aria-label=\"Transfer arrows\" className=\"nav-icon\">↔️</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>Transfer</span>}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/leaderboard' ? 'active' : ''}>\n              <Link to=\"/user/leaderboard\" title=\"Leaderboard\">\n                <span role=\"img\" aria-label=\"Trophy\" className=\"nav-icon\">🏆</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>Leaderboard</span>}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/league' ? 'active' : ''}>\n              <Link to=\"/user/league\" title=\"247 League\">\n                <span role=\"img\" aria-label=\"Soccer ball\" className=\"nav-icon\">⚽</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>247 League</span>}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/my-leagues' ? 'active' : ''}>\n              <Link to=\"/user/my-leagues\" title=\"My Leagues\">\n                <span role=\"img\" aria-label=\"Trophy\" className=\"nav-icon\">🏆</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>My Leagues</span>}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/wallet' ? 'active' : ''}>\n              <Link to=\"/user/wallet\" title=\"Credit Wallet\">\n                <span role=\"img\" aria-label=\"Wallet\" className=\"nav-icon\">👛</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>Credit Wallet</span>}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/credit-history' ? 'active' : ''}>\n              <Link to=\"/user/credit-history\" title=\"Credit History\">\n                <span role=\"img\" aria-label=\"Scroll\" className=\"nav-icon\">📜</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>Credit History</span>}\n              </Link>\n            </li>\n            <li className={location.pathname === '/user/settings' ? 'active' : ''}>\n              <Link to=\"/user/settings\" title=\"Settings\">\n                <span role=\"img\" aria-label=\"Gear\" className=\"nav-icon\">⚙️</span>\n                {(!isSidebarCollapsed || isMobileView) && <span>Settings</span>}\n              </Link>\n            </li>\n          </ul>\n        </nav>\n\n        <button onClick={handleLogout} className=\"sidebar-logout-btn\" title=\"Logout\">\n          <span role=\"img\" aria-label=\"Door\" className=\"logout-icon\">🚪</span>\n          {(!isSidebarCollapsed || isMobileView) && 'Logout'}\n        </button>\n      </aside>\n\n      <main className={`main-container ${isSidebarCollapsed && !isMobileView ? 'expanded' : ''}`}>\n        {notification && (\n          <div className={`notification-banner ${notification.type}`}>\n            <div className=\"notification-content\">\n              <FaEnvelope className=\"notification-icon\" />\n              <span>{notification.message}</span>\n            </div>\n          </div>\n        )}\n        \n        {!isMobileView && (\n          <header className=\"dashboard-header\">\n            <div className=\"dashboard-nav\">\n              <div className=\"nav-left\">\n                <h1 className=\"page-title\">{getPageTitle()}</h1>\n              </div>\n              <div className=\"nav-right\">\n                <div className=\"user-balances\">\n                  <div className=\"balance-item\">\n                    <span className=\"balance-label\">FanCoin:</span>\n                    <span className=\"header-balance\">${(userData.points || 0).toFixed(2)} FC</span>\n                  </div>\n                  <div className=\"currency-selector-item\">\n                    <span className=\"header-currency-selector\">USD ($)</span>\n                  </div>\n                </div>\n                <button onClick={handleLogout} className=\"nav-logout-btn\">\n                  <i className=\"nav-icon\">🚪</i>\n                  Logout\n                </button>\n              </div>\n            </div>\n          </header>\n        )}\n        \n        <div className=\"dashboard-content\">\n          <Outlet />\n        </div>\n      </main>\n    </div>\n  );\n}\n\nexport default UserLayout;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,EAAEC,MAAM,QAAQ,kBAAkB;AACzE,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAO,yBAAyB;AAChC,OAAO,kBAAkB;AACzB,SAASC,UAAU,EAAEC,MAAM,EAAEC,aAAa,EAAEC,OAAO,QAAQ,gBAAgB;AAC3E,SAASC,OAAO,QAAQ,wBAAwB;AAChD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc,QAAQ;IAAEC;EAAY,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC3C,MAAM,CAACS,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACuB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC2B,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAC1E,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAMuC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAC7C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;EACjD,MAAME,KAAK,GAAGH,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;;EAE/C;EACA,MAAMG,YAAY,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,GAAG,EAAE,QAAQ,CAAC;;EAE3D;EACA3C,SAAS,CAAC,MAAM;IACd,MAAM4C,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,MAAM,GAAGnB,MAAM,CAACC,UAAU,IAAI,GAAG;MACvCF,eAAe,CAACoB,MAAM,CAAC;MACvB,IAAI,CAACA,MAAM,EAAE;QACXtB,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF,CAAC;IAEDG,MAAM,CAACoB,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/C,OAAO,MAAMlB,MAAM,CAACqB,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5C,SAAS,CAAC,MAAM;IACduB,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC,EAAE,CAACP,QAAQ,CAACgC,QAAQ,CAAC,CAAC;EAEvB,MAAMC,aAAa,GAAGhD,WAAW,CAAC,YAAY;IAC5C,IAAI,CAACyC,KAAK,IAAI,CAACJ,MAAM,EAAE;IAEvB,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAM5C,KAAK,CAAC6C,GAAG,CAAC,iCAAiC,EAAE;QAClEC,MAAM,EAAE;UAAEd;QAAO,CAAC;QAClBe,OAAO,EAAE;UACP,eAAe,EAAE,UAAUX,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAIQ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzBpC,WAAW,CAAC;UACVqC,OAAO,EAAEN,QAAQ,CAACI,IAAI,CAACE,OAAO;UAC9BC,MAAM,EAAEP,QAAQ,CAACI,IAAI,CAACG,MAAM;UAC5BhB,QAAQ,EAAES,QAAQ,CAACI,IAAI,CAACb;QAC1B,CAAC,CAAC;MACJ,CAAC,MAAM;QACLiB,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAET,QAAQ,CAACI,IAAI,CAACM,OAAO,CAAC;MACpE;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MAAA,IAAAE,eAAA;MACdH,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,IAAI,EAAAE,eAAA,GAAAF,KAAK,CAACT,QAAQ,cAAAW,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAClC7C,QAAQ,CAAC,QAAQ,EAAE;UAAE8C,KAAK,EAAE;YAAEC,IAAI,EAAEhD,QAAQ,CAACgC;UAAS;QAAE,CAAC,CAAC;MAC5D;IACF;EACF,CAAC,EAAE,CAACN,KAAK,EAAEJ,MAAM,EAAErB,QAAQ,EAAED,QAAQ,CAACgC,QAAQ,CAAC,CAAC;EAEhD,MAAMiB,kBAAkB,GAAGhE,WAAW,CAAC,YAAY;IACjD,IAAI,CAACyC,KAAK,IAAI,CAACJ,MAAM,EAAE;IAEvB,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAM5C,KAAK,CAAC6C,GAAG,CAAC,gCAAgC,EAAE;QACjEC,MAAM,EAAE;UACNc,OAAO,EAAE5B,MAAM;UACf6B,IAAI,EAAE;QACR;MACF,CAAC,CAAC;MAEF,IAAIjB,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzB1B,iBAAiB,CAACqB,QAAQ,CAACI,IAAI,CAACc,YAAY,CAAC;MAC/C;MAEArC,kBAAkB,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MAAA,IAAAU,gBAAA;MACdX,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,IAAI,EAAAU,gBAAA,GAAAV,KAAK,CAACT,QAAQ,cAAAmB,gBAAA,uBAAdA,gBAAA,CAAgBP,MAAM,MAAK,GAAG,IAAI,CAACnB,YAAY,CAAC2B,IAAI,CAACC,KAAK,IAAIvD,QAAQ,CAACgC,QAAQ,CAACwB,UAAU,CAACD,KAAK,CAAC,CAAC,EAAE;QACtGtD,QAAQ,CAAC,QAAQ,EAAE;UAAE8C,KAAK,EAAE;YAAEC,IAAI,EAAEhD,QAAQ,CAACgC;UAAS;QAAE,CAAC,CAAC;MAC5D;IACF;EACF,CAAC,EAAE,CAACN,KAAK,EAAEJ,MAAM,EAAErB,QAAQ,EAAED,QAAQ,CAACgC,QAAQ,EAAEL,YAAY,CAAC,CAAC;EAE9D3C,SAAS,CAAC,MAAM;IACd,MAAMyE,aAAa,GAAG9B,YAAY,CAAC2B,IAAI,CAACC,KAAK,IAAIvD,QAAQ,CAACgC,QAAQ,CAACwB,UAAU,CAACD,KAAK,CAAC,CAAC;IAErF,IAAI,CAACE,aAAa,KAAK,CAAC/B,KAAK,IAAI,CAACJ,MAAM,CAAC,EAAE;MACzCrB,QAAQ,CAAC,QAAQ,EAAE;QAAE8C,KAAK,EAAE;UAAEC,IAAI,EAAEhD,QAAQ,CAACgC;QAAS;MAAE,CAAC,CAAC;MAC1D;IACF;IAEA,IAAI,CAACyB,aAAa,IAAI/B,KAAK,IAAIJ,MAAM,EAAE;MACrCW,aAAa,CAAC,CAAC;MACfgB,kBAAkB,CAAC,CAAC;MAEpB,MAAMS,gBAAgB,GAAGC,WAAW,CAAC1B,aAAa,EAAE,KAAK,CAAC;MAC1D,MAAM2B,oBAAoB,GAAGD,WAAW,CAACV,kBAAkB,EAAE,KAAK,CAAC;MAEnE,OAAO,MAAM;QACXY,aAAa,CAACH,gBAAgB,CAAC;QAC/BG,aAAa,CAACD,oBAAoB,CAAC;MACrC,CAAC;IACH;EACF,CAAC,EAAE,CAAClC,KAAK,EAAEJ,MAAM,EAAErB,QAAQ,EAAED,QAAQ,CAACgC,QAAQ,EAAEC,aAAa,EAAEgB,kBAAkB,CAAC,CAAC;EAEnFjE,SAAS,CAAC,MAAM;IACd,IAAI4B,cAAc,GAAGI,eAAe,EAAE;MACpCG,eAAe,CAAC;QACdyB,OAAO,EAAE,YAAYhC,cAAc,eAAeA,cAAc,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;QACjFuC,IAAI,EAAE;MACR,CAAC,CAAC;MACFW,UAAU,CAAC,MAAM3C,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC/C;IACAF,kBAAkB,CAACL,cAAc,CAAC;EACpC,CAAC,EAAE,CAACA,cAAc,EAAEI,eAAe,CAAC,CAAC;EAErC,MAAM+C,YAAY,GAAGA,CAAA,KAAM;IACzBxC,YAAY,CAACyC,UAAU,CAAC,QAAQ,CAAC;IACjCzC,YAAY,CAACyC,UAAU,CAAC,WAAW,CAAC;IACpC/D,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMgE,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,IAAI,GAAGlE,QAAQ,CAACgC,QAAQ;IAC9B,IAAIkC,IAAI,KAAK,iBAAiB,EAAE,OAAO,WAAW;IAClD,IAAIA,IAAI,KAAK,eAAe,EAAE,OAAO,SAAS;IAC9C,IAAIA,IAAI,KAAK,qBAAqB,EAAE,OAAO,eAAe;IAC1D,IAAIA,IAAI,KAAK,qBAAqB,EAAE,OAAO,eAAe;IAC1D,IAAIA,IAAI,KAAK,qBAAqB,EAAE,OAAO,eAAe;IAC1D,IAAIA,IAAI,KAAK,eAAe,EAAE,OAAO,SAAS;IAC9C,IAAIA,IAAI,KAAK,gBAAgB,EAAE,OAAO,UAAU;IAChD,IAAIA,IAAI,KAAK,gBAAgB,EAAE,OAAO,UAAU;IAChD,IAAIA,IAAI,KAAK,mBAAmB,EAAE,OAAO,aAAa;IACtD,IAAIA,IAAI,KAAK,cAAc,EAAE,OAAO,YAAY;IAChD,IAAIA,IAAI,KAAK,kBAAkB,EAAE,OAAO,YAAY;IACpD,IAAIA,IAAI,KAAK,cAAc,EAAE,OAAO,QAAQ;IAC5C,IAAIA,IAAI,KAAK,gBAAgB,EAAE,OAAO,UAAU;IAChD,IAAIA,IAAI,KAAK,uBAAuB,EAAE,OAAO,iBAAiB;IAC9D,IAAIA,IAAI,KAAK,kBAAkB,EAAE,OAAO,YAAY;IACpD,IAAIA,IAAI,KAAK,mBAAmB,EAAE,OAAO,aAAa;IACtD,OAAO,WAAW;EACpB,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI3D,YAAY,EAAE;MAChBD,sBAAsB,CAAC,CAACD,mBAAmB,CAAC;IAC9C,CAAC,MAAM;MACLD,qBAAqB,CAAC,CAACD,kBAAkB,CAAC;IAC5C;EACF,CAAC;EAED,oBACEP,OAAA;IAAKuE,SAAS,EAAE,eAAe5D,YAAY,GAAG,aAAa,GAAG,EAAE,IAAIF,mBAAmB,GAAG,qBAAqB,GAAG,EAAE,EAAG;IAAA+D,QAAA,GAEpH7D,YAAY,iBACXX,OAAA;MAAKuE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BxE,OAAA;QACEuE,SAAS,EAAC,oBAAoB;QAC9BE,OAAO,EAAEH,aAAc;QACvB,cAAW,aAAa;QACxB,iBAAe7D,mBAAoB;QAAA+D,QAAA,EAElC/D,mBAAmB,gBAAGT,OAAA,CAACH,OAAO;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG7E,OAAA,CAACL,MAAM;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eACT7E,OAAA;QAAKuE,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BxE,OAAA,CAACX,IAAI;UAACyF,EAAE,EAAC,GAAG;UAAAN,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACN7E,OAAA;QAAKuE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BxE,OAAA;UAAMuE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,GAAC,GAAC,EAAC,CAACnE,QAAQ,CAACuC,MAAM,IAAI,CAAC,EAAEmC,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAlE,YAAY,IAAIF,mBAAmB,iBAClCT,OAAA;MAAKuE,SAAS,EAAC,iBAAiB;MAACE,OAAO,EAAEA,CAAA,KAAM/D,sBAAsB,CAAC,KAAK;IAAE;MAAAgE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACjF,eAED7E,OAAA;MAAOuE,SAAS,EAAE,0BAA0BhE,kBAAkB,GAAG,WAAW,GAAG,EAAE,IAAII,YAAY,GAAG,QAAQ,GAAG,EAAE,IAAIF,mBAAmB,GAAG,MAAM,GAAG,EAAE,EAAG;MAAA+D,QAAA,gBACvJxE,OAAA;QAAKuE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BxE,OAAA;UAAKuE,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClCxE,OAAA,CAACX,IAAI;YAACyF,EAAE,EAAC,GAAG;YAAAN,QAAA,EACTjD,WAAW,gBACVvB,OAAA;cACEgF,GAAG,EAAEzD,WAAY;cACjB0D,GAAG,EAAC,WAAW;cACfV,SAAS,EAAC,kBAAkB;cAC5BW,KAAK,EAAE;gBACLC,SAAS,EAAE5E,kBAAkB,IAAI,CAACI,YAAY,GAAG,MAAM,GAAG,MAAM;gBAChEyE,QAAQ,EAAE,MAAM;gBAChBC,SAAS,EAAE;cACb;YAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAEFtE,kBAAkB,IAAI,CAACI,YAAY,GAAG,IAAI,GAAG;UAC9C;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EACL,CAAClE,YAAY,iBACZX,OAAA;UAAQuE,SAAS,EAAC,gBAAgB;UAACE,OAAO,EAAEH,aAAc;UAAAE,QAAA,EACvDjE,kBAAkB,gBACjBP,OAAA,CAACL,MAAM;YAAC4E,SAAS,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAElC7E,OAAA,CAACJ,aAAa;YAAC2E,SAAS,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACzC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN7E,OAAA;QAAKuE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCxE,OAAA;UAAKuE,SAAS,EAAC,iBAAiB;UAACe,KAAK,EAAE1D,QAAS;UAAA4C,QAAA,gBAC/CxE,OAAA;YAAMuF,IAAI,EAAC,KAAK;YAAC,cAAW,cAAc;YAAChB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC5E,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;YAAMuE,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAE5C;UAAQ;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC,eACN7E,OAAA;UAAKuE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BxE,OAAA;YAAKuE,SAAS,EAAC,cAAc;YAACe,KAAK,EAAC,iBAAiB;YAAAd,QAAA,gBACnDxE,OAAA;cAAMuF,IAAI,EAAC,KAAK;cAAC,cAAW,MAAM;cAAChB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACpE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBACnCX,OAAA;cAAMuE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAAC,GAAC,EAAC,CAACnE,QAAQ,CAACuC,MAAM,IAAI,CAAC,EAAEmC,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAChF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EACL,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBACnCX,OAAA;YAAKuE,SAAS,EAAC,2BAA2B;YAAAC,QAAA,eACxCxE,OAAA;cAAMuE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7E,OAAA;QAAKuE,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjCxE,OAAA;UAAIuE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAChCxE,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,iBAAiB,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eACrExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,iBAAiB;cAACQ,KAAK,EAAC,WAAW;cAAAd,QAAA,gBAC1CxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,MAAM;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAChE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eACnExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,eAAe;cAACQ,KAAK,EAAC,SAAS;cAAAd,QAAA,gBACtCxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,MAAM;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAChE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,qBAAqB,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eACzExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,qBAAqB;cAACQ,KAAK,EAAC,eAAe;cAAAd,QAAA,gBAClDxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,QAAQ;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAClE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,kBAAkB,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eACtExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,kBAAkB;cAACQ,KAAK,EAAC,YAAY;cAAAd,QAAA,gBAC5CxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,QAAQ;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAClE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,mBAAmB,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eACvExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,mBAAmB;cAACQ,KAAK,EAAC,aAAa;cAAAd,QAAA,gBAC9CxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,OAAO;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACjE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,qBAAqB,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eACzExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,qBAAqB;cAACQ,KAAK,EAAC,eAAe;cAAAd,QAAA,gBAClDxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,OAAO;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACjE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,qBAAqB,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eACzExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,qBAAqB;cAACQ,KAAK,EAAC,eAAe;cAAAd,QAAA,gBAClDxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,YAAY;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACrE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eACnExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,eAAe;cAACQ,KAAK,EAAC,SAAS;cAAAd,QAAA,gBACtCxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,QAAQ;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAClE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC7D5D,eAAe,GAAG,CAAC,iBAAIjB,OAAA;gBAAMuE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEvD;cAAe;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,uBAAuB,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eAC3ExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,uBAAuB;cAACQ,KAAK,EAAC,iBAAiB;cAAAd,QAAA,gBACtDxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,MAAM;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAChE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACrE5D,eAAe,GAAG,CAAC,iBAClBjB,OAAA;gBAAMuE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEvD;cAAe;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAC7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,gBAAgB,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eACpExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,gBAAgB;cAACQ,KAAK,EAAC,UAAU;cAAAd,QAAA,gBACxCxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,eAAe;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACzE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAMuE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACpF9D,cAAc,GAAG,CAAC,iBACjBf,OAAA;gBAAMuE,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAEzD;cAAc;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAC3D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,gBAAgB,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eACpExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,gBAAgB;cAACQ,KAAK,EAAC,UAAU;cAAAd,QAAA,gBACxCxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,iBAAiB;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC3E,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,mBAAmB,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eACvExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,mBAAmB;cAACQ,KAAK,EAAC,aAAa;cAAAd,QAAA,gBAC9CxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,QAAQ;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAClE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eAClExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,cAAc;cAACQ,KAAK,EAAC,YAAY;cAAAd,QAAA,gBACxCxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,aAAa;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACtE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,kBAAkB,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eACtExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,kBAAkB;cAACQ,KAAK,EAAC,YAAY;cAAAd,QAAA,gBAC5CxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,QAAQ;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAClE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eAClExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,cAAc;cAACQ,KAAK,EAAC,eAAe;cAAAd,QAAA,gBAC3CxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,QAAQ;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAClE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,sBAAsB,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eAC1ExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,sBAAsB;cAACQ,KAAK,EAAC,gBAAgB;cAAAd,QAAA,gBACpDxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,QAAQ;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAClE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL7E,OAAA;YAAIuE,SAAS,EAAEpE,QAAQ,CAACgC,QAAQ,KAAK,gBAAgB,GAAG,QAAQ,GAAG,EAAG;YAAAqC,QAAA,eACpExE,OAAA,CAACX,IAAI;cAACyF,EAAE,EAAC,gBAAgB;cAACQ,KAAK,EAAC,UAAU;cAAAd,QAAA,gBACxCxE,OAAA;gBAAMuF,IAAI,EAAC,KAAK;gBAAC,cAAW,MAAM;gBAAChB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAChE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,kBAAKX,OAAA;gBAAAwE,QAAA,EAAM;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEN7E,OAAA;QAAQyE,OAAO,EAAEP,YAAa;QAACK,SAAS,EAAC,oBAAoB;QAACe,KAAK,EAAC,QAAQ;QAAAd,QAAA,gBAC1ExE,OAAA;UAAMuF,IAAI,EAAC,KAAK;UAAC,cAAW,MAAM;UAAChB,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACnE,CAAC,CAACtE,kBAAkB,IAAII,YAAY,KAAK,QAAQ;MAAA;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAER7E,OAAA;MAAMuE,SAAS,EAAE,kBAAkBhE,kBAAkB,IAAI,CAACI,YAAY,GAAG,UAAU,GAAG,EAAE,EAAG;MAAA6D,QAAA,GACxFnD,YAAY,iBACXrB,OAAA;QAAKuE,SAAS,EAAE,uBAAuBlD,YAAY,CAACiC,IAAI,EAAG;QAAAkB,QAAA,eACzDxE,OAAA;UAAKuE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCxE,OAAA,CAACN,UAAU;YAAC6E,SAAS,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5C7E,OAAA;YAAAwE,QAAA,EAAOnD,YAAY,CAAC0B;UAAO;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEA,CAAClE,YAAY,iBACZX,OAAA;QAAQuE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAClCxE,OAAA;UAAKuE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BxE,OAAA;YAAKuE,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBxE,OAAA;cAAIuE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEJ,YAAY,CAAC;YAAC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACN7E,OAAA;YAAKuE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBxE,OAAA;cAAKuE,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BxE,OAAA;gBAAKuE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BxE,OAAA;kBAAMuE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/C7E,OAAA;kBAAMuE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAC,GAAC,EAAC,CAACnE,QAAQ,CAACuC,MAAM,IAAI,CAAC,EAAEmC,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;gBAAA;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACN7E,OAAA;gBAAKuE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrCxE,OAAA;kBAAMuE,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7E,OAAA;cAAQyE,OAAO,EAAEP,YAAa;cAACK,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBACvDxE,OAAA;gBAAGuE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,UAEhC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACT,eAED7E,OAAA;QAAKuE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCxE,OAAA,CAACR,MAAM;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAC3E,EAAA,CA9YQD,UAAU;EAAA,QACAX,WAAW,EACXC,WAAW,EACMO,OAAO;AAAA;AAAA0F,EAAA,GAHlCvF,UAAU;AAgZnB,eAAeA,UAAU;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}