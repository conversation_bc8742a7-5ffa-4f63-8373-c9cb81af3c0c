{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\GeneralSettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCog, FaCheck, FaTimes, FaSave, FaUpload } from 'react-icons/fa';\nimport { useSiteConfig } from '../contexts/SiteConfigContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction GeneralSettings() {\n  _s();\n  const {\n    refreshConfig\n  } = useSiteConfig();\n  const [settings, setSettings] = useState({\n    site_name: '',\n    site_logo: '',\n    contact_email: '',\n    contact_phone: '',\n    facebook_url: '',\n    twitter_url: '',\n    instagram_url: '',\n    about_text: '',\n    terms_conditions: '',\n    privacy_policy: '',\n    footer_text: ''\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [logoPreview, setLogoPreview] = useState('');\n  const [logoFile, setLogoFile] = useState(null);\n  const [faviconPreview, setFaviconPreview] = useState('');\n  const [faviconFile, setFaviconFile] = useState(null);\n  const [testingSmtp, setTestingSmtp] = useState(false);\n  useEffect(() => {\n    fetchSettings();\n  }, []);\n  const fetchSettings = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_general_settings.php`);\n      if (response.data.success && response.data.settings) {\n        const formattedSettings = {};\n        Object.keys(response.data.settings).forEach(key => {\n          formattedSettings[key] = response.data.settings[key].value;\n        });\n        setSettings(formattedSettings);\n\n        // Set logo preview\n        if (formattedSettings.site_logo) {\n          setLogoPreview(`${API_BASE_URL}/${formattedSettings.site_logo}`);\n        }\n      }\n    } catch (err) {\n      setError('Failed to load settings');\n      console.error('Error fetching settings:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setSettings(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleLogoChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];\n      if (!allowedTypes.includes(file.type)) {\n        setError('Please select a valid image file (JPG, PNG, or SVG)');\n        return;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        setError('File size must be less than 5MB');\n        return;\n      }\n      setLogoFile(file);\n      setLogoPreview(URL.createObjectURL(file));\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      setSaving(true);\n      setError('');\n      setSuccess('');\n\n      // First upload logo if a new one is selected\n      let updatedSettings = {\n        ...settings\n      };\n      if (logoFile) {\n        const formData = new FormData();\n        formData.append('logo', logoFile);\n        const uploadResponse = await axios.post(`${API_BASE_URL}/handlers/upload_logo.php`, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        if (uploadResponse.data.success) {\n          updatedSettings.site_logo = uploadResponse.data.file_path;\n        } else {\n          throw new Error(uploadResponse.data.message || 'Failed to upload logo');\n        }\n      }\n\n      // Then update settings\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_general_settings.php`, {\n        settings: updatedSettings\n      });\n      if (response.data.success) {\n        setSuccess('Settings saved successfully!');\n        setTimeout(() => setSuccess(''), 3000);\n        // Update settings state with new logo path\n        setSettings(updatedSettings);\n        // Refresh site config to update throughout the app\n        refreshConfig();\n      } else {\n        throw new Error(response.data.message || 'Failed to save general settings');\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to save settings. Please try again.');\n      console.error('Error saving settings:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n  const testSmtpConnection = async () => {\n    try {\n      setTestingSmtp(true);\n      setError('');\n      setSuccess('');\n      const response = await axios.post(`${API_BASE_URL}/handlers/test_smtp.php`, {\n        host: 'mail.hostinger.com',\n        port: 465,\n        username: '<EMAIL>',\n        password: 'Money2025@Demo#',\n        encryption: 'ssl',\n        from_email: '<EMAIL>',\n        from_name: 'FanBet247'\n      });\n      if (response.data.success) {\n        setSuccess('SMTP connection test successful!');\n      } else {\n        setError(response.data.message || 'SMTP connection test failed');\n      }\n    } catch (err) {\n      setError('Failed to test SMTP connection');\n      console.error('Error testing SMTP:', err);\n    } finally {\n      setTestingSmtp(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg text-gray-600\",\n          children: \"Loading settings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-800 flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(FaCog, {\n          className: \"text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 21\n        }, this), \"General Settings\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mt-2\",\n        children: \"Configure basic system settings like site name, logo, and contact information.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n        className: \"text-red-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-red-700\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n        className: \"text-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-green-700\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"admin-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Basic Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"site_name\",\n            children: \"Site Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"site_name\",\n            name: \"site_name\",\n            value: settings.site_name,\n            onChange: handleInputChange,\n            required: true,\n            placeholder: \"FanBet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"site_logo\",\n            children: \"Site Logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-upload-container\",\n            children: [logoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-preview\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: logoPreview,\n                alt: \"Site Logo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-upload\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"logo_file\",\n                className: \"upload-button\",\n                children: [/*#__PURE__*/_jsxDEV(FaUpload, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this), \" Upload Logo\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"logo_file\",\n                accept: \"image/*\",\n                onChange: handleLogoChange,\n                style: {\n                  display: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Contact Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"contact_email\",\n            children: \"Contact Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"contact_email\",\n            name: \"contact_email\",\n            value: settings.contact_email,\n            onChange: handleInputChange,\n            required: true,\n            placeholder: \"<EMAIL>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"contact_phone\",\n            children: \"Contact Phone\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"contact_phone\",\n            name: \"contact_phone\",\n            value: settings.contact_phone,\n            onChange: handleInputChange,\n            placeholder: \"+1234567890\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Social Media\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"facebook_url\",\n            children: \"Facebook URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"url\",\n            id: \"facebook_url\",\n            name: \"facebook_url\",\n            value: settings.facebook_url,\n            onChange: handleInputChange,\n            placeholder: \"https://facebook.com/fanbet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"twitter_url\",\n            children: \"Twitter URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"url\",\n            id: \"twitter_url\",\n            name: \"twitter_url\",\n            value: settings.twitter_url,\n            onChange: handleInputChange,\n            placeholder: \"https://twitter.com/fanbet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"instagram_url\",\n            children: \"Instagram URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"url\",\n            id: \"instagram_url\",\n            name: \"instagram_url\",\n            value: settings.instagram_url,\n            onChange: handleInputChange,\n            placeholder: \"https://instagram.com/fanbet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Content\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"about_text\",\n            children: \"About Text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"about_text\",\n            name: \"about_text\",\n            value: settings.about_text,\n            onChange: handleInputChange,\n            rows: \"4\",\n            placeholder: \"About FanBet247...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"terms_conditions\",\n            children: \"Terms & Conditions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"terms_conditions\",\n            name: \"terms_conditions\",\n            value: settings.terms_conditions,\n            onChange: handleInputChange,\n            rows: \"6\",\n            placeholder: \"Terms and conditions text goes here...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"privacy_policy\",\n            children: \"Privacy Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"privacy_policy\",\n            name: \"privacy_policy\",\n            value: settings.privacy_policy,\n            onChange: handleInputChange,\n            rows: \"6\",\n            placeholder: \"Privacy policy text goes here...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"footer_text\",\n            children: \"Footer Text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"footer_text\",\n            name: \"footer_text\",\n            value: settings.footer_text,\n            onChange: handleInputChange,\n            placeholder: \"\\xA9 2024 FanBet247. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"SMTP Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Test the SMTP connection with the configured settings (mail.hostinger.com:465)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: testSmtpConnection,\n          className: \"btn btn-secondary\",\n          disabled: testingSmtp,\n          children: testingSmtp ? 'Testing...' : 'Test SMTP Connection'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: saving,\n          children: saving ? 'Saving...' : 'Save Settings'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 9\n  }, this);\n}\n_s(GeneralSettings, \"Ooy3NJXohrVCYBi0iGPiuyNs4O4=\", false, function () {\n  return [useSiteConfig];\n});\n_c = GeneralSettings;\n;\nexport default GeneralSettings;\nvar _c;\n$RefreshReg$(_c, \"GeneralSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaCog", "FaCheck", "FaTimes", "FaSave", "FaUpload", "useSiteConfig", "jsxDEV", "_jsxDEV", "API_BASE_URL", "GeneralSettings", "_s", "refreshConfig", "settings", "setSettings", "site_name", "site_logo", "contact_email", "contact_phone", "facebook_url", "twitter_url", "instagram_url", "about_text", "terms_conditions", "privacy_policy", "footer_text", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "logoPreview", "setLogoPreview", "logoFile", "setLogoFile", "faviconPreview", "setFaviconPreview", "faviconFile", "setFaviconFile", "testingSmtp", "setTestingSmtp", "fetchSettings", "response", "get", "data", "formattedSettings", "Object", "keys", "for<PERSON>ach", "key", "value", "err", "console", "handleInputChange", "e", "name", "target", "prev", "handleLogoChange", "file", "files", "allowedTypes", "includes", "type", "size", "URL", "createObjectURL", "handleSubmit", "preventDefault", "updatedSettings", "formData", "FormData", "append", "uploadResponse", "post", "headers", "file_path", "Error", "message", "setTimeout", "testSmtpConnection", "host", "port", "username", "password", "encryption", "from_email", "from_name", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "id", "onChange", "required", "placeholder", "src", "alt", "accept", "style", "display", "rows", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/GeneralSettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCog, FaCheck, FaTimes, FaSave, FaUpload } from 'react-icons/fa';\nimport { useSiteConfig } from '../contexts/SiteConfigContext';\n\nconst API_BASE_URL = '/backend';\n\nfunction GeneralSettings() {\n    const { refreshConfig } = useSiteConfig();\n    const [settings, setSettings] = useState({\n        site_name: '',\n        site_logo: '',\n        contact_email: '',\n        contact_phone: '',\n        facebook_url: '',\n        twitter_url: '',\n        instagram_url: '',\n        about_text: '',\n        terms_conditions: '',\n        privacy_policy: '',\n        footer_text: ''\n    });\n    const [loading, setLoading] = useState(true);\n    const [saving, setSaving] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [logoPreview, setLogoPreview] = useState('');\n    const [logoFile, setLogoFile] = useState(null);\n    const [faviconPreview, setFaviconPreview] = useState('');\n    const [faviconFile, setFaviconFile] = useState(null);\n    const [testingSmtp, setTestingSmtp] = useState(false);\n\n    useEffect(() => {\n        fetchSettings();\n    }, []);\n\n    const fetchSettings = async () => {\n        try {\n            setLoading(true);\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_general_settings.php`);\n\n            if (response.data.success && response.data.settings) {\n                const formattedSettings = {};\n                Object.keys(response.data.settings).forEach(key => {\n                    formattedSettings[key] = response.data.settings[key].value;\n                });\n                setSettings(formattedSettings);\n\n                // Set logo preview\n                if (formattedSettings.site_logo) {\n                    setLogoPreview(`${API_BASE_URL}/${formattedSettings.site_logo}`);\n                }\n            }\n        } catch (err) {\n            setError('Failed to load settings');\n            console.error('Error fetching settings:', err);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setSettings(prev => ({\n            ...prev,\n            [name]: value\n        }));\n    };\n\n    const handleLogoChange = (e) => {\n        const file = e.target.files[0];\n        if (file) {\n            // Validate file type\n            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];\n            if (!allowedTypes.includes(file.type)) {\n                setError('Please select a valid image file (JPG, PNG, or SVG)');\n                return;\n            }\n\n            // Validate file size (max 5MB)\n            if (file.size > 5 * 1024 * 1024) {\n                setError('File size must be less than 5MB');\n                return;\n            }\n\n            setLogoFile(file);\n            setLogoPreview(URL.createObjectURL(file));\n        }\n    };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      setSaving(true);\n      setError('');\n      setSuccess('');\n\n      // First upload logo if a new one is selected\n      let updatedSettings = { ...settings };\n      \n      if (logoFile) {\n        const formData = new FormData();\n        formData.append('logo', logoFile);\n        \n        const uploadResponse = await axios.post(`${API_BASE_URL}/handlers/upload_logo.php`, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        \n        if (uploadResponse.data.success) {\n          updatedSettings.site_logo = uploadResponse.data.file_path;\n        } else {\n          throw new Error(uploadResponse.data.message || 'Failed to upload logo');\n        }\n      }\n\n      // Then update settings\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_general_settings.php`, {\n        settings: updatedSettings\n      });\n\n      if (response.data.success) {\n        setSuccess('Settings saved successfully!');\n        setTimeout(() => setSuccess(''), 3000);\n        // Update settings state with new logo path\n        setSettings(updatedSettings);\n        // Refresh site config to update throughout the app\n        refreshConfig();\n      } else {\n        throw new Error(response.data.message || 'Failed to save general settings');\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to save settings. Please try again.');\n      console.error('Error saving settings:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const testSmtpConnection = async () => {\n    try {\n      setTestingSmtp(true);\n      setError('');\n      setSuccess('');\n\n      const response = await axios.post(`${API_BASE_URL}/handlers/test_smtp.php`, {\n        host: 'mail.hostinger.com',\n        port: 465,\n        username: '<EMAIL>',\n        password: 'Money2025@Demo#',\n        encryption: 'ssl',\n        from_email: '<EMAIL>',\n        from_name: 'FanBet247'\n      });\n\n      if (response.data.success) {\n        setSuccess('SMTP connection test successful!');\n      } else {\n        setError(response.data.message || 'SMTP connection test failed');\n      }\n    } catch (err) {\n      setError('Failed to test SMTP connection');\n      console.error('Error testing SMTP:', err);\n    } finally {\n      setTestingSmtp(false);\n    }\n  };\n\n    if (loading) {\n        return (\n            <div className=\"p-6\">\n                <div className=\"flex items-center justify-center h-64\">\n                    <div className=\"text-lg text-gray-600\">Loading settings...</div>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"p-6\">\n            {/* Header */}\n            <div className=\"mb-8\">\n                <h1 className=\"text-2xl font-bold text-gray-800 flex items-center gap-3\">\n                    <FaCog className=\"text-blue-500\" />\n                    General Settings\n                </h1>\n                <p className=\"text-gray-600 mt-2\">\n                    Configure basic system settings like site name, logo, and contact information.\n                </p>\n            </div>\n\n            {/* Alerts */}\n            {error && (\n                <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\">\n                    <FaTimes className=\"text-red-500\" />\n                    <span className=\"text-red-700\">{error}</span>\n                </div>\n            )}\n\n            {success && (\n                <div className=\"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\">\n                    <FaCheck className=\"text-green-500\" />\n                    <span className=\"text-green-700\">{success}</span>\n                </div>\n            )}\n\n        <form onSubmit={handleSubmit} className=\"admin-form\">\n          <div className=\"form-section\">\n            <h2>Basic Information</h2>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"site_name\">Site Name</label>\n              <input\n                type=\"text\"\n                id=\"site_name\"\n                name=\"site_name\"\n                value={settings.site_name}\n                onChange={handleInputChange}\n                required\n                placeholder=\"FanBet247\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"site_logo\">Site Logo</label>\n              <div className=\"logo-upload-container\">\n                {logoPreview && (\n                  <div className=\"logo-preview\">\n                    <img src={logoPreview} alt=\"Site Logo\" />\n                  </div>\n                )}\n                <div className=\"logo-upload\">\n                  <label htmlFor=\"logo_file\" className=\"upload-button\">\n                    <FaUpload /> Upload Logo\n                  </label>\n                  <input\n                    type=\"file\"\n                    id=\"logo_file\"\n                    accept=\"image/*\"\n                    onChange={handleLogoChange}\n                    style={{ display: 'none' }}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h2>Contact Information</h2>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"contact_email\">Contact Email</label>\n              <input\n                type=\"email\"\n                id=\"contact_email\"\n                name=\"contact_email\"\n                value={settings.contact_email}\n                onChange={handleInputChange}\n                required\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"contact_phone\">Contact Phone</label>\n              <input\n                type=\"text\"\n                id=\"contact_phone\"\n                name=\"contact_phone\"\n                value={settings.contact_phone}\n                onChange={handleInputChange}\n                placeholder=\"+1234567890\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h2>Social Media</h2>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"facebook_url\">Facebook URL</label>\n              <input\n                type=\"url\"\n                id=\"facebook_url\"\n                name=\"facebook_url\"\n                value={settings.facebook_url}\n                onChange={handleInputChange}\n                placeholder=\"https://facebook.com/fanbet247\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"twitter_url\">Twitter URL</label>\n              <input\n                type=\"url\"\n                id=\"twitter_url\"\n                name=\"twitter_url\"\n                value={settings.twitter_url}\n                onChange={handleInputChange}\n                placeholder=\"https://twitter.com/fanbet247\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"instagram_url\">Instagram URL</label>\n              <input\n                type=\"url\"\n                id=\"instagram_url\"\n                name=\"instagram_url\"\n                value={settings.instagram_url}\n                onChange={handleInputChange}\n                placeholder=\"https://instagram.com/fanbet247\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h2>Content</h2>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"about_text\">About Text</label>\n              <textarea\n                id=\"about_text\"\n                name=\"about_text\"\n                value={settings.about_text}\n                onChange={handleInputChange}\n                rows=\"4\"\n                placeholder=\"About FanBet247...\"\n              ></textarea>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"terms_conditions\">Terms & Conditions</label>\n              <textarea\n                id=\"terms_conditions\"\n                name=\"terms_conditions\"\n                value={settings.terms_conditions}\n                onChange={handleInputChange}\n                rows=\"6\"\n                placeholder=\"Terms and conditions text goes here...\"\n              ></textarea>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"privacy_policy\">Privacy Policy</label>\n              <textarea\n                id=\"privacy_policy\"\n                name=\"privacy_policy\"\n                value={settings.privacy_policy}\n                onChange={handleInputChange}\n                rows=\"6\"\n                placeholder=\"Privacy policy text goes here...\"\n              ></textarea>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"footer_text\">Footer Text</label>\n              <input\n                type=\"text\"\n                id=\"footer_text\"\n                name=\"footer_text\"\n                value={settings.footer_text}\n                onChange={handleInputChange}\n                placeholder=\"© 2024 FanBet247. All rights reserved.\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-section\">\n            <h2>SMTP Test</h2>\n            <p>Test the SMTP connection with the configured settings (mail.hostinger.com:465)</p>\n            <button\n              type=\"button\"\n              onClick={testSmtpConnection}\n              className=\"btn btn-secondary\"\n              disabled={testingSmtp}\n            >\n              {testingSmtp ? 'Testing...' : 'Test SMTP Connection'}\n            </button>\n          </div>\n\n          <div className=\"form-actions\">\n            <button\n              type=\"submit\"\n              className=\"btn btn-primary\"\n              disabled={saving}\n            >\n              {saving ? 'Saving...' : 'Save Settings'}\n            </button>\n          </div>\n        </form>\n    </div>\n  );\n};\n\nexport default GeneralSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AAC1E,SAASC,aAAa,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAc,CAAC,GAAGN,aAAa,CAAC,CAAC;EACzC,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC;IACrCiB,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,UAAU,EAAE,EAAE;IACdC,gBAAgB,EAAE,EAAE;IACpBC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAErDC,SAAS,CAAC,MAAM;IACZ6C,aAAa,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACAjB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkB,QAAQ,GAAG,MAAM7C,KAAK,CAAC8C,GAAG,CAAC,GAAGrC,YAAY,oCAAoC,CAAC;MAErF,IAAIoC,QAAQ,CAACE,IAAI,CAACf,OAAO,IAAIa,QAAQ,CAACE,IAAI,CAAClC,QAAQ,EAAE;QACjD,MAAMmC,iBAAiB,GAAG,CAAC,CAAC;QAC5BC,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACE,IAAI,CAAClC,QAAQ,CAAC,CAACsC,OAAO,CAACC,GAAG,IAAI;UAC/CJ,iBAAiB,CAACI,GAAG,CAAC,GAAGP,QAAQ,CAACE,IAAI,CAAClC,QAAQ,CAACuC,GAAG,CAAC,CAACC,KAAK;QAC9D,CAAC,CAAC;QACFvC,WAAW,CAACkC,iBAAiB,CAAC;;QAE9B;QACA,IAAIA,iBAAiB,CAAChC,SAAS,EAAE;UAC7BmB,cAAc,CAAC,GAAG1B,YAAY,IAAIuC,iBAAiB,CAAChC,SAAS,EAAE,CAAC;QACpE;MACJ;IACJ,CAAC,CAAC,OAAOsC,GAAG,EAAE;MACVvB,QAAQ,CAAC,yBAAyB,CAAC;MACnCwB,OAAO,CAACzB,KAAK,CAAC,0BAA0B,EAAEwB,GAAG,CAAC;IAClD,CAAC,SAAS;MACN3B,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM6B,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEL;IAAM,CAAC,GAAGI,CAAC,CAACE,MAAM;IAChC7C,WAAW,CAAC8C,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACF,IAAI,GAAGL;IACZ,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMQ,gBAAgB,GAAIJ,CAAC,IAAK;IAC5B,MAAMK,IAAI,GAAGL,CAAC,CAACE,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACN;MACA,MAAME,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,CAAC;MAC9E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;QACnCnC,QAAQ,CAAC,qDAAqD,CAAC;QAC/D;MACJ;;MAEA;MACA,IAAI+B,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC7BpC,QAAQ,CAAC,iCAAiC,CAAC;QAC3C;MACJ;MAEAM,WAAW,CAACyB,IAAI,CAAC;MACjB3B,cAAc,CAACiC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC,CAAC;IAC7C;EACJ,CAAC;EAEH,MAAMQ,YAAY,GAAG,MAAOb,CAAC,IAAK;IAChCA,CAAC,CAACc,cAAc,CAAC,CAAC;IAClB,IAAI;MACF1C,SAAS,CAAC,IAAI,CAAC;MACfE,QAAQ,CAAC,EAAE,CAAC;MACZE,UAAU,CAAC,EAAE,CAAC;;MAEd;MACA,IAAIuC,eAAe,GAAG;QAAE,GAAG3D;MAAS,CAAC;MAErC,IAAIuB,QAAQ,EAAE;QACZ,MAAMqC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEvC,QAAQ,CAAC;QAEjC,MAAMwC,cAAc,GAAG,MAAM5E,KAAK,CAAC6E,IAAI,CAAC,GAAGpE,YAAY,2BAA2B,EAAEgE,QAAQ,EAAE;UAC5FK,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIF,cAAc,CAAC7B,IAAI,CAACf,OAAO,EAAE;UAC/BwC,eAAe,CAACxD,SAAS,GAAG4D,cAAc,CAAC7B,IAAI,CAACgC,SAAS;QAC3D,CAAC,MAAM;UACL,MAAM,IAAIC,KAAK,CAACJ,cAAc,CAAC7B,IAAI,CAACkC,OAAO,IAAI,uBAAuB,CAAC;QACzE;MACF;;MAEA;MACA,MAAMpC,QAAQ,GAAG,MAAM7C,KAAK,CAAC6E,IAAI,CAAC,GAAGpE,YAAY,uCAAuC,EAAE;QACxFI,QAAQ,EAAE2D;MACZ,CAAC,CAAC;MAEF,IAAI3B,QAAQ,CAACE,IAAI,CAACf,OAAO,EAAE;QACzBC,UAAU,CAAC,8BAA8B,CAAC;QAC1CiD,UAAU,CAAC,MAAMjD,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QACtC;QACAnB,WAAW,CAAC0D,eAAe,CAAC;QAC5B;QACA5D,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL,MAAM,IAAIoE,KAAK,CAACnC,QAAQ,CAACE,IAAI,CAACkC,OAAO,IAAI,iCAAiC,CAAC;MAC7E;IACF,CAAC,CAAC,OAAO3B,GAAG,EAAE;MACZvB,QAAQ,CAACuB,GAAG,CAAC2B,OAAO,IAAI,4CAA4C,CAAC;MACrE1B,OAAO,CAACzB,KAAK,CAAC,wBAAwB,EAAEwB,GAAG,CAAC;IAC9C,CAAC,SAAS;MACRzB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMsD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFxC,cAAc,CAAC,IAAI,CAAC;MACpBZ,QAAQ,CAAC,EAAE,CAAC;MACZE,UAAU,CAAC,EAAE,CAAC;MAEd,MAAMY,QAAQ,GAAG,MAAM7C,KAAK,CAAC6E,IAAI,CAAC,GAAGpE,YAAY,yBAAyB,EAAE;QAC1E2E,IAAI,EAAE,oBAAoB;QAC1BC,IAAI,EAAE,GAAG;QACTC,QAAQ,EAAE,2BAA2B;QACrCC,QAAQ,EAAE,iBAAiB;QAC3BC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE,2BAA2B;QACvCC,SAAS,EAAE;MACb,CAAC,CAAC;MAEF,IAAI7C,QAAQ,CAACE,IAAI,CAACf,OAAO,EAAE;QACzBC,UAAU,CAAC,kCAAkC,CAAC;MAChD,CAAC,MAAM;QACLF,QAAQ,CAACc,QAAQ,CAACE,IAAI,CAACkC,OAAO,IAAI,6BAA6B,CAAC;MAClE;IACF,CAAC,CAAC,OAAO3B,GAAG,EAAE;MACZvB,QAAQ,CAAC,gCAAgC,CAAC;MAC1CwB,OAAO,CAACzB,KAAK,CAAC,qBAAqB,EAAEwB,GAAG,CAAC;IAC3C,CAAC,SAAS;MACRX,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAEC,IAAIjB,OAAO,EAAE;IACT,oBACIlB,OAAA;MAAKmF,SAAS,EAAC,KAAK;MAAAC,QAAA,eAChBpF,OAAA;QAAKmF,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eAClDpF,OAAA;UAAKmF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIxF,OAAA;IAAKmF,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAEhBpF,OAAA;MAAKmF,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBpF,OAAA;QAAImF,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACpEpF,OAAA,CAACP,KAAK;UAAC0F,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLxF,OAAA;QAAGmF,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLlE,KAAK,iBACFtB,OAAA;MAAKmF,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBACxFpF,OAAA,CAACL,OAAO;QAACwF,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpCxF,OAAA;QAAMmF,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAE9D;MAAK;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CACR,EAEAhE,OAAO,iBACJxB,OAAA;MAAKmF,SAAS,EAAC,iFAAiF;MAAAC,QAAA,gBAC5FpF,OAAA,CAACN,OAAO;QAACyF,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtCxF,OAAA;QAAMmF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAE5D;MAAO;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CACR,eAELxF,OAAA;MAAMyF,QAAQ,EAAE3B,YAAa;MAACqB,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAClDpF,OAAA;QAAKmF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpF,OAAA;UAAAoF,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE1BxF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpF,OAAA;YAAO0F,OAAO,EAAC,WAAW;YAAAN,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CxF,OAAA;YACE0D,IAAI,EAAC,MAAM;YACXiC,EAAE,EAAC,WAAW;YACdzC,IAAI,EAAC,WAAW;YAChBL,KAAK,EAAExC,QAAQ,CAACE,SAAU;YAC1BqF,QAAQ,EAAE5C,iBAAkB;YAC5B6C,QAAQ;YACRC,WAAW,EAAC;UAAW;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpF,OAAA;YAAO0F,OAAO,EAAC,WAAW;YAAAN,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CxF,OAAA;YAAKmF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GACnC1D,WAAW,iBACV1B,OAAA;cAAKmF,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BpF,OAAA;gBAAK+F,GAAG,EAAErE,WAAY;gBAACsE,GAAG,EAAC;cAAW;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CACN,eACDxF,OAAA;cAAKmF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BpF,OAAA;gBAAO0F,OAAO,EAAC,WAAW;gBAACP,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAClDpF,OAAA,CAACH,QAAQ;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBACd;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxF,OAAA;gBACE0D,IAAI,EAAC,MAAM;gBACXiC,EAAE,EAAC,WAAW;gBACdM,MAAM,EAAC,SAAS;gBAChBL,QAAQ,EAAEvC,gBAAiB;gBAC3B6C,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAO;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxF,OAAA;QAAKmF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpF,OAAA;UAAAoF,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE5BxF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpF,OAAA;YAAO0F,OAAO,EAAC,eAAe;YAAAN,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpDxF,OAAA;YACE0D,IAAI,EAAC,OAAO;YACZiC,EAAE,EAAC,eAAe;YAClBzC,IAAI,EAAC,eAAe;YACpBL,KAAK,EAAExC,QAAQ,CAACI,aAAc;YAC9BmF,QAAQ,EAAE5C,iBAAkB;YAC5B6C,QAAQ;YACRC,WAAW,EAAC;UAAuB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpF,OAAA;YAAO0F,OAAO,EAAC,eAAe;YAAAN,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpDxF,OAAA;YACE0D,IAAI,EAAC,MAAM;YACXiC,EAAE,EAAC,eAAe;YAClBzC,IAAI,EAAC,eAAe;YACpBL,KAAK,EAAExC,QAAQ,CAACK,aAAc;YAC9BkF,QAAQ,EAAE5C,iBAAkB;YAC5B8C,WAAW,EAAC;UAAa;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxF,OAAA;QAAKmF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpF,OAAA;UAAAoF,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAErBxF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpF,OAAA;YAAO0F,OAAO,EAAC,cAAc;YAAAN,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClDxF,OAAA;YACE0D,IAAI,EAAC,KAAK;YACViC,EAAE,EAAC,cAAc;YACjBzC,IAAI,EAAC,cAAc;YACnBL,KAAK,EAAExC,QAAQ,CAACM,YAAa;YAC7BiF,QAAQ,EAAE5C,iBAAkB;YAC5B8C,WAAW,EAAC;UAAgC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpF,OAAA;YAAO0F,OAAO,EAAC,aAAa;YAAAN,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChDxF,OAAA;YACE0D,IAAI,EAAC,KAAK;YACViC,EAAE,EAAC,aAAa;YAChBzC,IAAI,EAAC,aAAa;YAClBL,KAAK,EAAExC,QAAQ,CAACO,WAAY;YAC5BgF,QAAQ,EAAE5C,iBAAkB;YAC5B8C,WAAW,EAAC;UAA+B;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpF,OAAA;YAAO0F,OAAO,EAAC,eAAe;YAAAN,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpDxF,OAAA;YACE0D,IAAI,EAAC,KAAK;YACViC,EAAE,EAAC,eAAe;YAClBzC,IAAI,EAAC,eAAe;YACpBL,KAAK,EAAExC,QAAQ,CAACQ,aAAc;YAC9B+E,QAAQ,EAAE5C,iBAAkB;YAC5B8C,WAAW,EAAC;UAAiC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxF,OAAA;QAAKmF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpF,OAAA;UAAAoF,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEhBxF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpF,OAAA;YAAO0F,OAAO,EAAC,YAAY;YAAAN,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9CxF,OAAA;YACE2F,EAAE,EAAC,YAAY;YACfzC,IAAI,EAAC,YAAY;YACjBL,KAAK,EAAExC,QAAQ,CAACS,UAAW;YAC3B8E,QAAQ,EAAE5C,iBAAkB;YAC5BoD,IAAI,EAAC,GAAG;YACRN,WAAW,EAAC;UAAoB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpF,OAAA;YAAO0F,OAAO,EAAC,kBAAkB;YAAAN,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5DxF,OAAA;YACE2F,EAAE,EAAC,kBAAkB;YACrBzC,IAAI,EAAC,kBAAkB;YACvBL,KAAK,EAAExC,QAAQ,CAACU,gBAAiB;YACjC6E,QAAQ,EAAE5C,iBAAkB;YAC5BoD,IAAI,EAAC,GAAG;YACRN,WAAW,EAAC;UAAwC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpF,OAAA;YAAO0F,OAAO,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtDxF,OAAA;YACE2F,EAAE,EAAC,gBAAgB;YACnBzC,IAAI,EAAC,gBAAgB;YACrBL,KAAK,EAAExC,QAAQ,CAACW,cAAe;YAC/B4E,QAAQ,EAAE5C,iBAAkB;YAC5BoD,IAAI,EAAC,GAAG;YACRN,WAAW,EAAC;UAAkC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpF,OAAA;YAAO0F,OAAO,EAAC,aAAa;YAAAN,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChDxF,OAAA;YACE0D,IAAI,EAAC,MAAM;YACXiC,EAAE,EAAC,aAAa;YAChBzC,IAAI,EAAC,aAAa;YAClBL,KAAK,EAAExC,QAAQ,CAACY,WAAY;YAC5B2E,QAAQ,EAAE5C,iBAAkB;YAC5B8C,WAAW,EAAC;UAAwC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxF,OAAA;QAAKmF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpF,OAAA;UAAAoF,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClBxF,OAAA;UAAAoF,QAAA,EAAG;QAA8E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrFxF,OAAA;UACE0D,IAAI,EAAC,QAAQ;UACb2C,OAAO,EAAE1B,kBAAmB;UAC5BQ,SAAS,EAAC,mBAAmB;UAC7BmB,QAAQ,EAAEpE,WAAY;UAAAkD,QAAA,EAErBlD,WAAW,GAAG,YAAY,GAAG;QAAsB;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENxF,OAAA;QAAKmF,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BpF,OAAA;UACE0D,IAAI,EAAC,QAAQ;UACbyB,SAAS,EAAC,iBAAiB;UAC3BmB,QAAQ,EAAElF,MAAO;UAAAgE,QAAA,EAEhBhE,MAAM,GAAG,WAAW,GAAG;QAAe;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACrF,EAAA,CAnYQD,eAAe;EAAA,QACMJ,aAAa;AAAA;AAAAyG,EAAA,GADlCrG,eAAe;AAmYvB;AAED,eAAeA,eAAe;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}